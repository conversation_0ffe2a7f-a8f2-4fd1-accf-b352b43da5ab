import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { promisify } from 'util';
import axios from 'axios';
import { ProcessManager } from './managers/processManager';
import { GithubManager } from './managers/githubManager';
import { DiagnosticsManager } from './managers/diagnosticsManager';
import { WebManager } from './managers/webManager';
import { FileChangePreview, FileChange } from './fileChangePreview';
import { StringReplaceEditor, EditOperation } from './editors/stringReplaceEditor';
import { EditPreview, EditPreviewData } from './editors/editPreview';
import { EditHistory } from './editors/editHistory';
import { BatchEditor } from './editors/batchEditor';
import { CodebaseRetrieval, CodeSearchResult, SearchQuery } from './retrieval/codebaseRetrieval';
import { SymbolAnalyzer, SymbolAnalysis } from './retrieval/symbolAnalyzer';
import { DependencyMapper, DependencyGraph, ImpactAnalysis } from './retrieval/dependencyMapper';
import { CrossReferenceTracker, CrossReference } from './retrieval/crossReferenceTracker';
import { RelationshipGraph } from './retrieval/relationshipGraph';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const exists = promisify(fs.exists);

export class AgentTools {
  private stringReplaceEditor: StringReplaceEditor;
  private editPreview: EditPreview;
  private editHistory: EditHistory;
  private batchEditor: BatchEditor;
  private codebaseRetrieval: CodebaseRetrieval;
  private symbolAnalyzer: SymbolAnalyzer;
  private dependencyMapper: DependencyMapper;
  private crossReferenceTracker: CrossReferenceTracker;
  private relationshipGraph: RelationshipGraph;

  constructor(
    private processManager: ProcessManager,
    private githubManager: GithubManager,
    private diagnosticsManager: DiagnosticsManager,
    private webManager: WebManager,
    private fileChangePreview: FileChangePreview,
    context: vscode.ExtensionContext
  ) {
    this.stringReplaceEditor = new StringReplaceEditor();
    this.editPreview = new EditPreview();
    this.editHistory = new EditHistory(context);
    this.batchEditor = new BatchEditor(this.stringReplaceEditor, this.editPreview, this.editHistory);
    this.codebaseRetrieval = new CodebaseRetrieval();
    this.symbolAnalyzer = new SymbolAnalyzer();
    this.dependencyMapper = new DependencyMapper();
    this.crossReferenceTracker = new CrossReferenceTracker();
    this.relationshipGraph = new RelationshipGraph();
  }

  // File System Tools

  async readFile(filePath: string): Promise<string> {
    try {
      // Resolve the path relative to the workspace
      const fullPath = this.resolveWorkspacePath(filePath);

      // Check if file exists
      if (!(await exists(fullPath))) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Read the file
      const content = await readFile(fullPath, 'utf8');
      return content;
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      throw error;
    }
  }

  async writeFile(filePath: string, content: string, preview: boolean = false): Promise<void> {
    try {
      // Resolve the path relative to the workspace
      const fullPath = this.resolveWorkspacePath(filePath);

      // Create directory if it doesn't exist
      const dir = path.dirname(fullPath);
      if (!(await exists(dir))) {
        await mkdir(dir, { recursive: true });
      }

      if (preview) {
        // Check if file exists
        if (await exists(fullPath)) {
          // Create a modify preview
          await this.fileChangePreview.createModifyPreview(
            filePath,
            content,
            `Modify file: ${filePath}`
          );
        } else {
          // Create a create preview
          await this.fileChangePreview.createCreatePreview(
            filePath,
            content,
            `Create file: ${filePath}`
          );
        }
      } else {
        // Write the file directly
        await writeFile(fullPath, content, 'utf8');
      }
    } catch (error) {
      console.error(`Error writing file ${filePath}:`, error);
      throw error;
    }
  }

  async listFiles(dirPath: string): Promise<string[]> {
    try {
      // Resolve the path relative to the workspace
      const fullPath = this.resolveWorkspacePath(dirPath);

      // Check if directory exists
      if (!(await exists(fullPath))) {
        throw new Error(`Directory not found: ${dirPath}`);
      }

      // List files
      const files = await fs.promises.readdir(fullPath);
      return files;
    } catch (error) {
      console.error(`Error listing files in ${dirPath}:`, error);
      throw error;
    }
  }

  // Code Editing Tools

  async editFile(filePath: string, edits: { range: vscode.Range; newText: string }[], preview: boolean = false): Promise<void> {
    try {
      // Resolve the path relative to the workspace
      const fullPath = this.resolveWorkspacePath(filePath);

      // Check if file exists
      if (!(await exists(fullPath))) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Open the document
      const document = await vscode.workspace.openTextDocument(fullPath);

      if (preview) {
        // Create a temporary edit to generate the new content
        const tempEdit = new vscode.WorkspaceEdit();

        for (const { range, newText } of edits) {
          tempEdit.replace(document.uri, range, newText);
        }

        // Apply the edit to a temporary document
        const originalContent = document.getText();
        let newContent = originalContent;

        // Apply edits manually to generate the new content
        // Sort edits in reverse order to avoid position changes
        const sortedEdits = [...edits].sort((a, b) => {
          if (a.range.start.line !== b.range.start.line) {
            return b.range.start.line - a.range.start.line;
          }
          return b.range.start.character - a.range.start.character;
        });

        const lines = originalContent.split('\n');

        for (const { range, newText } of sortedEdits) {
          // Get the affected lines
          const startLine = range.start.line;
          const endLine = range.end.line;

          // Get the text before and after the edit
          const startLineText = lines[startLine];
          const endLineText = lines[endLine];

          const textBefore = startLineText.substring(0, range.start.character);
          const textAfter = endLineText.substring(range.end.character);

          // Replace the affected lines
          const newLines = newText.split('\n');

          if (newLines.length === 1) {
            // Single line replacement
            lines[startLine] = textBefore + newLines[0] + textAfter;

            // Remove any lines in between if this was a multi-line range
            if (startLine !== endLine) {
              lines.splice(startLine + 1, endLine - startLine);
            }
          } else {
            // Multi-line replacement
            newLines[0] = textBefore + newLines[0];
            newLines[newLines.length - 1] += textAfter;

            // Replace the affected lines
            lines.splice(startLine, endLine - startLine + 1, ...newLines);
          }
        }

        newContent = lines.join('\n');

        // Create a modify preview
        await this.fileChangePreview.createModifyPreview(
          filePath,
          newContent,
          `Edit file: ${filePath}`
        );
      } else {
        // Apply edits directly
        const edit = new vscode.WorkspaceEdit();

        for (const { range, newText } of edits) {
          edit.replace(document.uri, range, newText);
        }

        // Apply the edit
        await vscode.workspace.applyEdit(edit);

        // Save the document
        await document.save();
      }
    } catch (error) {
      console.error(`Error editing file ${filePath}:`, error);
      throw error;
    }
  }

  async createFile(filePath: string, content: string, preview: boolean = false): Promise<void> {
    try {
      // Resolve the path relative to the workspace
      const fullPath = this.resolveWorkspacePath(filePath);

      // Check if file already exists
      if (await exists(fullPath)) {
        throw new Error(`File already exists: ${filePath}`);
      }

      // Create directory if it doesn't exist
      const dir = path.dirname(fullPath);
      if (!(await exists(dir))) {
        await mkdir(dir, { recursive: true });
      }

      if (preview) {
        // Create a create preview
        await this.fileChangePreview.createCreatePreview(
          filePath,
          content,
          `Create file: ${filePath}`
        );
      } else {
        // Create the file directly
        await writeFile(fullPath, content, 'utf8');
      }
    } catch (error) {
      console.error(`Error creating file ${filePath}:`, error);
      throw error;
    }
  }

  async deleteFile(filePath: string, preview: boolean = false): Promise<void> {
    try {
      // Resolve the path relative to the workspace
      const fullPath = this.resolveWorkspacePath(filePath);

      // Check if file exists
      if (!(await exists(fullPath))) {
        throw new Error(`File not found: ${filePath}`);
      }

      if (preview) {
        // Create a delete preview
        await this.fileChangePreview.createDeletePreview(
          filePath,
          `Delete file: ${filePath}`
        );
      } else {
        // Delete the file directly
        await fs.promises.unlink(fullPath);
      }
    } catch (error) {
      console.error(`Error deleting file ${filePath}:`, error);
      throw error;
    }
  }

  // Process Execution Tools

  async executeCommand(command: string, cwd?: string): Promise<{ stdout: string; stderr: string }> {
    return this.processManager.executeCommand(command, cwd);
  }

  async startProcess(command: string, cwd?: string): Promise<number> {
    return this.processManager.startProcess(command, cwd);
  }

  async stopProcess(processId: number): Promise<void> {
    return this.processManager.stopProcess(processId);
  }

  async getProcessOutput(processId: number): Promise<string> {
    return this.processManager.getProcessOutput(processId);
  }

  async writeToProcess(processId: number, input: string): Promise<void> {
    return this.processManager.writeToProcess(processId, input);
  }

  // GitHub Tools

  async getRepositoryInfo(): Promise<any> {
    return this.githubManager.getRepositoryInfo();
  }

  async createPullRequest(title: string, body: string, head: string, base: string): Promise<any> {
    return this.githubManager.createPullRequest(title, body, head, base);
  }

  async getIssues(state: string = 'open'): Promise<any[]> {
    return this.githubManager.getIssues(state);
  }

  async createIssue(title: string, body: string, labels: string[] = []): Promise<any> {
    return this.githubManager.createIssue(title, body, labels);
  }

  async getPullRequests(state: string = 'open'): Promise<any[]> {
    return this.githubManager.getPullRequests(state);
  }

  // Diagnostics Tools

  async getDiagnostics(filePath?: string): Promise<vscode.Diagnostic[]> {
    return this.diagnosticsManager.getDiagnostics(filePath);
  }

  async fixDiagnostic(filePath: string, diagnostic: vscode.Diagnostic): Promise<boolean> {
    return this.diagnosticsManager.fixDiagnostic(filePath, diagnostic);
  }

  // Web Tools

  async webSearch(query: string, numResults: number = 5): Promise<any[]> {
    try {
      return this.webManager.search(query, numResults);
    } catch (error) {
      console.error(`Error performing web search for "${query}":`, error);
      throw error;
    }
  }

  async fetchWebPage(url: string): Promise<string> {
    try {
      return this.webManager.fetchPage(url);
    } catch (error) {
      console.error(`Error fetching web page ${url}:`, error);
      throw error;
    }
  }

  // Advanced File Editing Tools

  /**
   * Perform string replacement edit with line number precision
   */
  async strReplaceEdit(
    filePath: string,
    startLine: number,
    endLine: number,
    newText: string,
    description: string = 'String replace edit'
  ): Promise<string> {
    try {
      // Create edit operation
      const operation = await this.stringReplaceEditor.replaceTextInRange(
        filePath,
        startLine,
        endLine,
        newText,
        true // validate
      );

      // Get before content for history
      const beforeContent = await this.getFileContent(filePath);

      // Apply the edit
      await this.stringReplaceEditor.applyEdit(operation);

      // Get after content for history
      const afterContent = await this.getFileContent(filePath);

      // Record in history
      const historyId = await this.editHistory.recordEdit(
        operation,
        description,
        beforeContent,
        afterContent
      );

      return historyId;
    } catch (error) {
      throw new Error(`String replace edit failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Insert code at specific line with smart indentation
   */
  async insertCode(
    filePath: string,
    lineNumber: number,
    code: string,
    matchIndentation: boolean = true,
    description: string = 'Code insertion'
  ): Promise<string> {
    try {
      // Create insert operation
      const operation = await this.stringReplaceEditor.insertCodeAtLine(
        filePath,
        lineNumber,
        code,
        matchIndentation
      );

      // Get before content for history
      const beforeContent = await this.getFileContent(filePath);

      // Apply the insert
      await this.stringReplaceEditor.applyInsert(operation);

      // Get after content for history
      const afterContent = await this.getFileContent(filePath);

      // Record in history
      const historyId = await this.editHistory.recordEdit(
        operation,
        description,
        beforeContent,
        afterContent
      );

      return historyId;
    } catch (error) {
      throw new Error(`Code insertion failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Perform batch edit operations
   */
  async batchEdit(
    operations: EditOperation[],
    description: string = 'Batch edit operation',
    atomic: boolean = true
  ): Promise<{ batchId: string; success: boolean; completedCount: number; failedCount: number }> {
    try {
      // Queue the batch operation
      const batchId = await this.batchEditor.queueBatchEdit(operations, description);

      // Execute the batch
      const result = atomic
        ? await this.batchEditor.executeBatchAtomic(batchId)
        : await this.batchEditor.executeBatchPartial(batchId);

      return {
        batchId: result.batchId,
        success: result.success,
        completedCount: result.completedOperations.length,
        failedCount: result.failedOperations.length
      };
    } catch (error) {
      throw new Error(`Batch edit failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Preview changes before applying them
   */
  async previewChanges(operations: EditOperation[]): Promise<EditPreviewData[]> {
    try {
      if (operations.length === 1) {
        const preview = await this.editPreview.createEditPreview(operations[0]);
        return [preview];
      } else {
        const batchPreview = await this.editPreview.createBatchEditPreview(operations);
        return batchPreview.edits;
      }
    } catch (error) {
      throw new Error(`Preview generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Undo last edit for a file
   */
  async undoEdit(filePath: string): Promise<boolean> {
    return await this.editHistory.undoLastEdit(filePath);
  }

  /**
   * Redo last undone edit for a file
   */
  async redoEdit(filePath: string): Promise<boolean> {
    return await this.editHistory.redoLastEdit(filePath);
  }

  /**
   * Get edit history for a file
   */
  getEditHistory(filePath: string) {
    return this.editHistory.getFileHistory(filePath);
  }

  /**
   * Check if undo/redo is available
   */
  getUndoRedoStatus(filePath: string): { canUndo: boolean; canRedo: boolean } {
    return {
      canUndo: this.editHistory.canUndo(filePath),
      canRedo: this.editHistory.canRedo(filePath)
    };
  }

  /**
   * Helper method to get file content
   */
  private async getFileContent(filePath: string): Promise<string> {
    try {
      const document = await vscode.workspace.openTextDocument(filePath);
      return document.getText();
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // File Change Preview Tools

  async showFileDiff(filePath: string): Promise<void> {
    return this.fileChangePreview.showDiff(filePath);
  }

  async applyFileChange(filePath: string): Promise<boolean> {
    return this.fileChangePreview.applyChange(filePath);
  }

  async applyAllFileChanges(): Promise<string[]> {
    return this.fileChangePreview.applyAllChanges();
  }

  async discardFileChange(filePath: string): Promise<void> {
    return this.fileChangePreview.removeChange(filePath);
  }

  async discardAllFileChanges(): Promise<void> {
    return this.fileChangePreview.clearChanges();
  }

  async getPendingFileChanges(): Promise<any[]> {
    return this.fileChangePreview.getChanges().map(change => ({
      filePath: change.filePath,
      description: change.description,
      type: change.type
    }));
  }

  async getFullFileChange(filePath: string): Promise<FileChange | undefined> {
    return this.fileChangePreview.getChange(filePath);
  }

  // Advanced Codebase Retrieval Tools

  /**
   * Search codebase with natural language query
   */
  async searchCodebase(query: string, options?: {
    fileTypes?: string[];
    maxResults?: number;
    includeTests?: boolean;
  }): Promise<CodeSearchResult[]> {
    try {
      const searchQuery: SearchQuery = {
        text: query,
        intent: 'general',
        fileTypes: options?.fileTypes,
        maxResults: options?.maxResults || 10,
        includeTests: options?.includeTests || false
      };

      return await this.codebaseRetrieval.searchCodebase(searchQuery);
    } catch (error) {
      throw new Error(`Codebase retrieval failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Find symbols by name or pattern
   */
  async findSymbols(pattern: string, type?: 'class' | 'function' | 'variable' | 'interface' | 'enum' | 'type'): Promise<any[]> {
    try {
      return await this.codebaseRetrieval.findSymbols(pattern, type);
    } catch (error) {
      throw new Error(`Symbol search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Index the codebase for better search performance
   */
  async indexCodebase(progressCallback?: (progress: number) => void): Promise<void> {
    try {
      await this.codebaseRetrieval.indexCodebase(progressCallback);
    } catch (error) {
      throw new Error(`Codebase indexing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Helper Methods

  private resolveWorkspacePath(relativePath: string): string {
    const workspaceFolders = vscode.workspace.workspaceFolders;

    if (!workspaceFolders) {
      throw new Error('No workspace folder is open');
    }

    // If the path is already absolute, return it
    if (path.isAbsolute(relativePath)) {
      return relativePath;
    }

    // Otherwise, resolve it relative to the first workspace folder
    return path.join(workspaceFolders[0].uri.fsPath, relativePath);
  }
}
