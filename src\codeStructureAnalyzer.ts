import * as vscode from "vscode";
import * as path from "path";
import * as fs from "fs";
import { promisify } from "util";
import * as ts from "typescript";
import { fileUtils } from "./utils/fileUtils";
import { EmbeddingUtils } from "./utils/embeddingUtils";
import * as vectorUtils from "./utils/vectorUtils";

const readFile = promisify(fs.readFile);

/**
 * Represents a code symbol with its details
 */
export interface CodeSymbol {
    name: string;
    kind: string;
    filePath: string;
    range: vscode.Range;
    parentName?: string;
    children?: CodeSymbol[];
    properties?: string[];
    methods?: string[];
    extends?: string[];
    implements?: string[];
    documentation?: string;
    signature?: string;
    returnType?: string;
    parameters?: Array<{ name: string; type: string }>;
}

/**
 * Represents a relationship between code symbols
 */
export interface CodeRelationship {
    sourceSymbol: string;
    targetSymbol: string;
    type: "extends" | "implements" | "imports" | "calls" | "references";
    sourceFile: string;
    targetFile: string;
}

/**
 * Analyzes code structure to provide better understanding of the codebase
 */
export class CodeStructureAnalyzer {
    private symbols: Map<string, CodeSymbol> = new Map();
    private relationships: CodeRelationship[] = [];
    private fileSymbols: Map<string, CodeSymbol[]> = new Map();
    private embeddingUtils: EmbeddingUtils;
    private symbolEmbeddings: Map<string, number[]> = new Map();

    constructor(private context: vscode.ExtensionContext) {
        this.embeddingUtils = new EmbeddingUtils();
    }

    /**
     * Analyze the workspace to extract code structure
     * @param progress Progress reporter
     */
    public async analyzeWorkspace(
        progress?: vscode.Progress<{ increment: number; message?: string }>
    ): Promise<void> {
        this.symbols.clear();
        this.relationships = [];
        this.fileSymbols.clear();
        this.symbolEmbeddings.clear();

        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            throw new Error("No workspace folder is open");
        }

        // Get all TypeScript and JavaScript files in the workspace
        const tsFiles = await vscode.workspace.findFiles(
            "**/*.{ts,js,tsx,jsx}",
            "**/node_modules/**"
        );

        if (progress) {
            progress.report({
                increment: 0,
                message: `Analyzing ${tsFiles.length} files...`
            });
        }

        // Process each file
        for (let i = 0; i < tsFiles.length; i++) {
            const file = tsFiles[i];
            await this.analyzeFile(file.fsPath);

            if (progress) {
                progress.report({
                    increment: (100 / tsFiles.length),
                    message: `Analyzed ${i + 1} of ${tsFiles.length} files`
                });
            }
        }

        // Build relationships between symbols
        await this.buildRelationships();

        // Generate embeddings for symbols
        await this.generateSymbolEmbeddings();
    }

    /**
     * Analyze a single file to extract code structure
     * @param filePath Path to the file
     */
    private async analyzeFile(filePath: string): Promise<void> {
        try {
            const content = await readFile(filePath, "utf8");
            const sourceFile = ts.createSourceFile(
                filePath,
                content,
                ts.ScriptTarget.Latest,
                true
            );

            const fileSymbols: CodeSymbol[] = [];
            this.visitNode(sourceFile, undefined, filePath, fileSymbols);
            this.fileSymbols.set(filePath, fileSymbols);
        } catch (error) {
            console.error(`Error analyzing file ${filePath}:`, error);
        }
    }

    /**
     * Visit a TypeScript AST node to extract symbols
     * @param node Node to visit
     * @param parent Parent node
     * @param filePath File path
     * @param fileSymbols Array to collect file symbols
     */
    private visitNode(
        node: ts.Node,
        parent: CodeSymbol | undefined,
        filePath: string,
        fileSymbols: CodeSymbol[]
    ): void {
        // Extract symbol information based on node kind
        if (ts.isClassDeclaration(node) && node.name) {
            const symbol = this.createSymbol(node, "class", filePath, parent);
            fileSymbols.push(symbol);
            this.symbols.set(symbol.name, symbol);

            // Extract heritage clauses (extends, implements)
            if (node.heritageClauses) {
                for (const clause of node.heritageClauses) {
                    if (clause.token === ts.SyntaxKind.ExtendsKeyword) {
                        symbol.extends = clause.types.map(t => t.expression.getText());
                    } else if (clause.token === ts.SyntaxKind.ImplementsKeyword) {
                        symbol.implements = clause.types.map(t => t.expression.getText());
                    }
                }
            }

            // Visit class members
            node.members.forEach(member => {
                this.visitNode(member, symbol, filePath, fileSymbols);
            });
        } else if (ts.isInterfaceDeclaration(node) && node.name) {
            const symbol = this.createSymbol(node, "interface", filePath, parent);
            fileSymbols.push(symbol);
            this.symbols.set(symbol.name, symbol);

            // Extract heritage clauses (extends)
            if (node.heritageClauses) {
                for (const clause of node.heritageClauses) {
                    if (clause.token === ts.SyntaxKind.ExtendsKeyword) {
                        symbol.extends = clause.types.map(t => t.expression.getText());
                    }
                }
            }

            // Visit interface members
            node.members.forEach(member => {
                this.visitNode(member, symbol, filePath, fileSymbols);
            });
        } else if (ts.isEnumDeclaration(node) && node.name) {
            const symbol = this.createSymbol(node, "enum", filePath, parent);
            fileSymbols.push(symbol);
            this.symbols.set(symbol.name, symbol);

            // Visit enum members
            node.members.forEach(member => {
                this.visitNode(member, symbol, filePath, fileSymbols);
            });
        } else if (ts.isMethodDeclaration(node) && parent) {
            const methodName = node.name.getText();
            if (!parent.methods) {
                parent.methods = [];
            }
            parent.methods.push(methodName);

            // Create method symbol
            const symbol = this.createSymbol(node, "method", filePath, parent);
            symbol.signature = node.getText().split("{")[0].trim();

            // Extract return type
            if (node.type) {
                symbol.returnType = node.type.getText();
            }

            // Extract parameters
            symbol.parameters = node.parameters.map(param => ({
                name: param.name.getText(),
                type: param.type ? param.type.getText() : "any"
            }));

            fileSymbols.push(symbol);
            this.symbols.set(`${parent.name}.${symbol.name}`, symbol);
        } else if (ts.isPropertyDeclaration(node) && parent) {
            const propertyName = node.name.getText();
            if (!parent.properties) {
                parent.properties = [];
            }
            parent.properties.push(propertyName);

            // Create property symbol
            const symbol = this.createSymbol(node, "property", filePath, parent);
            fileSymbols.push(symbol);
            this.symbols.set(`${parent.name}.${symbol.name}`, symbol);
        } else if (ts.isFunctionDeclaration(node) && node.name) {
            const symbol = this.createSymbol(node, "function", filePath, parent);
            symbol.signature = node.getText().split("{")[0].trim();

            // Extract return type
            if (node.type) {
                symbol.returnType = node.type.getText();
            }

            // Extract parameters
            symbol.parameters = node.parameters.map(param => ({
                name: param.name.getText(),
                type: param.type ? param.type.getText() : "any"
            }));

            fileSymbols.push(symbol);
            this.symbols.set(symbol.name, symbol);
        } else if (ts.isVariableStatement(node)) {
            // Handle variable declarations
            node.declarationList.declarations.forEach(declaration => {
                if (declaration.name) {
                    const symbol = this.createSymbol(declaration, "variable", filePath, parent);
                    fileSymbols.push(symbol);
                    this.symbols.set(symbol.name, symbol);
                }
            });
        } else if (ts.isImportDeclaration(node)) {
            // Handle imports (for relationship building)
            // We'll process these when building relationships
        }

        // Continue visiting child nodes
        ts.forEachChild(node, child => {
            if (child) {
                this.visitNode(child, parent, filePath, fileSymbols);
            }
        });
    }

    /**
     * Create a code symbol from a TypeScript node
     * @param node Node to create symbol from
     * @param kind Kind of symbol
     * @param filePath File path
     * @param parent Parent symbol
     * @returns Created code symbol
     */
    private createSymbol(
        node: ts.Node,
        kind: string,
        filePath: string,
        parent?: CodeSymbol
    ): CodeSymbol {
        const start = node.getStart();
        const end = node.getEnd();
        const sourceFile = node.getSourceFile();
        const startPos = sourceFile.getLineAndCharacterOfPosition(start);
        const endPos = sourceFile.getLineAndCharacterOfPosition(end);

        let name = "";
        if ("name" in node && node.name) {
            if (ts.isIdentifier(node.name as ts.Node)) {
                name = (node.name as ts.Identifier).getText();
            }
        } else if (ts.isVariableDeclaration(node) && ts.isIdentifier(node.name)) {
            name = node.name.getText();
        } else {
            name = node.getText().split(/[\s({]/)[0];
        }

        // Extract JSDoc comments if available
        let documentation = "";
        const nodePos = node.pos;
        const fullText = sourceFile.getFullText();
        const trivia = fullText.substring(nodePos - 100 < 0 ? 0 : nodePos - 100, nodePos);
        const jsdocRegex = /\/\*\*([\s\S]*?)\*\//;
        const match = trivia.match(jsdocRegex);
        if (match) {
            documentation = match[1].replace(/^\s*\*\s*/gm, "").trim();
        }

        return {
            name,
            kind,
            filePath,
            range: new vscode.Range(
                startPos.line,
                startPos.character,
                endPos.line,
                endPos.character
            ),
            parentName: parent ? parent.name : undefined,
            documentation,
            children: [],
            properties: [],
            methods: []
        };
    }

    /**
     * Build relationships between symbols
     */
    private async buildRelationships(): Promise<void> {
        // Process extends and implements relationships
        for (const [_, symbol] of this.symbols) {
            // Handle extends relationships
            if (symbol.extends) {
                for (const extendedClass of symbol.extends) {
                    const targetSymbol = this.findSymbolByName(extendedClass);
                    if (targetSymbol) {
                        this.relationships.push({
                            sourceSymbol: symbol.name,
                            targetSymbol: extendedClass,
                            type: "extends",
                            sourceFile: symbol.filePath,
                            targetFile: targetSymbol.filePath
                        });
                    }
                }
            }

            // Handle implements relationships
            if (symbol.implements) {
                for (const implementedInterface of symbol.implements) {
                    const targetSymbol = this.findSymbolByName(implementedInterface);
                    if (targetSymbol) {
                        this.relationships.push({
                            sourceSymbol: symbol.name,
                            targetSymbol: implementedInterface,
                            type: "implements",
                            sourceFile: symbol.filePath,
                            targetFile: targetSymbol.filePath
                        });
                    }
                }
            }
        }

        // Process imports and references (this would require more detailed analysis)
        // For a complete implementation, we would need to analyze imports and track references
        // This is a simplified version
    }

    /**
     * Find a symbol by name
     * @param name Symbol name
     * @returns Found symbol or undefined
     */
    private findSymbolByName(name: string): CodeSymbol | undefined {
        return this.symbols.get(name);
    }

    /**
     * Generate embeddings for symbols to enable semantic search
     */
    private async generateSymbolEmbeddings(): Promise<void> {
        const symbolTexts: string[] = [];
        const symbolKeys: string[] = [];

        for (const [key, symbol] of this.symbols) {
            // Create a rich text representation of the symbol
            let symbolText = `${symbol.kind} ${symbol.name}`;

            if (symbol.documentation) {
                symbolText += `\n${symbol.documentation}`;
            }

            if (symbol.signature) {
                symbolText += `\n${symbol.signature}`;
            }

            if (symbol.properties && symbol.properties.length > 0) {
                symbolText += `\nProperties: ${symbol.properties.join(", ")}`;
            }

            if (symbol.methods && symbol.methods.length > 0) {
                symbolText += `\nMethods: ${symbol.methods.join(", ")}`;
            }

            if (symbol.extends && symbol.extends.length > 0) {
                symbolText += `\nExtends: ${symbol.extends.join(", ")}`;
            }

            if (symbol.implements && symbol.implements.length > 0) {
                symbolText += `\nImplements: ${symbol.implements.join(", ")}`;
            }

            symbolTexts.push(symbolText);
            symbolKeys.push(key);
        }

        // Generate embeddings in batches to avoid memory issues
        const batchSize = 50;
        for (let i = 0; i < symbolTexts.length; i += batchSize) {
            const batch = symbolTexts.slice(i, i + batchSize);
            const batchKeys = symbolKeys.slice(i, i + batchSize);

            try {
                // Process each text individually since getEmbeddings doesn't exist
                const embeddings: number[][] = [];
                for (const text of batch) {
                    try {
                        const embedding = await this.embeddingUtils.getEmbedding(text);
                        embeddings.push(embedding);
                    } catch (error) {
                        console.error(`Error generating embedding for text: ${text.substring(0, 50)}...`, error);
                        // Push an empty embedding to maintain index alignment
                        embeddings.push([]);
                    }
                }

                for (let j = 0; j < embeddings.length; j++) {
                    this.symbolEmbeddings.set(batchKeys[j], embeddings[j]);
                }
            } catch (error) {
                console.error("Error generating embeddings for symbols:", error);
            }
        }
    }

    /**
     * Find symbols related to a query using semantic search
     * @param query Query to search for
     * @param maxResults Maximum number of results to return
     * @returns Array of relevant symbols
     */
    public async findRelatedSymbols(query: string, maxResults: number = 10): Promise<CodeSymbol[]> {
        try {
            // Get query embedding
            const queryEmbedding = await this.embeddingUtils.getEmbedding(query);

            // Calculate similarity with all symbol embeddings
            const similarities: Array<{ symbol: CodeSymbol; similarity: number }> = [];

            for (const [key, embedding] of this.symbolEmbeddings.entries()) {
                const symbol = this.symbols.get(key);
                if (symbol) {
                    // Use vectorUtils instead of embeddingUtils for cosineSimilarity
                    const similarity = vectorUtils.cosineSimilarity(queryEmbedding, embedding);
                    similarities.push({ symbol, similarity });
                }
            }

            // Sort by similarity (descending)
            similarities.sort((a, b) => b.similarity - a.similarity);

            // Return top results
            return similarities.slice(0, maxResults).map(item => item.symbol);
        } catch (error) {
            console.error("Error finding related symbols:", error);
            return [];
        }
    }

    /**
     * Get all symbols in the workspace
     * @returns Array of all symbols
     */
    public getAllSymbols(): CodeSymbol[] {
        return Array.from(this.symbols.values());
    }

    /**
     * Get symbols in a specific file
     * @param filePath File path
     * @returns Array of symbols in the file
     */
    public getFileSymbols(filePath: string): CodeSymbol[] {
        return this.fileSymbols.get(filePath) || [];
    }

    /**
     * Get all relationships between symbols
     * @returns Array of all relationships
     */
    public getRelationships(): CodeRelationship[] {
        return this.relationships;
    }

    /**
     * Get relationships for a specific symbol
     * @param symbolName Symbol name
     * @returns Array of relationships involving the symbol
     */
    public getSymbolRelationships(symbolName: string): CodeRelationship[] {
        return this.relationships.filter(
            rel => rel.sourceSymbol === symbolName || rel.targetSymbol === symbolName
        );
    }

    /**
     * Get a formatted representation of the code structure
     * @returns Formatted code structure
     */
    public getFormattedCodeStructure(): string {
        let result = "# Code Structure\n\n";

        // Group symbols by kind
        const symbolsByKind = new Map<string, CodeSymbol[]>();

        for (const symbol of this.symbols.values()) {
            if (!symbolsByKind.has(symbol.kind)) {
                symbolsByKind.set(symbol.kind, []);
            }
            symbolsByKind.get(symbol.kind)!.push(symbol);
        }

        // Format each kind of symbol
        for (const [kind, symbols] of symbolsByKind.entries()) {
            result += `## ${kind.charAt(0).toUpperCase() + kind.slice(1)}s\n\n`;

            for (const symbol of symbols) {
                result += `### ${symbol.name}\n\n`;

                if (symbol.documentation) {
                    result += `${symbol.documentation}\n\n`;
                }

                result += `File: ${vscode.workspace.asRelativePath(symbol.filePath)}\n\n`;

                if (symbol.signature) {
                    result += `\`\`\`typescript\n${symbol.signature}\n\`\`\`\n\n`;
                }

                if (symbol.extends && symbol.extends.length > 0) {
                    result += `Extends: ${symbol.extends.join(", ")}\n\n`;
                }

                if (symbol.implements && symbol.implements.length > 0) {
                    result += `Implements: ${symbol.implements.join(", ")}\n\n`;
                }

                if (symbol.properties && symbol.properties.length > 0) {
                    result += "Properties:\n\n";
                    for (const prop of symbol.properties) {
                        result += `- ${prop}\n`;
                    }
                    result += "\n";
                }

                if (symbol.methods && symbol.methods.length > 0) {
                    result += "Methods:\n\n";
                    for (const method of symbol.methods) {
                        result += `- ${method}\n`;
                    }
                    result += "\n";
                }
            }
        }

        return result;
    }
}