import * as vscode from 'vscode';
import { CodeSymbol, SymbolRelationship } from './symbolAnalyzer';

export interface GraphNode {
  id: string;
  symbol: CodeSymbol;
  type: 'class' | 'function' | 'interface' | 'variable' | 'module';
  metadata: {
    complexity: number;
    usageCount: number;
    lastModified: Date;
    isPublic: boolean;
  };
}

export interface GraphEdge {
  id: string;
  from: string;
  to: string;
  relationship: SymbolRelationship['type'];
  weight: number;
  metadata: {
    strength: number;
    frequency: number;
    isOptional: boolean;
  };
}

export interface RelationshipGraphData {
  nodes: Map<string, GraphNode>;
  edges: Map<string, GraphEdge>;
  clusters: Map<string, string[]>;
  metrics: GraphMetrics;
}

export interface GraphMetrics {
  totalNodes: number;
  totalEdges: number;
  density: number;
  averageDegree: number;
  clusteringCoefficient: number;
  centralityScores: Map<string, number>;
}

export interface GraphQuery {
  startNode?: string;
  endNode?: string;
  relationshipTypes?: SymbolRelationship['type'][];
  maxDepth?: number;
  includeTransitive?: boolean;
}

export interface GraphTraversal {
  path: string[];
  relationships: Array<SymbolRelationship['type']>;
  depth: number;
  totalWeight: number;
}

export class RelationshipGraph implements RelationshipGraphData {
  nodes: Map<string, GraphNode> = new Map();
  edges: Map<string, GraphEdge> = new Map();
  clusters: Map<string, string[]> = new Map();
  metrics: GraphMetrics = {
    totalNodes: 0,
    totalEdges: 0,
    density: 0,
    averageDegree: 0,
    clusteringCoefficient: 0,
    centralityScores: new Map()
  };

  private adjacencyList: Map<string, Set<string>> = new Map();
  private reverseAdjacencyList: Map<string, Set<string>> = new Map();

  constructor() {
    this.initializeGraph();
  }

  /**
   * Build relationship graph from symbols and their relationships
   */
  async buildGraph(
    symbols: Map<string, CodeSymbol[]>,
    relationships: SymbolRelationship[]
  ): Promise<RelationshipGraph> {
    // Reset graph
    this.initializeGraph();

    // Add all symbols as nodes
    for (const [_, symbolList] of symbols) {
      for (const symbol of symbolList) {
        this.addNode(symbol);
      }
    }

    // Add all relationships as edges
    for (const relationship of relationships) {
      this.addEdge(relationship);
    }

    // Calculate metrics
    this.calculateMetrics();
    this.calculateClusters();

    console.log(`Graph built: ${this.nodes.size} nodes, ${this.edges.size} edges`);
    return this;
  }

  /**
   * Find all paths between two symbols
   */
  findPaths(
    fromSymbol: CodeSymbol,
    toSymbol: CodeSymbol,
    maxDepth: number = 5
  ): GraphTraversal[] {
    if (!fromSymbol || !toSymbol) {
      console.warn('Invalid symbols provided to findPaths');
      return [];
    }

    if (maxDepth < 1 || maxDepth > 10) {
      console.warn('maxDepth should be between 1 and 10');
      maxDepth = Math.min(Math.max(maxDepth, 1), 10);
    }
    if (!fromSymbol || !toSymbol) {
      console.warn('Invalid symbols provided to findPaths');
      return [];
    }

    if (maxDepth < 1 || maxDepth > 10) {
      console.warn('maxDepth should be between 1 and 10');
      maxDepth = Math.min(Math.max(maxDepth, 1), 10);
    }
    if (!fromSymbol || !toSymbol) {
      return [];
    }

    const fromId = this.generateNodeId(fromSymbol);
    const toId = this.generateNodeId(toSymbol);

    if (!fromId || !toId) {
      return [];
    }

    const paths: GraphTraversal[] = [];
    const visited = new Set<string>();

    this.dfsTraversal(
      fromId,
      toId,
      [], // currentPath
      [], // currentRelationships
      0,  // currentWeight
      maxDepth,
      visited,
      paths
    );

    // Sort by depth (ascending) and then by weight (descending)
    return paths.sort((a, b) => {
      if (a.depth !== b.depth) {
        return a.depth - b.depth;
      }
      return b.totalWeight - a.totalWeight;
    });
  }

  /**
   * Find related nodes through DFS traversal
   */
  private findRelatedNodes(
    nodeId: string,
    relationshipTypes: Array<SymbolRelationship['type']> | undefined,
    maxDepth: number,
    currentDepth: number,
    visited: Set<string>,
    results: Set<string>
  ): void {
    // Validate input parameters
    if (!nodeId || !visited || !results) {
      return;
    }

    // Base case: stop if max depth reached or already visited
    if (currentDepth > maxDepth || visited.has(nodeId)) {
      return;
    }

    // Mark current node as visited
    visited.add(nodeId);
    results.add(nodeId);

    // Get neighbors from adjacency list
    const neighbors = this.adjacencyList.get(nodeId) || new Set<string>();

    // Visit each neighbor
    for (const neighbor of neighbors) {
      if (!neighbor) {
        continue;
      }

      // Check if we should traverse this edge based on relationship type
      let shouldTraverse = true;
      if (relationshipTypes && relationshipTypes.length > 0) {
        const edge = this.edges.get(`${nodeId}->${neighbor}`);
        shouldTraverse = edge ? relationshipTypes.includes(edge.relationship) : false;
      }

      if (shouldTraverse) {
        // Create a new visited set for this path to allow revisiting nodes in different paths
        const newVisited = new Set(visited);
        this.findRelatedNodes(
          neighbor,
          relationshipTypes,
          maxDepth,
          currentDepth + 1,
          newVisited,
          results
        );
      }
    }
  }

  /**
   * Find symbols related to a given symbol
   */
  findRelatedSymbols(
    symbol: CodeSymbol,
    relationshipTypes?: SymbolRelationship['type'][],
    maxDepth: number = 2
  ): CodeSymbol[] {
    const nodeId = this.generateNodeId(symbol);
    const related = new Set<string>();
    const visited = new Set<string>();

    this.findRelatedNodes(nodeId, relationshipTypes, maxDepth, 0, visited, related);

    const relatedSymbols: CodeSymbol[] = [];
    for (const relatedId of related) {
      const node = this.nodes.get(relatedId);
      if (node && relatedId !== nodeId) {
        relatedSymbols.push(node.symbol);
      }
    }

    return relatedSymbols;
  }

  /**
   * Analyze impact of changing a symbol
   */
  analyzeImpact(symbol: CodeSymbol): {
    directlyAffected: CodeSymbol[];
    transitivelyAffected: CodeSymbol[];
    impactScore: number;
    criticalPaths: GraphTraversal[];
  } {
    const nodeId = this.generateNodeId(symbol);
    const directlyAffected = new Set<string>();
    const transitivelyAffected = new Set<string>();

    // Find directly affected nodes
    if (this.adjacencyList.has(nodeId)) {
      for (const neighbor of this.adjacencyList.get(nodeId)!) {
        directlyAffected.add(neighbor);
      }
    }

    // Find transitively affected nodes
    for (const affectedNode of directlyAffected) {
      this.findTransitiveDependents(affectedNode, transitivelyAffected, new Set([nodeId]));
    }

    // Calculate impact score
    const impactScore = this.calculateImpactScore(nodeId, directlyAffected, transitivelyAffected);

    // Find critical paths
    const criticalPaths = this.findCriticalPaths(nodeId, 3);

    return {
      directlyAffected: Array.from(directlyAffected).map(id => this.nodes.get(id)!.symbol),
      transitivelyAffected: Array.from(transitivelyAffected).map(id => this.nodes.get(id)!.symbol),
      impactScore,
      criticalPaths
    };
  }



  /**
   * Calculate centrality scores for all nodes
   */
  calculateCentralityScores(): Map<string, number> {
    const centralityScores = new Map<string, number>();

    // Calculate betweenness centrality (simplified)
    for (const nodeId of this.nodes.keys()) {
      if (nodeId) {
        const score = this.calculateBetweennessCentrality(nodeId);
        centralityScores.set(nodeId, score);
      }
    }

    return centralityScores;
  }

  /**
   * Find architectural hotspots (highly connected nodes)
   */
  findArchitecturalHotspots(): Array<{
    symbol: CodeSymbol;
    inDegree: number;
    outDegree: number;
    totalDegree: number;
    centralityScore: number;
  }> {
    const hotspots: Array<{
      symbol: CodeSymbol;
      inDegree: number;
      outDegree: number;
      totalDegree: number;
      centralityScore: number;
    }> = [];

    for (const [nodeId, node] of this.nodes) {
      if (!nodeId || !node) continue;

      const inDegree = (this.reverseAdjacencyList.get(nodeId) || new Set()).size;
      const outDegree = (this.adjacencyList.get(nodeId) || new Set()).size;
      const totalDegree = inDegree + outDegree;
      const centralityScore = this.metrics.centralityScores.get(nodeId) || 0;

      if (totalDegree > 5 || centralityScore > 0.1) {
        hotspots.push({
          symbol: node.symbol,
          inDegree,
          outDegree,
          totalDegree,
          centralityScore
        });
      }
    }

    return hotspots.sort((a, b) => b.totalDegree - a.totalDegree);
  }

  /**
   * Query the graph with specific criteria
   */
  queryGraph(query: GraphQuery): CodeSymbol[] {
    const results: CodeSymbol[] = [];

    if (query.startNode && query.endNode) {
      // Find paths between specific nodes
      const startSymbol = this.findSymbolById(query.startNode);
      const endSymbol = this.findSymbolById(query.endNode);

      if (startSymbol && endSymbol) {
        const paths = this.findPaths(startSymbol, endSymbol, query.maxDepth || 5);
        for (const path of paths) {
          for (const nodeId of path.path) {
            const node = this.nodes.get(nodeId);
            if (node) {
              results.push(node.symbol);
            }
          }
        }
      }
    } else if (query.startNode) {
      // Find all nodes reachable from start node
      const startSymbol = this.findSymbolById(query.startNode);
      if (startSymbol) {
        const related = this.findRelatedSymbols(
          startSymbol,
          query.relationshipTypes,
          query.maxDepth || 2
        );
        results.push(...related);
      }
    }

    return results;
  }

  /**
   * Private helper methods
   */
  private initializeGraph(): void {
    this.nodes.clear();
    this.edges.clear();
    this.clusters.clear();
    this.adjacencyList.clear();
    this.reverseAdjacencyList.clear();
    this.metrics = {
      totalNodes: 0,
      totalEdges: 0,
      density: 0,
      averageDegree: 0,
      clusteringCoefficient: 0,
      centralityScores: new Map()
    };
  }

  private generateNodeId(symbol: CodeSymbol): string {
    if (!symbol?.filePath || !symbol.name) {
      throw new Error('Invalid symbol provided to generateNodeId');
    }
    const line = typeof symbol.line === 'number' ? `:${symbol.line}` : '';
    return `${symbol.name}@${symbol.filePath}${line}`;
  }

  private addNode(symbol: CodeSymbol): void {
    if (!symbol?.name || !symbol.filePath) {
      console.warn('Invalid symbol provided to addNode');
      return;
    }

    const nodeId = this.generateNodeId(symbol);

    if (!this.nodes.has(nodeId)) {
      // Determine the node type based on symbol properties
      let nodeType: GraphNode['type'] = 'variable'; // Default type
      const symbolAny = symbol as any;

      if (symbolAny.kind) {
        nodeType = String(symbolAny.kind).toLowerCase() as GraphNode['type'];
      } else if (symbolAny.type) {
        nodeType = String(symbolAny.type).toLowerCase() as GraphNode['type'];
      }

      // Ensure nodeType is one of the allowed values
      const validNodeTypes: GraphNode['type'][] = ['class', 'function', 'interface', 'variable', 'module'];
      if (!validNodeTypes.includes(nodeType)) {
        nodeType = 'variable';
      }

      const node: GraphNode = {
        id: nodeId,
        symbol,
        type: nodeType,
        metadata: {
          complexity: 1, // Default complexity
          usageCount: 0,
          lastModified: new Date(),
          isPublic: symbol.name[0] === symbol.name[0].toUpperCase()
        }
      };

      this.nodes.set(nodeId, node);
      this.adjacencyList.set(nodeId, new Set());
      this.reverseAdjacencyList.set(nodeId, new Set());
      this.metrics.totalNodes++;
    }
  }

  private addEdge(relationship: SymbolRelationship): void {
    const fromId = this.generateNodeId(relationship.fromSymbol);
    const toId = this.generateNodeId(relationship.toSymbol);

    // Skip if either node doesn't exist
    if (!fromId || !toId || !this.nodes.has(fromId) || !this.nodes.has(toId)) {
      console.warn(`Skipping edge: ${fromId} -> ${toId} - nodes not found`);
      return;
    }

    const edgeId = `${fromId}->${toId}`;
    const weight = this.calculateEdgeWeight(relationship);

    // Ensure relationship type is valid and properly typed
    const relationshipType: SymbolRelationship['type'] =
      this.isValidRelationshipType(relationship?.type)
        ? relationship.type as SymbolRelationship['type']
        : 'references'; // Default to 'references' if type is invalid or undefined

    // Create the edge object with proper typing
    const edge: GraphEdge = {
      id: edgeId,
      from: fromId,
      to: toId,
      relationship: relationshipType,
      weight,
      metadata: {
        strength: weight,
        frequency: 1,
        isOptional: relationshipType === 'references'
      }
    };

    // Initialize adjacency lists if they don't exist
    if (!this.adjacencyList.has(fromId)) {
      this.adjacencyList.set(fromId, new Set<string>());
    }
    if (!this.reverseAdjacencyList.has(toId)) {
      this.reverseAdjacencyList.set(toId, new Set<string>());
    }

    // Update adjacency lists with the new edge
    const fromNeighbors = this.adjacencyList.get(fromId);
    const toReverseNeighbors = this.reverseAdjacencyList.get(toId);

    if (fromNeighbors && toReverseNeighbors) {
      fromNeighbors.add(toId);
      toReverseNeighbors.add(fromId);

      // Add the edge to the edges map
      this.edges.set(edgeId, edge);
      this.metrics.totalEdges++;
    }
  }

  /**
   * Calculate the weight of a relationship based on its type
   */
  private calculateEdgeWeight(relationship: SymbolRelationship): number {
    // Ensure relationship and its type are defined and valid
    if (!relationship?.type || !this.isValidRelationshipType(relationship.type)) {
      return 0.1; // Default weight for unknown relationship types
    }

    const weightMap: Record<SymbolRelationship['type'], number> = {
      'extends': 1.0,
      'implements': 0.9,
      'calls': 0.7,
      'uses': 0.6,
      'imports': 0.5,
      'references': 0.3
    };

    return weightMap[relationship.type] ?? 0.1;
  }

  /**
   * Type guard to check if a value is a valid relationship type
   * @param type The value to check
   * @returns True if the value is a valid relationship type
   */
  private isValidRelationshipType(type: unknown): type is SymbolRelationship['type'] {
    if (typeof type !== 'string') return false;

    const validTypes = new Set<SymbolRelationship['type']>([
      'extends',
      'implements',
      'calls',
      'uses',
      'imports',
      'references'
    ]);

    return validTypes.has(type as SymbolRelationship['type']);
  }

  /**
   * Calculate metrics for the graph
   */
  private calculateMetrics(): void {
    try {
      const nodeCount = this.nodes.size;
      const edgeCount = this.edges.size;

      // Calculate density (for directed graph)
      const maxPossibleEdges = nodeCount * (nodeCount - 1);
      const density = nodeCount > 1 ? edgeCount / maxPossibleEdges : 0;

      // Calculate average degree
      let totalDegree = 0;
      for (const nodeId of this.nodes.keys()) {
        const outDegree = this.adjacencyList.get(nodeId)?.size || 0;
        const inDegree = this.reverseAdjacencyList.get(nodeId)?.size || 0;
        totalDegree += outDegree + inDegree;
      }

      const averageDegree = nodeCount > 0 ? totalDegree / nodeCount : 0;
      const clusteringCoefficient = this.calculateAverageClusteringCoefficient();
      const centralityScores = this.calculateCentralityScores();

      // Update metrics
      this.metrics = {
        totalNodes: nodeCount,
        totalEdges: edgeCount,
        density,
        averageDegree,
        clusteringCoefficient,
        centralityScores
      };
    } catch (error) {
      console.error('Error calculating metrics:', error);
    }
  }

  /**
   * Calculate the average clustering coefficient for the graph
   */
  private calculateAverageClusteringCoefficient(): number {
    let totalCoefficient = 0;
    let nodeCount = 0;

    for (const [nodeId, neighbors] of this.adjacencyList.entries()) {
      const neighborSet = new Set(neighbors);
      const neighborArray = Array.from(neighborSet);

      if (neighborArray.length < 2) {
        continue; // Skip nodes with fewer than 2 neighbors
      }

      let triangles = 0;
      const possibleTriangles = (neighborArray.length * (neighborArray.length - 1)) / 2;

      for (let i = 0; i < neighborArray.length; i++) {
        for (let j = i + 1; j < neighborArray.length; j++) {
          if (this.adjacencyList.get(neighborArray[i])?.has(neighborArray[j])) {
            triangles++;
          }
        }
      }

      totalCoefficient += possibleTriangles > 0 ? triangles / possibleTriangles : 0;
      nodeCount++;
    }

    return nodeCount > 0 ? totalCoefficient / nodeCount : 0;
  }

  /**
   * Calculate the betweenness centrality for a given node
   */
  private calculateBetweennessCentrality(nodeId: string): number {
    let centrality = 0;
    const allNodes = Array.from(this.nodes.keys());

    for (let i = 0; i < allNodes.length; i++) {
      for (let j = i + 1; j < allNodes.length; j++) {
        const source = allNodes[i];
        const target = allNodes[j];

        if (source !== nodeId && target !== nodeId) {
          const pathsThroughNode = this.countPathsThroughNode(source, target, nodeId);
          const totalPaths = this.countTotalPaths(source, target);

          if (totalPaths > 0) {
            centrality += pathsThroughNode / totalPaths;
          }
        }
      }
    }

    return centrality;
  }

  /**
   * Count the number of paths through a given node between two other nodes
   */
  private countPathsThroughNode(source: string, target: string, throughNode: string): number {
    if (source === target) return 0;
    if (!this.nodes.has(source) || !this.nodes.has(target) || !this.nodes.has(throughNode)) {
      return 0;
    }

    const queue: Array<{ node: string, path: string[] }> = [];
    const visited = new Set<string>();
    let pathCount = 0;

    queue.push({ node: source, path: [source] });

    while (queue.length > 0) {
      const current = queue.shift()!;

      if (current.node === target) {
        if (current.path.includes(throughNode)) {
          pathCount++;
        }
        continue;
      }

      if (visited.has(current.node)) {
        continue;
      }

      visited.add(current.node);

      const neighbors = this.adjacencyList.get(current.node);
      if (!neighbors) continue;

      for (const neighbor of neighbors) {
        if (!current.path.includes(neighbor)) {
          queue.push({
            node: neighbor,
            path: [...current.path, neighbor]
          });
        }
      }
    }

    return pathCount;
  }

  /**
   * Count all possible paths between source and target
   */
  private countTotalPaths(source: string, target: string): number {
    // Count all possible paths between source and target
    if (source === target) return 0;

    const visited = new Set<string>();
    let pathCount = 0;

    const dfs = (current: string) => {
      if (current === target) {
        pathCount++;
        return;
      }

      visited.add(current);
      const neighbors = this.adjacencyList.get(current) || new Set<string>();

      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          dfs(neighbor);
        }
      }

      visited.delete(current);
    };

    dfs(source);
    return pathCount;
  }

  /**
   * Calculate clusters of related nodes using strongly connected components
   */
  private calculateClusters(): void {
    this.clusters.clear();
    const components = this.findStronglyConnectedComponents();

    // Only keep components with 2 or more nodes (actual clusters)
    components.forEach((component, index) => {
      if (component.length > 1) {
        const clusterId = `cluster_${index}`;
        this.clusters.set(clusterId, component);
      }
    });
  }

  /**
   * Find transitive dependents of a node
   */
  private findTransitiveDependents(
    startNodeId: string,
    resultSet: Set<string> = new Set(),
    visited: Set<string> = new Set(),
    depth: number = 0,
    maxDepth: number = 10
  ): void {
    if (depth > maxDepth) return;

    visited.add(startNodeId);
    const neighbors = this.adjacencyList.get(startNodeId) || new Set<string>();

    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        resultSet.add(neighbor);
        this.findTransitiveDependents(neighbor, resultSet, visited, depth + 1, maxDepth);
      }
    }
  }

  /**
   * Calculate impact score for a node
   */
  private calculateImpactScore(
    nodeId: string,
    directDependents: Set<string> = new Set(),
    transitiveDependents: Set<string> = new Set()
  ): number {
    // Combine direct and transitive dependents
    const allDependents = new Set([...directDependents, ...transitiveDependents]);
    let score = 0;

    for (const dependentId of allDependents) {
      const node = this.nodes.get(dependentId);
      if (node) {
        // Simple scoring based on node type and complexity
        let nodeScore = 1;
        if (node.type === 'class' || node.type === 'interface') {
          nodeScore *= 2;
        }
        score += nodeScore * node.metadata.complexity;
      }
    }

    return score;
  }

  /**
   * Find critical paths in the graph
   */
  private findCriticalPaths(nodeId: string, maxDepth: number = 3): GraphTraversal[] {
    const paths: GraphTraversal[] = [];
    const visited = new Set<string>();

    const dfs = (currentPath: string[], relationships: SymbolRelationship['type'][], currentDepth: number, totalWeight: number) => {
      if (currentDepth > maxDepth) return;

      const currentNode = currentPath[currentPath.length - 1];
      visited.add(currentNode);

      const neighbors = this.adjacencyList.get(currentNode) || new Set<string>();
      if (neighbors.size === 0) {
        // Found a leaf node, add the path
        paths.push({
          path: [...currentPath],
          relationships: [...relationships],
          depth: currentDepth,
          totalWeight
        });
      } else {
        for (const neighbor of neighbors) {
          if (!visited.has(neighbor)) {
            const edge = this.findEdge(currentNode, neighbor);
            currentPath.push(neighbor);
            relationships.push(edge?.relationship || 'references');
            const edgeWeight = edge?.weight || 1;
            dfs(currentPath, relationships, currentDepth + 1, totalWeight + edgeWeight);
            currentPath.pop();
            relationships.pop();
          }
        }
      }

      visited.delete(currentNode);
    };

    dfs([nodeId], [], 0, 0);
    return paths;
  }

  /**
   * Helper method to find an edge between two nodes
   */
  private findEdge(from: string, to: string): GraphEdge | undefined {
    for (const edge of this.edges.values()) {
      if (edge.from === from && edge.to === to) {
        return edge;
      }
    }
    return undefined;
  }

  /**
   * Create transpose graph
   */
  private createTransposeGraph(): Map<string, Set<string>> {
    const transpose = new Map<string, Set<string>>();

    // Initialize all nodes in transpose graph
    for (const nodeId of this.adjacencyList.keys()) {
      transpose.set(nodeId, new Set<string>());
    }

    // Add reversed edges
    for (const [from, neighbors] of this.adjacencyList.entries()) {
      for (const to of neighbors) {
        transpose.get(to)?.add(from);
      }
    }

    return transpose;
  }

  private dfsForStack(nodeId: string, visited: Set<string>, stack: string[]): void {
    visited.add(nodeId);
    const neighbors = this.adjacencyList.get(nodeId) || new Set<string>();

    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        this.dfsForStack(neighbor, visited, stack);
      }
    }

    stack.push(nodeId);
  }

  private dfsOnTranspose(nodeId: string, visited: Set<string>, component: string[], transpose: Map<string, Set<string>>): void {
    visited.add(nodeId);
    component.push(nodeId);

    const neighbors = transpose.get(nodeId) || new Set<string>();
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        this.dfsOnTranspose(neighbor, visited, component, transpose);
      }
    }
  }

  private dfsTraversal(
    currentNode: string,
    targetNode: string,
    currentPath: string[],
    currentRelationships: Array<SymbolRelationship['type']>,
    currentWeight: number,
    maxDepth: number,
    visited: Set<string>,
    results: GraphTraversal[]
  ): void {
    if (currentPath.length > maxDepth) return;

    currentPath.push(currentNode);

    if (currentNode === targetNode && currentPath.length > 1) {
      results.push({
        path: [...currentPath],
        relationships: [...currentRelationships],
        depth: currentPath.length - 1,
        totalWeight: currentWeight
      });
      currentPath.pop();
      return;
    }

    visited.add(currentNode);

    const neighbors = this.adjacencyList.get(currentNode) || new Set<string>();
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor) || neighbor === targetNode) {
        const edge = this.edges.get(`${currentNode}->${neighbor}`);
        if (edge) {
          currentRelationships.push(edge.relationship);
          this.dfsTraversal(
            neighbor,
            targetNode,
            currentPath,
            currentRelationships,
            currentWeight + (edge.weight || 1),
            maxDepth,
            new Set(visited),
            results
          );
          currentRelationships.pop();
        }
      }
    }

    currentPath.pop();
  }

  /**
   * Find a symbol by its ID
   * @param symbolId The ID of the symbol to find
   * @returns The found symbol or undefined if not found
   */
  private findSymbolById(symbolId: string): CodeSymbol | undefined {
    for (const node of this.nodes.values()) {
      if (node && node.symbol && this.generateNodeId(node.symbol) === symbolId) {
        return node.symbol;
      }
    }
    return undefined;
  }

  /**
   * Find strongly connected components using Kosaraju's algorithm
   * @returns Array of strongly connected components (each is an array of node IDs)
   */
  private findStronglyConnectedComponents(): string[][] {
    const components: string[][] = [];
    const visited = new Set<string>();
    const stack: string[] = [];

    // First pass: Fill the stack with nodes in order of finishing times
    for (const nodeId of this.adjacencyList.keys()) {
      if (!visited.has(nodeId)) {
        this.dfsForStack(nodeId, visited, stack);
      }
    }

    // Create transpose graph
    const transpose = this.createTransposeGraph();

    // Second pass: Process nodes in reverse order of finishing times
    visited.clear();
    while (stack.length > 0) {
      const nodeId = stack.pop()!;
      if (nodeId && !visited.has(nodeId)) {
        const component: string[] = [];
        this.dfsOnTranspose(nodeId, visited, component, transpose);
        if (component.length > 1) { // Only include components with 2+ nodes
          components.push(component);
        }
      }
    }

    return components;
  }
}
