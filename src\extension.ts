import * as vscode from 'vscode';
import { AIChatViewProvider } from './AIChatViewProvider';
import { MemoryManager } from './managers/memoryManager';
import { ProcessManager } from './managers/processManager';
import { GithubManager } from './managers/githubManager';
import { DiagnosticsManager } from './managers/diagnosticsManager';
import { WebManager } from './managers/webManager';
import { CodeIndexer } from './codeIndexer';
import { CodeStructureAnalyzer } from './codeStructureAnalyzer';
import { FileChangePreview } from './fileChangePreview';
import { AgentTools } from './agentTools';
import { TaskManager } from './tasks/taskManager';
import { TaskScheduler } from './tasks/taskScheduler';
import { TaskPanel } from './ui/taskPanel';
import { EditPreviewPanel } from './ui/editPreviewPanel';

import { MemoryPanelProvider } from './ui/memoryPanelProvider';
import { ProcessPanelProvider } from './ui/processPanelProvider';
import { GithubPanelProvider } from './ui/githubPanelProvider';
import { DiagnosticsPanelProvider } from './ui/diagnosticsPanelProvider';
import { WebPanelProvider } from './ui/webPanelProvider';
import { FileChangePanelProvider } from './ui/fileChangePanelProvider';
import { ModeManager } from './managers/modeManager';

export async function activate(context: vscode.ExtensionContext) {
  console.log('Vidyadhara is now active!');

  // Initialize managers
  const memoryManager = new MemoryManager(context);
  const processManager = new ProcessManager();
  const githubManager = new GithubManager();
  const diagnosticsManager = new DiagnosticsManager();
  const webManager = new WebManager();
  const codeIndexer = new CodeIndexer(context);
  const codeStructureAnalyzer = new CodeStructureAnalyzer(context);
  const fileChangePreview = new FileChangePreview();
  const modeManager = new ModeManager();

  // Initialize task management
  const taskManager = new TaskManager(context);
  const taskScheduler = new TaskScheduler();

  // Initialize agent tools
  const agentTools = new AgentTools(
    processManager,
    githubManager,
    diagnosticsManager,
    webManager,
    fileChangePreview,
    context
  );

  // Initialize UI providers
  const chatViewProvider = new AIChatViewProvider(
    context,
    memoryManager,
    processManager,
    githubManager,
    diagnosticsManager,
    webManager,
    codeIndexer,
    codeStructureAnalyzer
  );

  const memoryPanelProvider = new MemoryPanelProvider(context, memoryManager);
  const processPanelProvider = new ProcessPanelProvider(context, processManager);
  const githubPanelProvider = new GithubPanelProvider(context, githubManager);
  const diagnosticsPanelProvider = new DiagnosticsPanelProvider(context, diagnosticsManager);
  const webPanelProvider = new WebPanelProvider(context, webManager);
  const fileChangePanelProvider = new FileChangePanelProvider(context, fileChangePreview);

  // Register WebView providers
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(
      'vidyadhara.chatView',
      chatViewProvider,
      { webviewOptions: { retainContextWhenHidden: true } }
    ),
    vscode.window.registerWebviewViewProvider(
      'vidyadhara.memoryView',
      memoryPanelProvider
    ),
    vscode.window.registerWebviewViewProvider(
      'vidyadhara.processView',
      processPanelProvider
    ),
    vscode.window.registerWebviewViewProvider(
      'vidyadhara.githubView',
      githubPanelProvider
    ),
    vscode.window.registerWebviewViewProvider(
      'vidyadhara.webView',
      webPanelProvider
    ),
    vscode.window.registerWebviewViewProvider(
      'vidyadhara.fileChangeView',
      fileChangePanelProvider
    )
  );

  // Register commands
  context.subscriptions.push(
    vscode.commands.registerCommand('vidyadhara.start', () => {
      vscode.commands.executeCommand('vidyadhara.chatView.focus');
    }),
    vscode.commands.registerCommand('vidyadhara.clearMemory', async () => {
      await memoryManager.clearMemory();
      vscode.window.showInformationMessage('Vidyadhara memory cleared');
    }),
    vscode.commands.registerCommand('vidyadhara.indexCodebase', async () => {
      vscode.window.withProgress(
        {
          location: vscode.ProgressLocation.Notification,
          title: 'Indexing codebase...',
          cancellable: true,
        },
        async (progress, token) => {
          token.onCancellationRequested(() => {
            console.log('User canceled the indexing');
          });

          progress.report({ increment: 0 });

          try {
            // Step 1: Index the codebase for content search
            progress.report({ increment: 0, message: 'Indexing codebase content...' });
            await codeIndexer.indexWorkspace(progress);

            // Step 2: Analyze code structure
            progress.report({ increment: 50, message: 'Analyzing code structure...' });
            await codeStructureAnalyzer.analyzeWorkspace(progress);

            vscode.window.showInformationMessage('Codebase indexing and structure analysis complete');
          } catch (error) {
            vscode.window.showErrorMessage(`Indexing failed: ${error}`);
          }
        }
      );
    }),
    // Register mode switching command
    vscode.commands.registerCommand('vidyadhara.setChatMode', async (mode: string) => {
      const validModes = ['chat', 'agent', 'agent-auto'];
      if (!validModes.includes(mode)) {
        vscode.window.showErrorMessage(`Invalid mode: ${mode}. Valid modes are: ${validModes.join(', ')}`);
        return;
      }

      await vscode.workspace.getConfiguration('vidyadhara').update('chatMode', mode, true);
      vscode.window.showInformationMessage(`Chat mode switched to: ${mode}`);

      // Update the webview if it exists
      if (chatViewProvider.webviewView) {
        chatViewProvider.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: mode
        });
      }
    })
  );

  // Register task management commands
  context.subscriptions.push(
    vscode.commands.registerCommand('vidyadhara.openTaskPanel', () => {
      TaskPanel.createOrShow(context.extensionUri, taskManager, taskScheduler);
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('vidyadhara.createTask', async () => {
      const name = await vscode.window.showInputBox({
        prompt: 'Enter task name',
        placeHolder: 'Task name'
      });

      if (name) {
        const description = await vscode.window.showInputBox({
          prompt: 'Enter task description (optional)',
          placeHolder: 'Task description'
        });

        await taskManager.createTask({
          name,
          description: description || ''
        });

        vscode.window.showInformationMessage(`Task "${name}" created successfully`);
      }
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('vidyadhara.showEditPreview', (preview: any) => {
      EditPreviewPanel.createOrShow(context.extensionUri, preview);
    })
  );

  // Automatically index the codebase on startup
  vscode.commands.executeCommand('vidyadhara.indexCodebase');
}

export function deactivate() {
  console.log('Vidyadhara is now deactivated');
}
