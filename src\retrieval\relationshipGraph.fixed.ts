import * as vscode from 'vscode';
import { CodeSymbol, SymbolRelationship } from './symbolAnalyzer';

export interface GraphNode {
  id: string;
  symbol: CodeSymbol;
  type: 'class' | 'function' | 'interface' | 'variable' | 'module';
  metadata: {
    complexity: number;
    usageCount: number;
    lastModified: Date;
    isPublic: boolean;
  };
}

export interface GraphEdge {
  id: string;
  from: string;
  to: string;
  relationship: SymbolRelationship['type'];
  weight: number;
  metadata: {
    strength: number;
    frequency: number;
    isOptional: boolean;
  };
}

export interface RelationshipGraphData {
  nodes: Map<string, GraphNode>;
  edges: Map<string, GraphEdge>;
  clusters: Map<string, string[]>;
  metrics: GraphMetrics;
}

export interface GraphMetrics {
  totalNodes: number;
  totalEdges: number;
  density: number;
  averageDegree: number;
  clusteringCoefficient: number;
  centralityScores: Map<string, number>;
}

export interface GraphQuery {
  startNode?: string;
  endNode?: string;
  relationshipTypes?: SymbolRelationship['type'][];
  maxDepth?: number;
  includeTransitive?: boolean;
}

export interface GraphTraversal {
  path: string[];
  relationships: SymbolRelationship['type'][];
  totalWeight: number;
  depth: number;
}

export class RelationshipGraph implements RelationshipGraphData {
  nodes: Map<string, GraphNode> = new Map();
  edges: Map<string, GraphEdge> = new Map();
  clusters: Map<string, string[]> = new Map();
  metrics: GraphMetrics = {
    totalNodes: 0,
    totalEdges: 0,
    density: 0,
    averageDegree: 0,
    clusteringCoefficient: 0,
    centralityScores: new Map()
  };
  
  private adjacencyList: Map<string, Set<string>> = new Map();
  private reverseAdjacencyList: Map<string, Set<string>> = new Map();

  constructor() {
    this.initializeGraph();
  }

  /**
   * Initialize the graph
   */
  private initializeGraph(): void {
    this.nodes.clear();
    this.edges.clear();
    this.clusters.clear();
    this.adjacencyList.clear();
    this.reverseAdjacencyList.clear();
    
    this.metrics = {
      totalNodes: 0,
      totalEdges: 0,
      density: 0,
      averageDegree: 0,
      clusteringCoefficient: 0,
      centralityScores: new Map()
    };
  }

  /**
   * Find strongly connected components (circular dependencies) using Kosaraju's algorithm
   * @returns Array of strongly connected components, where each component is an array of node IDs
   */
  findStronglyConnectedComponents(): string[][] {
    const components: string[][] = [];
    const visited = new Set<string>();
    const stack: string[] = [];
    
    // First pass: fill the stack with nodes in order of finishing times
    for (const nodeId of this.nodes.keys()) {
      if (nodeId && !visited.has(nodeId)) {
        this.dfsForStack(nodeId, visited, stack);
      }
    }
    
    // Create transpose graph (graph with reversed edges)
    const transpose = this.createTransposeGraph();
    
    // Second pass: process nodes in reverse order of finishing times
    visited.clear();
    
    while (stack.length > 0) {
      const nodeId = stack.pop();
      if (nodeId && !visited.has(nodeId)) {
        const component: string[] = [];
        this.dfsOnTranspose(nodeId, visited, component, transpose);
        // Only include components with 2 or more nodes (actual circular dependencies)
        if (component.length > 1) {
          components.push(component);
        }
      }
    }
    
    return components;
  }

  /**
   * Helper method for first DFS pass in Kosaraju's algorithm
   */
  private dfsForStack(nodeId: string, visited: Set<string>, stack: string[]): void {
    if (!nodeId || visited.has(nodeId)) {
      return;
    }
    
    visited.add(nodeId);
    const neighbors = this.adjacencyList.get(nodeId);
    
    if (neighbors) {
      for (const neighbor of neighbors) {
        if (neighbor && !visited.has(neighbor)) {
          this.dfsForStack(neighbor, visited, stack);
        }
      }
    }
    
    // Push node to stack after all its descendants
    stack.push(nodeId);
  }

  /**
   * Create a transpose of the graph (all edges reversed)
   */
  private createTransposeGraph(): Map<string, Set<string>> {
    const transpose = new Map<string, Set<string>>();
    
    // Initialize transpose with all nodes
    for (const nodeId of this.nodes.keys()) {
      if (nodeId) {
        transpose.set(nodeId, new Set<string>());
      }
    }
    
    // Add reversed edges
    for (const [from, neighbors] of this.adjacencyList.entries()) {
      if (!from || !neighbors) continue;
      
      for (const to of neighbors) {
        if (!to) continue;
        
        const toEdges = transpose.get(to) || new Set<string>();
        toEdges.add(from);
        transpose.set(to, toEdges);
      }
    }
    
    return transpose;
  }

  /**
   * Helper method for second DFS pass in Kosaraju's algorithm
   */
  private dfsOnTranspose(
    nodeId: string,
    visited: Set<string>,
    component: string[],
    transposeGraph: Map<string, Set<string>>
  ): void {
    // Validate input parameters
    if (!nodeId || !visited || !component || !transposeGraph) {
      return;
    }
    
    // Skip if already visited
    if (visited.has(nodeId)) {
      return;
    }
    
    // Mark as visited and add to current component
    visited.add(nodeId);
    component.push(nodeId);
    
    // Get neighbors from transpose graph
    const neighbors = transposeGraph.get(nodeId);
    if (!neighbors) {
      return;
    }
    
    // Visit all neighbors in the transpose graph
    for (const neighbor of neighbors) {
      if (neighbor && !visited.has(neighbor)) {
        this.dfsOnTranspose(neighbor, visited, component, transposeGraph);
      }
    }
  }

  /**
   * Generate a unique ID for a symbol
   */
  private generateNodeId(symbol: CodeSymbol): string {
    if (!symbol || !symbol.filePath || !symbol.name || typeof symbol.line !== 'number') {
      throw new Error('Invalid symbol provided to generateNodeId');
    }
    return `${symbol.filePath}:${symbol.name}:${symbol.line}`;
  }
}
