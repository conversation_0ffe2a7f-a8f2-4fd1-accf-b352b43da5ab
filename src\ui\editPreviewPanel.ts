import * as vscode from 'vscode';
import * as path from 'path';
import { EditPreviewData, BatchEditPreview } from '../editors/editPreview';
import { EditOperation } from '../editors/stringReplaceEditor';

export class EditPreviewPanel {
  public static currentPanel: EditPreviewPanel | undefined;
  private readonly _panel: vscode.WebviewPanel;
  private readonly _extensionUri: vscode.Uri;
  private _disposables: vscode.Disposable[] = [];
  private _currentPreview: EditPreviewData | BatchEditPreview | undefined;

  public static createOrShow(extensionUri: vscode.Uri, preview: EditPreviewData | BatchEditPreview) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // If we already have a panel, show it
    if (EditPreviewPanel.currentPanel) {
      EditPreviewPanel.currentPanel._panel.reveal(column);
      EditPreviewPanel.currentPanel.updatePreview(preview);
      return;
    }

    // Otherwise, create a new panel
    const panel = vscode.window.createWebviewPanel(
      'editPreview',
      'Edit Preview',
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.joinPath(extensionUri, 'media'),
          vscode.Uri.joinPath(extensionUri, 'out', 'media')
        ]
      }
    );

    EditPreviewPanel.currentPanel = new EditPreviewPanel(panel, extensionUri, preview);
  }

  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, preview: EditPreviewData | BatchEditPreview) {
    this._panel = panel;
    this._extensionUri = extensionUri;
    this._currentPreview = preview;

    // Set the webview's initial html content
    this._update();

    // Listen for when the panel is disposed
    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

    // Handle messages from the webview
    this._panel.webview.onDidReceiveMessage(
      message => {
        switch (message.command) {
          case 'acceptEdit':
            this._handleAcceptEdit(message.editIndex);
            return;
          case 'rejectEdit':
            this._handleRejectEdit(message.editIndex);
            return;
          case 'acceptAll':
            this._handleAcceptAll();
            return;
          case 'rejectAll':
            this._handleRejectAll();
            return;
          case 'showFile':
            this._handleShowFile(message.filePath, message.lineNumber);
            return;
        }
      },
      null,
      this._disposables
    );
  }

  public updatePreview(preview: EditPreviewData | BatchEditPreview) {
    this._currentPreview = preview;
    this._update();
  }

  public dispose() {
    EditPreviewPanel.currentPanel = undefined;

    // Clean up our resources
    this._panel.dispose();

    while (this._disposables.length) {
      const x = this._disposables.pop();
      if (x) {
        x.dispose();
      }
    }
  }

  private _update() {
    const webview = this._panel.webview;
    this._panel.title = 'Edit Preview';
    this._panel.webview.html = this._getHtmlForWebview(webview);
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    // Get the local path to main script and CSS
    const scriptPathOnDisk = vscode.Uri.joinPath(this._extensionUri, 'media', 'editPreview.js');
    const stylePathOnDisk = vscode.Uri.joinPath(this._extensionUri, 'media', 'editPreview.css');

    // And the uri we use to load this script and CSS in the webview
    const scriptUri = webview.asWebviewUri(scriptPathOnDisk);
    const styleUri = webview.asWebviewUri(stylePathOnDisk);

    // Use a nonce to only allow specific scripts to be run
    const nonce = getNonce();

    const previewHtml = this._generatePreviewHtml();

    return `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="${styleUri}" rel="stylesheet">
        <title>Edit Preview</title>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Edit Preview</h1>
            <div class="actions">
              <button id="acceptAll" class="btn btn-primary">Accept All</button>
              <button id="rejectAll" class="btn btn-secondary">Reject All</button>
            </div>
          </div>
          <div class="content">
            ${previewHtml}
          </div>
        </div>
        <script nonce="${nonce}" src="${scriptUri}"></script>
      </body>
      </html>`;
  }

  private _generatePreviewHtml(): string {
    if (!this._currentPreview) {
      return '<p>No preview data available</p>';
    }

    if ('edits' in this._currentPreview) {
      // Batch preview
      return this._generateBatchPreviewHtml(this._currentPreview);
    } else {
      // Single edit preview
      return this._generateSinglePreviewHtml(this._currentPreview);
    }
  }

  private _generateSinglePreviewHtml(preview: EditPreviewData): string {
    const stats = preview.changeStats;
    
    return `
      <div class="edit-preview" data-file-path="${preview.filePath}">
        <div class="file-header">
          <h2>${preview.filePath}</h2>
          <div class="change-stats">
            <span class="additions">+${stats.additions}</span>
            <span class="deletions">-${stats.deletions}</span>
            <span class="modifications">~${stats.modifications}</span>
          </div>
          <div class="file-actions">
            <button class="btn btn-sm btn-primary accept-edit" data-edit-index="0">Accept</button>
            <button class="btn btn-sm btn-secondary reject-edit" data-edit-index="0">Reject</button>
          </div>
        </div>
        <div class="diff-container">
          ${this._generateDiffHtml(preview.diffLines)}
        </div>
      </div>
    `;
  }

  private _generateBatchPreviewHtml(batchPreview: BatchEditPreview): string {
    const totalStats = batchPreview.totalStats;
    
    let html = `
      <div class="batch-summary">
        <h2>Batch Edit Summary</h2>
        <div class="total-stats">
          <span class="files-changed">${totalStats.filesChanged} files</span>
          <span class="additions">+${totalStats.totalAdditions}</span>
          <span class="deletions">-${totalStats.totalDeletions}</span>
          <span class="modifications">~${totalStats.totalModifications}</span>
        </div>
      </div>
    `;

    batchPreview.edits.forEach((edit, index) => {
      html += `
        <div class="edit-preview" data-file-path="${edit.filePath}">
          <div class="file-header">
            <h3>${edit.filePath}</h3>
            <div class="change-stats">
              <span class="additions">+${edit.changeStats.additions}</span>
              <span class="deletions">-${edit.changeStats.deletions}</span>
              <span class="modifications">~${edit.changeStats.modifications}</span>
            </div>
            <div class="file-actions">
              <button class="btn btn-sm btn-primary accept-edit" data-edit-index="${index}">Accept</button>
              <button class="btn btn-sm btn-secondary reject-edit" data-edit-index="${index}">Reject</button>
              <button class="btn btn-sm btn-link show-file" data-file-path="${edit.filePath}">Show File</button>
            </div>
          </div>
          <div class="diff-container">
            ${this._generateDiffHtml(edit.diffLines)}
          </div>
        </div>
      `;
    });

    return html;
  }

  private _generateDiffHtml(diffLines: any[]): string {
    return diffLines.map(line => {
      const cssClass = `diff-line diff-${line.type}`;
      const lineNumber = line.lineNumber > 0 ? line.lineNumber.toString().padStart(4) : '    ';
      const prefix = this._getDiffPrefix(line.type);
      
      return `
        <div class="${cssClass}" data-line-number="${line.lineNumber}">
          <span class="line-number">${lineNumber}</span>
          <span class="diff-prefix">${prefix}</span>
          <span class="line-content">${this._escapeHtml(line.content)}</span>
        </div>
      `;
    }).join('');
  }

  private _getDiffPrefix(type: string): string {
    switch (type) {
      case 'added': return '+';
      case 'removed': return '-';
      case 'modified': return '~';
      case 'unchanged': return ' ';
      default: return ' ';
    }
  }

  private _escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  private async _handleAcceptEdit(editIndex: number) {
    // Implementation would apply the specific edit
    vscode.window.showInformationMessage(`Accepting edit ${editIndex}`);
  }

  private async _handleRejectEdit(editIndex: number) {
    // Implementation would reject the specific edit
    vscode.window.showInformationMessage(`Rejecting edit ${editIndex}`);
  }

  private async _handleAcceptAll() {
    // Implementation would apply all edits
    vscode.window.showInformationMessage('Accepting all edits');
  }

  private async _handleRejectAll() {
    // Implementation would reject all edits
    vscode.window.showInformationMessage('Rejecting all edits');
  }

  private async _handleShowFile(filePath: string, lineNumber?: number) {
    try {
      const document = await vscode.workspace.openTextDocument(filePath);
      const editor = await vscode.window.showTextDocument(document);
      
      if (lineNumber && lineNumber > 0) {
        const position = new vscode.Position(lineNumber - 1, 0);
        editor.selection = new vscode.Selection(position, position);
        editor.revealRange(new vscode.Range(position, position));
      }
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to open file: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}
