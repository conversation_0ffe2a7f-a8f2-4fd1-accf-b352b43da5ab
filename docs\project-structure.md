# Vidyadhara Project Structure

This document provides an overview of the Vidyadhara codebase structure, key components, and their relationships.

## Table of Contents
- [Project Overview](#project-overview)
- [Directory Structure](#directory-structure)
- [Core Components](#core-components)
- [Dependency Graph](#dependency-graph)
- [Key Files](#key-files)
- [Development Workflow](#development-workflow)
- [Common Patterns](#common-patterns)
- [Troubleshooting](#troubleshooting)

## Project Overview

Vidyadhara is a VS Code extension that provides AI-powered code understanding and manipulation capabilities. The project is written in TypeScript and follows the VS Code extension architecture.

## Directory Structure

```
vidyadhara/
├── .vscode/              # VS Code workspace settings
├── docs/                 # Project documentation
│   ├── analysis/         # Generated analysis reports
│   ├── dependencies/     # Dependency analysis
│   └── ...
├── scripts/              # Build and utility scripts
├── src/                  # Source code
│   ├── editors/          # Code editing components
│   ├── managers/         # Core service managers
│   ├── models/           # Data models
│   ├── retrieval/        # Code analysis and retrieval
│   ├── tasks/            # Background task handling
│   ├── ui/               # User interface components
│   ├── utils/            # Utility functions
│   ├── AIChatProvider.ts # AI chat functionality
│   ├── extension.ts      # Extension entry point
│   └── ...
├── test/                 # Test files
├── .gitignore
├── package.json
├── tsconfig.json
└── ...
```

## Core Components

### 1. Extension Entry Point (`extension.ts`)
- Initializes the extension
- Registers commands and providers
- Sets up the extension context

### 2. AI Chat (`AIChatProvider.ts`)
- Manages AI chat interactions
- Handles message passing between UI and AI services
- Maintains conversation state

### 3. Code Retrieval (`retrieval/`)
- `relationshipGraph.ts`: Tracks code relationships
- `symbolAnalyzer.ts`: Analyzes code symbols and their usage
- `codebaseRetrieval.ts`: Handles code search and retrieval

### 4. Editors (`editors/`)
- `stringReplaceEditor.ts`: Handles text replacements
- `editPreview.ts`: Manages edit previews
- `editHistory.ts`: Tracks edit history

## Dependency Graph

```mermaid
graph TD
    A[extension.ts] --> B[AIChatProvider]
    A --> C[Managers]
    B --> D[retrieval/]
    B --> E[editors/]
    C --> F[codeIndexManager]
    C --> G[diagnosticsManager]
    D --> H[relationshipGraph]
    D --> I[symbolAnalyzer]
```

## Key Files

| File | Purpose | Dependencies |
|------|---------|--------------|
| `extension.ts` | Main entry point | vscode, AIChatProvider |
| `AIChatProvider.ts` | AI chat functionality | vscode, retrieval/
| `retrieval/relationshipGraph.ts` | Code relationship mapping | typescript, vscode |
| `editors/stringReplaceEditor.ts` | Text editing operations | vscode, path |

## Development Workflow

1. **Setup**
   ```bash
   npm install
   ```

2. **Build**
   ```bash
   npm run compile
   ```

3. **Test**
   ```bash
   npm test
   ```

4. **Debug**
   - Use VS Code's built-in debugger
   - Set breakpoints in `.vscode/launch.json`

## Common Patterns

1. **Event Handling**
   - Uses VS Code's event system
   - Implements disposable pattern for resources

2. **Error Handling**
   - Centralized error handling in `errorHandler.ts`
   - Consistent error reporting through VS Code's notification system

3. **State Management**
   - Uses VS Code's Memento API for persistence
   - Manages state in respective managers

## Troubleshooting

### Common Issues
1. **TypeScript Errors**
   - Run `npm run check-types`
   - Check `tsconfig.json` for proper configuration

2. **Extension Not Loading**
   - Check VS Code Developer Tools for errors
   - Verify all dependencies are installed

3. **Performance Issues**
   - Profile using VS Code's built-in profiler
   - Check for memory leaks in long-running operations

## Dependencies

### Runtime Dependencies
- `vscode`: VS Code extension API
- `axios`: HTTP client
- `uuid`: Unique ID generation

### Dev Dependencies
- `typescript`: Type checking
- `@types/vscode`: VS Code type definitions
- `mocha`: Testing framework

---
*Last updated: 2025-07-11*
*Generated by dependency analyzer*
