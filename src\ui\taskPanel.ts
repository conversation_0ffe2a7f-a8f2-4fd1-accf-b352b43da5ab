import * as vscode from 'vscode';
import { Task, TaskState, TaskPriority, TaskManager } from '../tasks/taskManager';
import { TaskScheduler, ProjectSchedule } from '../tasks/taskScheduler';

export class TaskPanel {
  public static currentPanel: TaskPanel | undefined;
  private readonly _panel: vscode.WebviewPanel;
  private readonly _extensionUri: vscode.Uri;
  private _disposables: vscode.Disposable[] = [];
  private taskManager: TaskManager;
  private taskScheduler: TaskScheduler;

  public static createOrShow(
    extensionUri: vscode.Uri,
    taskManager: TaskManager,
    taskScheduler: TaskScheduler
  ) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    if (TaskPanel.currentPanel) {
      TaskPanel.currentPanel._panel.reveal(column);
      return;
    }

    const panel = vscode.window.createWebviewPanel(
      'taskPanel',
      'Task Management',
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.joinPath(extensionUri, 'media'),
          vscode.Uri.joinPath(extensionUri, 'out', 'media')
        ]
      }
    );

    TaskPanel.currentPanel = new TaskPanel(panel, extensionUri, taskManager, taskScheduler);
  }

  private constructor(
    panel: vscode.WebviewPanel,
    extensionUri: vscode.Uri,
    taskManager: TaskManager,
    taskScheduler: TaskScheduler
  ) {
    this._panel = panel;
    this._extensionUri = extensionUri;
    this.taskManager = taskManager;
    this.taskScheduler = taskScheduler;

    this._update();

    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

    this._panel.webview.onDidReceiveMessage(
      async message => {
        switch (message.command) {
          case 'createTask':
            await this.handleCreateTask(message.data);
            break;
          case 'updateTask':
            await this.handleUpdateTask(message.data);
            break;
          case 'deleteTask':
            await this.handleDeleteTask(message.taskId);
            break;
          case 'startTimeTracking':
            await this.handleStartTimeTracking(message.taskId);
            break;
          case 'stopTimeTracking':
            await this.handleStopTimeTracking(message.taskId);
            break;
          case 'generateSchedule':
            await this.handleGenerateSchedule();
            break;
          case 'filterTasks':
            await this.handleFilterTasks(message.filter);
            break;
          case 'exportTasks':
            await this.handleExportTasks(message.format);
            break;
        }
      },
      null,
      this._disposables
    );
  }

  public dispose() {
    TaskPanel.currentPanel = undefined;

    this._panel.dispose();

    while (this._disposables.length) {
      const x = this._disposables.pop();
      if (x) {
        x.dispose();
      }
    }
  }

  private _update() {
    const webview = this._panel.webview;
    this._panel.title = 'Task Management';
    this._panel.webview.html = this._getHtmlForWebview(webview);
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    const scriptPathOnDisk = vscode.Uri.joinPath(this._extensionUri, 'media', 'taskPanel.js');
    const stylePathOnDisk = vscode.Uri.joinPath(this._extensionUri, 'media', 'taskPanel.css');

    const scriptUri = webview.asWebviewUri(scriptPathOnDisk);
    const styleUri = webview.asWebviewUri(stylePathOnDisk);

    const nonce = getNonce();

    // Get current tasks and statistics
    const tasks = this.taskManager.getTasks();
    const statistics = this.taskManager.getTaskStatistics();
    const hierarchy = this.taskManager.getTaskHierarchy();

    return `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="${styleUri}" rel="stylesheet">
        <title>Task Management</title>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Task Management</h1>
            <div class="actions">
              <button id="createTaskBtn" class="btn btn-primary">Create Task</button>
              <button id="generateScheduleBtn" class="btn btn-secondary">Generate Schedule</button>
              <button id="exportBtn" class="btn btn-secondary">Export</button>
            </div>
          </div>

          <div class="dashboard">
            <div class="stats-grid">
              <div class="stat-card">
                <h3>Total Tasks</h3>
                <div class="stat-value">${statistics.total}</div>
              </div>
              <div class="stat-card">
                <h3>Completion Rate</h3>
                <div class="stat-value">${(statistics.completionRate * 100).toFixed(1)}%</div>
              </div>
              <div class="stat-card">
                <h3>Overdue Tasks</h3>
                <div class="stat-value">${statistics.overdueTasks}</div>
              </div>
              <div class="stat-card">
                <h3>Avg Completion Time</h3>
                <div class="stat-value">${(statistics.averageCompletionTime / (1000 * 60 * 60 * 24)).toFixed(1)} days</div>
              </div>
            </div>
          </div>

          <div class="filters">
            <div class="filter-group">
              <label>State:</label>
              <select id="stateFilter" multiple>
                <option value="NOT_STARTED">Not Started</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="BLOCKED">Blocked</option>
                <option value="REVIEW">Review</option>
                <option value="COMPLETE">Complete</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Priority:</label>
              <select id="priorityFilter" multiple>
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="CRITICAL">Critical</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Search:</label>
              <input type="text" id="searchFilter" placeholder="Search tasks...">
            </div>
            <button id="applyFiltersBtn" class="btn btn-secondary">Apply Filters</button>
            <button id="clearFiltersBtn" class="btn btn-secondary">Clear</button>
          </div>

          <div class="task-views">
            <div class="view-tabs">
              <button class="tab-btn active" data-view="list">List View</button>
              <button class="tab-btn" data-view="kanban">Kanban Board</button>
              <button class="tab-btn" data-view="gantt">Gantt Chart</button>
              <button class="tab-btn" data-view="calendar">Calendar</button>
            </div>

            <div id="listView" class="view-content active">
              <div class="task-list">
                ${this.generateTaskListHtml(hierarchy)}
              </div>
            </div>

            <div id="kanbanView" class="view-content">
              <div class="kanban-board">
                ${this.generateKanbanHtml(tasks)}
              </div>
            </div>

            <div id="ganttView" class="view-content">
              <div class="gantt-chart">
                <div id="ganttContainer">Gantt chart will be rendered here</div>
              </div>
            </div>

            <div id="calendarView" class="view-content">
              <div class="calendar">
                <div id="calendarContainer">Calendar will be rendered here</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Task Creation Modal -->
        <div id="taskModal" class="modal">
          <div class="modal-content">
            <div class="modal-header">
              <h2 id="modalTitle">Create Task</h2>
              <span class="close">&times;</span>
            </div>
            <div class="modal-body">
              <form id="taskForm">
                <div class="form-group">
                  <label for="taskName">Name:</label>
                  <input type="text" id="taskName" required>
                </div>
                <div class="form-group">
                  <label for="taskDescription">Description:</label>
                  <textarea id="taskDescription" rows="3"></textarea>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label for="taskPriority">Priority:</label>
                    <select id="taskPriority">
                      <option value="LOW">Low</option>
                      <option value="MEDIUM" selected>Medium</option>
                      <option value="HIGH">High</option>
                      <option value="CRITICAL">Critical</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="taskState">State:</label>
                    <select id="taskState">
                      <option value="NOT_STARTED" selected>Not Started</option>
                      <option value="IN_PROGRESS">In Progress</option>
                      <option value="BLOCKED">Blocked</option>
                      <option value="REVIEW">Review</option>
                      <option value="COMPLETE">Complete</option>
                    </select>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label for="taskAssignee">Assignee:</label>
                    <input type="text" id="taskAssignee">
                  </div>
                  <div class="form-group">
                    <label for="taskEstimatedHours">Estimated Hours:</label>
                    <input type="number" id="taskEstimatedHours" min="0" step="0.5">
                  </div>
                </div>
                <div class="form-group">
                  <label for="taskDueDate">Due Date:</label>
                  <input type="datetime-local" id="taskDueDate">
                </div>
                <div class="form-group">
                  <label for="taskTags">Tags (comma-separated):</label>
                  <input type="text" id="taskTags" placeholder="bug, feature, urgent">
                </div>
                <div class="form-group">
                  <label for="taskParent">Parent Task:</label>
                  <select id="taskParent">
                    <option value="">None</option>
                    ${this.generateParentTaskOptions(tasks)}
                  </select>
                </div>
                <div class="form-group">
                  <label for="taskDependencies">Dependencies:</label>
                  <select id="taskDependencies" multiple>
                    ${this.generateDependencyOptions(tasks)}
                  </select>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" id="cancelBtn">Cancel</button>
              <button type="submit" class="btn btn-primary" id="saveTaskBtn">Save Task</button>
            </div>
          </div>
        </div>

        <script nonce="${nonce}">
          window.initialData = {
            tasks: ${JSON.stringify(tasks)},
            statistics: ${JSON.stringify(this.serializeStatistics(statistics))},
            hierarchy: ${JSON.stringify(hierarchy)}
          };
        </script>
        <script nonce="${nonce}" src="${scriptUri}"></script>
      </body>
      </html>`;
  }

  private generateTaskListHtml(tasks: Task[]): string {
    return tasks.map(task => `
      <div class="task-item" data-task-id="${task.id}">
        <div class="task-header">
          <div class="task-info">
            <h3 class="task-name">${task.name}</h3>
            <span class="task-state state-${task.state.toLowerCase()}">${task.state}</span>
            <span class="task-priority priority-${task.priority.toLowerCase()}">${task.priority}</span>
          </div>
          <div class="task-actions">
            <button class="btn-icon edit-task" title="Edit Task">✏️</button>
            <button class="btn-icon delete-task" title="Delete Task">🗑️</button>
            <button class="btn-icon time-track" title="Track Time">⏱️</button>
          </div>
        </div>
        <div class="task-details">
          <p class="task-description">${task.description}</p>
          ${task.assignee ? `<div class="task-assignee">Assignee: ${task.assignee}</div>` : ''}
          ${task.dueDate ? `<div class="task-due-date">Due: ${new Date(task.dueDate).toLocaleDateString()}</div>` : ''}
          ${task.estimatedHours ? `<div class="task-hours">Estimated: ${task.estimatedHours}h</div>` : ''}
          ${task.tags.length > 0 ? `<div class="task-tags">${task.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>` : ''}
        </div>
        ${task.childIds && task.childIds.length > 0 ? `
          <div class="subtasks">
            ${this.generateTaskListHtml(task.childIds.map(id => this.taskManager.getTask(id)).filter(Boolean) as Task[])}
          </div>
        ` : ''}
      </div>
    `).join('');
  }

  private generateKanbanHtml(tasks: Task[]): string {
    const columns = [
      { state: TaskState.NOT_STARTED, title: 'Not Started' },
      { state: TaskState.IN_PROGRESS, title: 'In Progress' },
      { state: TaskState.BLOCKED, title: 'Blocked' },
      { state: TaskState.REVIEW, title: 'Review' },
      { state: TaskState.COMPLETE, title: 'Complete' }
    ];

    return columns.map(column => {
      const columnTasks = tasks.filter(task => task.state === column.state);
      return `
        <div class="kanban-column" data-state="${column.state}">
          <div class="column-header">
            <h3>${column.title}</h3>
            <span class="task-count">${columnTasks.length}</span>
          </div>
          <div class="column-tasks">
            ${columnTasks.map(task => `
              <div class="kanban-task" data-task-id="${task.id}" draggable="true">
                <h4>${task.name}</h4>
                <p>${task.description}</p>
                <div class="task-meta">
                  <span class="priority priority-${task.priority.toLowerCase()}">${task.priority}</span>
                  ${task.assignee ? `<span class="assignee">${task.assignee}</span>` : ''}
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    }).join('');
  }

  private generateParentTaskOptions(tasks: Task[]): string {
    return tasks
      .filter(task => task.childIds.length === 0) // Only leaf tasks can be parents
      .map(task => `<option value="${task.id}">${task.name}</option>`)
      .join('');
  }

  private generateDependencyOptions(tasks: Task[]): string {
    return tasks
      .map(task => `<option value="${task.id}">${task.name}</option>`)
      .join('');
  }

  private serializeStatistics(stats: any): any {
    return {
      total: stats.total,
      byState: Object.fromEntries(stats.byState),
      byPriority: Object.fromEntries(stats.byPriority),
      completionRate: stats.completionRate,
      averageCompletionTime: stats.averageCompletionTime,
      overdueTasks: stats.overdueTasks
    };
  }

  private async handleCreateTask(data: any): Promise<void> {
    try {
      await this.taskManager.createTask({
        name: data.name,
        description: data.description,
        priority: data.priority,
        state: data.state,
        assignee: data.assignee,
        estimatedHours: data.estimatedHours,
        dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
        tags: data.tags ? data.tags.split(',').map((tag: string) => tag.trim()) : [],
        parentId: data.parentId || undefined,
        dependencies: data.dependencies || []
      });
      this._update();
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to create task: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleUpdateTask(data: any): Promise<void> {
    try {
      await this.taskManager.updateTask(data.id, data.updates);
      this._update();
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to update task: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleDeleteTask(taskId: string): Promise<void> {
    try {
      await this.taskManager.deleteTask(taskId);
      this._update();
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to delete task: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleStartTimeTracking(taskId: string): Promise<void> {
    try {
      await this.taskManager.startTimeTracking(taskId);
      this._update();
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to start time tracking: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleStopTimeTracking(taskId: string): Promise<void> {
    try {
      const hoursSpent = await this.taskManager.stopTimeTracking(taskId);
      vscode.window.showInformationMessage(`Time tracking stopped. ${hoursSpent.toFixed(2)} hours logged.`);
      this._update();
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to stop time tracking: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleGenerateSchedule(): Promise<void> {
    try {
      const tasks = this.taskManager.getTasks();
      const schedule = this.taskScheduler.generateSchedule(tasks);
      
      // Show schedule in a new panel or update current view
      vscode.window.showInformationMessage(`Schedule generated: ${schedule.tasks.length} tasks, ${schedule.totalDuration} days`);
      this._update();
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to generate schedule: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleFilterTasks(filter: any): Promise<void> {
    // Filter logic would be implemented here
    this._update();
  }

  private async handleExportTasks(format: string): Promise<void> {
    try {
      const tasks = this.taskManager.getTasks();
      
      if (format === 'json') {
        const json = JSON.stringify(tasks, null, 2);
        const uri = await vscode.window.showSaveDialog({
          defaultUri: vscode.Uri.file('tasks.json'),
          filters: { 'JSON': ['json'] }
        });
        
        if (uri) {
          await vscode.workspace.fs.writeFile(uri, Buffer.from(json));
          vscode.window.showInformationMessage('Tasks exported successfully');
        }
      }
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to export tasks: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}
