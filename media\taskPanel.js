(function() {
  const vscode = acquireVsCodeApi();
  
  // State management
  let currentView = 'list';
  let tasks = [];
  let filteredTasks = [];
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    initializeData();
    initializeEventListeners();
    initializeViewSwitching();
    initializeModal();
    initializeDragAndDrop();
    
    // Load initial view
    switchView('list');
  });
  
  function initializeData() {
    if (window.initialData) {
      tasks = window.initialData.tasks || [];
      filteredTasks = [...tasks];
    }
  }
  
  function initializeEventListeners() {
    // Header actions
    document.getElementById('createTaskBtn')?.addEventListener('click', () => {
      openTaskModal();
    });
    
    document.getElementById('generateScheduleBtn')?.addEventListener('click', () => {
      vscode.postMessage({ command: 'generateSchedule' });
    });
    
    document.getElementById('exportBtn')?.addEventListener('click', () => {
      showExportOptions();
    });
    
    // Filter actions
    document.getElementById('applyFiltersBtn')?.addEventListener('click', () => {
      applyFilters();
    });
    
    document.getElementById('clearFiltersBtn')?.addEventListener('click', () => {
      clearFilters();
    });
    
    // Task actions
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('edit-task')) {
        const taskId = e.target.closest('.task-item').dataset.taskId;
        editTask(taskId);
      }
      
      if (e.target.classList.contains('delete-task')) {
        const taskId = e.target.closest('.task-item').dataset.taskId;
        deleteTask(taskId);
      }
      
      if (e.target.classList.contains('time-track')) {
        const taskId = e.target.closest('.task-item').dataset.taskId;
        toggleTimeTracking(taskId);
      }
    });
  }
  
  function initializeViewSwitching() {
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const view = this.dataset.view;
        switchView(view);
      });
    });
  }
  
  function initializeModal() {
    const modal = document.getElementById('taskModal');
    const closeBtn = modal?.querySelector('.close');
    const cancelBtn = document.getElementById('cancelBtn');
    const saveBtn = document.getElementById('saveTaskBtn');
    
    closeBtn?.addEventListener('click', () => {
      closeTaskModal();
    });
    
    cancelBtn?.addEventListener('click', () => {
      closeTaskModal();
    });
    
    saveBtn?.addEventListener('click', () => {
      saveTask();
    });
    
    // Close modal when clicking outside
    modal?.addEventListener('click', function(e) {
      if (e.target === modal) {
        closeTaskModal();
      }
    });
  }
  
  function initializeDragAndDrop() {
    // Initialize drag and drop for Kanban board
    document.addEventListener('dragstart', function(e) {
      if (e.target.classList.contains('kanban-task')) {
        e.dataTransfer.setData('text/plain', e.target.dataset.taskId);
        e.target.style.opacity = '0.5';
      }
    });
    
    document.addEventListener('dragend', function(e) {
      if (e.target.classList.contains('kanban-task')) {
        e.target.style.opacity = '1';
      }
    });
    
    document.addEventListener('dragover', function(e) {
      if (e.target.closest('.kanban-column')) {
        e.preventDefault();
      }
    });
    
    document.addEventListener('drop', function(e) {
      const column = e.target.closest('.kanban-column');
      if (column) {
        e.preventDefault();
        const taskId = e.dataTransfer.getData('text/plain');
        const newState = column.dataset.state;
        
        updateTaskState(taskId, newState);
      }
    });
  }
  
  function switchView(viewName) {
    currentView = viewName;
    
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.view === viewName);
    });
    
    // Update view content
    document.querySelectorAll('.view-content').forEach(content => {
      content.classList.toggle('active', content.id === viewName + 'View');
    });
    
    // Render the specific view
    switch (viewName) {
      case 'list':
        renderListView();
        break;
      case 'kanban':
        renderKanbanView();
        break;
      case 'gantt':
        renderGanttView();
        break;
      case 'calendar':
        renderCalendarView();
        break;
    }
  }
  
  function renderListView() {
    const container = document.querySelector('#listView .task-list');
    if (!container) return;
    
    container.innerHTML = '';
    
    const hierarchicalTasks = buildTaskHierarchy(filteredTasks);
    hierarchicalTasks.forEach(task => {
      container.appendChild(createTaskElement(task));
    });
  }
  
  function renderKanbanView() {
    const container = document.querySelector('#kanbanView .kanban-board');
    if (!container) return;
    
    const states = ['NOT_STARTED', 'IN_PROGRESS', 'BLOCKED', 'REVIEW', 'COMPLETE'];
    const stateNames = {
      'NOT_STARTED': 'Not Started',
      'IN_PROGRESS': 'In Progress',
      'BLOCKED': 'Blocked',
      'REVIEW': 'Review',
      'COMPLETE': 'Complete'
    };
    
    container.innerHTML = '';
    
    states.forEach(state => {
      const column = document.createElement('div');
      column.className = 'kanban-column';
      column.dataset.state = state;
      
      const stateTasks = filteredTasks.filter(task => task.state === state);
      
      column.innerHTML = `
        <div class="column-header">
          <h3>${stateNames[state]}</h3>
          <span class="task-count">${stateTasks.length}</span>
        </div>
        <div class="column-tasks">
          ${stateTasks.map(task => createKanbanTaskElement(task)).join('')}
        </div>
      `;
      
      container.appendChild(column);
    });
  }
  
  function renderGanttView() {
    const container = document.getElementById('ganttContainer');
    if (!container) return;
    
    container.innerHTML = '<p>Gantt chart functionality would be implemented here using a charting library like D3.js or Chart.js</p>';
  }
  
  function renderCalendarView() {
    const container = document.getElementById('calendarContainer');
    if (!container) return;
    
    container.innerHTML = '<p>Calendar view would be implemented here showing tasks by due date</p>';
  }
  
  function createTaskElement(task, level = 0) {
    const element = document.createElement('div');
    element.className = 'task-item';
    element.dataset.taskId = task.id;
    element.style.marginLeft = `${level * 20}px`;
    
    const dueDate = task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '';
    const tags = task.tags.map(tag => `<span class="tag">${tag}</span>`).join('');
    
    element.innerHTML = `
      <div class="task-header">
        <div class="task-info">
          <h3 class="task-name">${task.name}</h3>
          <span class="task-state state-${task.state.toLowerCase()}">${task.state}</span>
          <span class="task-priority priority-${task.priority.toLowerCase()}">${task.priority}</span>
        </div>
        <div class="task-actions">
          <button class="btn-icon edit-task" title="Edit Task">✏️</button>
          <button class="btn-icon delete-task" title="Delete Task">🗑️</button>
          <button class="btn-icon time-track" title="Track Time">⏱️</button>
        </div>
      </div>
      <div class="task-details">
        <p class="task-description">${task.description}</p>
        ${task.assignee ? `<div class="task-assignee">Assignee: ${task.assignee}</div>` : ''}
        ${dueDate ? `<div class="task-due-date">Due: ${dueDate}</div>` : ''}
        ${task.estimatedHours ? `<div class="task-hours">Estimated: ${task.estimatedHours}h</div>` : ''}
        ${tags ? `<div class="task-tags">${tags}</div>` : ''}
      </div>
    `;
    
    return element;
  }
  
  function createKanbanTaskElement(task) {
    return `
      <div class="kanban-task" data-task-id="${task.id}" draggable="true">
        <h4>${task.name}</h4>
        <p>${task.description}</p>
        <div class="task-meta">
          <span class="priority priority-${task.priority.toLowerCase()}">${task.priority}</span>
          ${task.assignee ? `<span class="assignee">${task.assignee}</span>` : ''}
        </div>
      </div>
    `;
  }
  
  function buildTaskHierarchy(tasks) {
    const taskMap = new Map(tasks.map(task => [task.id, { ...task, children: [] }]));
    const rootTasks = [];
    
    tasks.forEach(task => {
      const taskWithChildren = taskMap.get(task.id);
      if (task.parentId && taskMap.has(task.parentId)) {
        taskMap.get(task.parentId).children.push(taskWithChildren);
      } else {
        rootTasks.push(taskWithChildren);
      }
    });
    
    return rootTasks;
  }
  
  function applyFilters() {
    const stateFilter = Array.from(document.getElementById('stateFilter').selectedOptions).map(o => o.value);
    const priorityFilter = Array.from(document.getElementById('priorityFilter').selectedOptions).map(o => o.value);
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
    
    filteredTasks = tasks.filter(task => {
      if (stateFilter.length > 0 && !stateFilter.includes(task.state)) return false;
      if (priorityFilter.length > 0 && !priorityFilter.includes(task.priority)) return false;
      if (searchFilter && !task.name.toLowerCase().includes(searchFilter) && 
          !task.description.toLowerCase().includes(searchFilter)) return false;
      
      return true;
    });
    
    // Re-render current view
    switchView(currentView);
  }
  
  function clearFilters() {
    document.getElementById('stateFilter').selectedIndex = -1;
    document.getElementById('priorityFilter').selectedIndex = -1;
    document.getElementById('searchFilter').value = '';
    
    filteredTasks = [...tasks];
    switchView(currentView);
  }
  
  function openTaskModal(task = null) {
    const modal = document.getElementById('taskModal');
    const form = document.getElementById('taskForm');
    
    if (task) {
      // Edit mode
      document.getElementById('modalTitle').textContent = 'Edit Task';
      document.getElementById('taskName').value = task.name;
      document.getElementById('taskDescription').value = task.description;
      document.getElementById('taskPriority').value = task.priority;
      document.getElementById('taskState').value = task.state;
      document.getElementById('taskAssignee').value = task.assignee || '';
      document.getElementById('taskEstimatedHours').value = task.estimatedHours || '';
      document.getElementById('taskDueDate').value = task.dueDate ? new Date(task.dueDate).toISOString().slice(0, 16) : '';
      document.getElementById('taskTags').value = task.tags.join(', ');
      document.getElementById('taskParent').value = task.parentId || '';
      
      form.dataset.taskId = task.id;
    } else {
      // Create mode
      document.getElementById('modalTitle').textContent = 'Create Task';
      form.reset();
      delete form.dataset.taskId;
    }
    
    modal.classList.add('show');
  }
  
  function closeTaskModal() {
    const modal = document.getElementById('taskModal');
    modal.classList.remove('show');
  }
  
  function saveTask() {
    const form = document.getElementById('taskForm');
    const taskId = form.dataset.taskId;
    
    const taskData = {
      name: document.getElementById('taskName').value,
      description: document.getElementById('taskDescription').value,
      priority: document.getElementById('taskPriority').value,
      state: document.getElementById('taskState').value,
      assignee: document.getElementById('taskAssignee').value,
      estimatedHours: parseFloat(document.getElementById('taskEstimatedHours').value) || undefined,
      dueDate: document.getElementById('taskDueDate').value || undefined,
      tags: document.getElementById('taskTags').value.split(',').map(tag => tag.trim()).filter(Boolean),
      parentId: document.getElementById('taskParent').value || undefined,
      dependencies: Array.from(document.getElementById('taskDependencies').selectedOptions).map(o => o.value)
    };
    
    if (taskId) {
      // Update existing task
      vscode.postMessage({
        command: 'updateTask',
        data: { id: taskId, updates: taskData }
      });
    } else {
      // Create new task
      vscode.postMessage({
        command: 'createTask',
        data: taskData
      });
    }
    
    closeTaskModal();
  }
  
  function editTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      openTaskModal(task);
    }
  }
  
  function deleteTask(taskId) {
    if (confirm('Are you sure you want to delete this task?')) {
      vscode.postMessage({
        command: 'deleteTask',
        taskId: taskId
      });
    }
  }
  
  function toggleTimeTracking(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;
    
    const isTracking = task.metadata && task.metadata.timeTrackingStart;
    
    if (isTracking) {
      vscode.postMessage({
        command: 'stopTimeTracking',
        taskId: taskId
      });
    } else {
      vscode.postMessage({
        command: 'startTimeTracking',
        taskId: taskId
      });
    }
  }
  
  function updateTaskState(taskId, newState) {
    vscode.postMessage({
      command: 'updateTask',
      data: {
        id: taskId,
        updates: { state: newState }
      }
    });
  }
  
  function showExportOptions() {
    const options = ['JSON', 'CSV', 'PDF'];
    const choice = prompt(`Choose export format:\n${options.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}`);
    
    if (choice) {
      const index = parseInt(choice) - 1;
      if (index >= 0 && index < options.length) {
        vscode.postMessage({
          command: 'exportTasks',
          format: options[index].toLowerCase()
        });
      }
    }
  }
  
  // Handle messages from the extension
  window.addEventListener('message', event => {
    const message = event.data;
    
    switch (message.command) {
      case 'updateTasks':
        tasks = message.tasks;
        filteredTasks = [...tasks];
        switchView(currentView);
        break;
      
      case 'taskCreated':
        tasks.push(message.task);
        filteredTasks = [...tasks];
        switchView(currentView);
        break;
      
      case 'taskUpdated':
        const index = tasks.findIndex(t => t.id === message.task.id);
        if (index !== -1) {
          tasks[index] = message.task;
          filteredTasks = [...tasks];
          switchView(currentView);
        }
        break;
      
      case 'taskDeleted':
        tasks = tasks.filter(t => t.id !== message.taskId);
        filteredTasks = [...tasks];
        switchView(currentView);
        break;
    }
  });
})();
