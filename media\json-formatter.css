/* <PERSON><PERSON><PERSON> Formatter Styles */
.json-formatter {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 100%;
}

.json-formatter .json-formatter-row {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
}

.json-formatter .json-formatter-row .json-formatter-row {
  margin-left: 1em;
}

.json-formatter .json-formatter-row .json-formatter-key {
  color: #000;
  font-weight: bold;
}

.json-formatter .json-formatter-row .json-formatter-value {
  color: #0366d6;
}

.json-formatter .json-formatter-row .json-formatter-string {
  color: #032f62;
  white-space: pre-wrap;
  word-break: break-word;
}

.json-formatter .json-formatter-row .json-formatter-number {
  color: #005cc5;
}

.json-formatter .json-formatter-row .json-formatter-boolean {
  color: #6f42c1;
}

.json-formatter .json-formatter-row .json-formatter-null {
  color: #d73a49;
}

.json-formatter .json-formatter-row .json-formatter-undefined {
  color: #6a737d;
}

.json-formatter .json-formatter-row .json-formatter-function {
  color: #6f42c1;
  font-style: italic;
}

.json-formatter .json-formatter-row .json-formatter-constructor-name {
  color: #6f42c1;
}

.json-formatter .json-formatter-row .json-formatter-date {
  color: #6f42c1;
}

.json-formatter .json-formatter-row .json-formatter-url {
  color: #0366d6;
  text-decoration: underline;
  cursor: pointer;
}

.json-formatter .json-formatter-row .json-formatter-bracket {
  color: #000;
}

.json-formatter .json-formatter-row .json-formatter-key,
.json-formatter .json-formatter-row .json-formatter-bracket {
  cursor: pointer;
}

.json-formatter .json-formatter-row .json-formatter-key:hover,
.json-formatter .json-formatter-row .json-formatter-bracket:hover {
  text-decoration: underline;
}

.json-formatter .json-formatter-row .json-formatter-children {
  margin-left: 1em;
}

.json-formatter .json-formatter-row .json-formatter-toggler {
  color: #0366d6;
  cursor: pointer;
  margin-right: 4px;
  user-select: none;
}

.json-formatter .json-formatter-row .json-formatter-toggler:hover {
  text-decoration: underline;
}

.json-formatter .json-formatter-row .json-formatter-toggler:after {
  content: '-';
  display: inline-block;
  width: 10px;
  text-align: center;
}

.json-formatter .json-formatter-row .json-formatter-toggler.collapsed:after {
  content: '+';
}

.json-formatter .json-formatter-row .json-formatter-toggler.empty:after {
  content: '';
  visibility: hidden;
}

/* Dark theme support */
.vscode-dark .json-formatter {
  background-color: #1e1e1e;
  color: #d4d4d4;
}

.vscode-dark .json-formatter .json-formatter-key {
  color: #9cdcfe;
}

.vscode-dark .json-formatter .json-formatter-string {
  color: #ce9178;
}

.vscode-dark .json-formatter .json-formatter-number {
  color: #b5cea8;
}

.vscode-dark .json-formatter .json-formatter-boolean {
  color: #569cd6;
}

.vscode-dark .json-formatter .json-formatter-null {
  color: #ce9178;
}

.vscode-dark .json-formatter .json-formatter-undefined {
  color: #6a737d;
}

.vscode-dark .json-formatter .json-formatter-function,
.vscode-dark .json-formatter .json-formatter-constructor-name,
.vscode-dark .json-formatter .json-formatter-date {
  color: #dcdcaa;
}

.vscode-dark .json-formatter .json-formatter-url {
  color: #9cdcfe;
}

.vscode-dark .json-formatter .json-formatter-bracket {
  color: #d4d4d4;
}
