import * as vscode from 'vscode';
import { EditOperation } from './stringReplaceEditor';
import { EditPreviewData, BatchEditPreview } from './editPreview';

export interface ConfirmationOptions {
  showPreview: boolean;
  requireConfirmation: boolean;
  riskAssessment: boolean;
  autoConfirmSafe: boolean;
  batchConfirmation: boolean;
}

export interface RiskAssessment {
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: string[];
  recommendations: string[];
  requiresExplicitConfirmation: boolean;
}

export interface ConfirmationResult {
  confirmed: boolean;
  selectedOperations: EditOperation[];
  userFeedback?: string;
}

export class ConfirmationWorkflow {
  private defaultOptions: ConfirmationOptions = {
    showPreview: true,
    requireConfirmation: true,
    riskAssessment: true,
    autoConfirmSafe: false,
    batchConfirmation: true
  };

  constructor() {
    this.loadUserPreferences();
  }

  /**
   * Show confirmation workflow for single edit operation
   */
  async confirmSingleEdit(
    operation: EditOperation,
    preview: EditPreviewData,
    options: Partial<ConfirmationOptions> = {}
  ): Promise<ConfirmationResult> {
    const opts = { ...this.defaultOptions, ...options };
    
    // Assess risk
    const risk = this.assessEditRisk(operation, preview);
    
    // Auto-confirm safe operations if enabled
    if (opts.autoConfirmSafe && risk.riskLevel === 'low' && !opts.requireConfirmation) {
      return {
        confirmed: true,
        selectedOperations: [operation]
      };
    }

    // Show confirmation dialog
    return await this.showSingleEditConfirmation(operation, preview, risk, opts);
  }

  /**
   * Show confirmation workflow for batch edit operations
   */
  async confirmBatchEdit(
    operations: EditOperation[],
    batchPreview: BatchEditPreview,
    options: Partial<ConfirmationOptions> = {}
  ): Promise<ConfirmationResult> {
    const opts = { ...this.defaultOptions, ...options };
    
    // Assess overall risk
    const overallRisk = this.assessBatchRisk(operations, batchPreview);
    
    // Show batch confirmation
    return await this.showBatchEditConfirmation(operations, batchPreview, overallRisk, opts);
  }

  /**
   * Assess risk level of a single edit operation
   */
  private assessEditRisk(operation: EditOperation, preview: EditPreviewData): RiskAssessment {
    const factors: string[] = [];
    const recommendations: string[] = [];
    let riskLevel: RiskAssessment['riskLevel'] = 'low';

    // Check file type criticality
    const criticalFiles = [
      '.gitignore', 'package.json', 'tsconfig.json', 'webpack.config.js',
      'Dockerfile', 'docker-compose.yml', '.env', 'config.json'
    ];
    
    if (criticalFiles.some(file => operation.filePath.endsWith(file))) {
      factors.push('Critical configuration file');
      recommendations.push('Review changes carefully before applying');
      riskLevel = 'high';
    }

    // Check change size
    const stats = preview.changeStats;
    const totalChanges = stats.additions + stats.deletions + stats.modifications;
    
    if (totalChanges > 100) {
      factors.push(`Large change: ${totalChanges} lines affected`);
      recommendations.push('Consider breaking into smaller changes');
      riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
    }

    // Check for deletions
    if (stats.deletions > 50) {
      factors.push(`Large deletion: ${stats.deletions} lines removed`);
      recommendations.push('Ensure deleted code is not needed');
      riskLevel = riskLevel === 'low' ? 'medium' : 'high';
    }

    // Check for system commands or dangerous patterns
    const dangerousPatterns = [
      /rm\s+-rf/, /del\s+\/[fs]/, /DROP\s+TABLE/i, /DELETE\s+FROM/i,
      /sudo/, /chmod\s+777/, /eval\s*\(/, /exec\s*\(/
    ];
    
    if (dangerousPatterns.some(pattern => pattern.test(operation.newText))) {
      factors.push('Potentially dangerous commands detected');
      recommendations.push('Verify commands are safe and necessary');
      riskLevel = 'critical';
    }

    // Check for binary or large files
    if (operation.oldText.includes('\0') || operation.newText.includes('\0')) {
      factors.push('Binary file modification');
      recommendations.push('Ensure binary file changes are intentional');
      riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
    }

    return {
      riskLevel,
      factors,
      recommendations,
      requiresExplicitConfirmation: riskLevel === 'high' || riskLevel === 'critical'
    };
  }

  /**
   * Assess risk level of batch operations
   */
  private assessBatchRisk(operations: EditOperation[], batchPreview: BatchEditPreview): RiskAssessment {
    const factors: string[] = [];
    const recommendations: string[] = [];
    let highestRisk: RiskAssessment['riskLevel'] = 'low';

    // Assess each operation and aggregate risks
    const individualRisks = operations.map((op, index) => 
      this.assessEditRisk(op, batchPreview.edits[index])
    );

    individualRisks.forEach(risk => {
      factors.push(...risk.factors);
      recommendations.push(...risk.recommendations);
      
      if (risk.riskLevel === 'critical') highestRisk = 'critical';
      else if (risk.riskLevel === 'high' && highestRisk !== 'critical') highestRisk = 'high';
      else if (risk.riskLevel === 'medium' && highestRisk === 'low') highestRisk = 'medium';
    });

    // Additional batch-specific risks
    const stats = batchPreview.totalStats;
    
    if (stats.filesChanged > 20) {
      factors.push(`Large batch: ${stats.filesChanged} files affected`);
      recommendations.push('Consider processing in smaller batches');
      highestRisk = highestRisk === 'low' ? 'medium' : highestRisk;
    }

    if (stats.totalDeletions > 200) {
      factors.push(`Massive deletion: ${stats.totalDeletions} total lines removed`);
      recommendations.push('Ensure all deletions are intentional');
      highestRisk = 'high';
    }

    // Remove duplicates
    const uniqueFactors = [...new Set(factors)];
    const uniqueRecommendations = [...new Set(recommendations)];

    return {
      riskLevel: highestRisk,
      factors: uniqueFactors,
      recommendations: uniqueRecommendations,
      requiresExplicitConfirmation: ['high', 'critical'].includes(highestRisk)
    };
  }

  /**
   * Show single edit confirmation dialog
   */
  private async showSingleEditConfirmation(
    operation: EditOperation,
    preview: EditPreviewData,
    risk: RiskAssessment,
    options: ConfirmationOptions
  ): Promise<ConfirmationResult> {
    const stats = preview.changeStats;
    
    // Build confirmation message
    let message = `Apply changes to ${operation.filePath}?\n\n`;
    message += `Changes: +${stats.additions} additions, -${stats.deletions} deletions, ~${stats.modifications} modifications\n`;
    
    if (risk.riskLevel !== 'low') {
      message += `\n⚠️ Risk Level: ${risk.riskLevel.toUpperCase()}\n`;
      message += `Factors: ${risk.factors.join(', ')}\n`;
      
      if (risk.recommendations.length > 0) {
        message += `\nRecommendations:\n${risk.recommendations.map(r => `• ${r}`).join('\n')}`;
      }
    }

    // Choose appropriate dialog based on risk level
    let result: string | undefined;
    
    if (risk.requiresExplicitConfirmation) {
      result = await vscode.window.showWarningMessage(
        message,
        { modal: true },
        'I understand the risks - Apply Changes',
        'Show Preview',
        'Cancel'
      );
    } else {
      result = await vscode.window.showInformationMessage(
        message,
        { modal: options.requireConfirmation },
        'Apply Changes',
        'Show Preview',
        'Cancel'
      );
    }

    switch (result) {
      case 'Apply Changes':
      case 'I understand the risks - Apply Changes':
        return {
          confirmed: true,
          selectedOperations: [operation]
        };
      
      case 'Show Preview':
        // This would trigger the preview panel
        const previewResult = await this.showPreviewAndConfirm(operation, preview);
        return previewResult;
      
      default:
        return {
          confirmed: false,
          selectedOperations: []
        };
    }
  }

  /**
   * Show batch edit confirmation dialog
   */
  private async showBatchEditConfirmation(
    operations: EditOperation[],
    batchPreview: BatchEditPreview,
    risk: RiskAssessment,
    options: ConfirmationOptions
  ): Promise<ConfirmationResult> {
    const stats = batchPreview.totalStats;
    
    let message = `Apply batch changes to ${stats.filesChanged} files?\n\n`;
    message += `Total changes: +${stats.totalAdditions} additions, -${stats.totalDeletions} deletions, ~${stats.totalModifications} modifications\n`;
    
    if (risk.riskLevel !== 'low') {
      message += `\n⚠️ Overall Risk Level: ${risk.riskLevel.toUpperCase()}\n`;
      message += `Risk factors: ${risk.factors.slice(0, 3).join(', ')}`;
      if (risk.factors.length > 3) {
        message += ` and ${risk.factors.length - 3} more...`;
      }
    }

    const buttons = [];
    
    if (risk.requiresExplicitConfirmation) {
      buttons.push('I understand the risks - Apply All Changes');
    } else {
      buttons.push('Apply All Changes');
    }
    
    buttons.push('Review Individual Files', 'Show Preview', 'Cancel');

    const result = await vscode.window.showWarningMessage(
      message,
      { modal: true },
      ...buttons
    );

    switch (result) {
      case 'Apply All Changes':
      case 'I understand the risks - Apply All Changes':
        return {
          confirmed: true,
          selectedOperations: operations
        };
      
      case 'Review Individual Files':
        return await this.showIndividualFileConfirmations(operations, batchPreview);
      
      case 'Show Preview':
        return await this.showBatchPreviewAndConfirm(operations, batchPreview);
      
      default:
        return {
          confirmed: false,
          selectedOperations: []
        };
    }
  }

  /**
   * Show individual file confirmations for batch operations
   */
  private async showIndividualFileConfirmations(
    operations: EditOperation[],
    batchPreview: BatchEditPreview
  ): Promise<ConfirmationResult> {
    const confirmedOperations: EditOperation[] = [];
    
    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i];
      const preview = batchPreview.edits[i];
      const risk = this.assessEditRisk(operation, preview);
      
      const result = await this.showSingleEditConfirmation(
        operation,
        preview,
        risk,
        { ...this.defaultOptions, requireConfirmation: true }
      );
      
      if (result.confirmed) {
        confirmedOperations.push(...result.selectedOperations);
      }
    }

    return {
      confirmed: confirmedOperations.length > 0,
      selectedOperations: confirmedOperations
    };
  }

  /**
   * Show preview and get confirmation
   */
  private async showPreviewAndConfirm(
    operation: EditOperation,
    preview: EditPreviewData
  ): Promise<ConfirmationResult> {
    // This would integrate with the EditPreviewPanel
    // For now, show a simplified confirmation
    const result = await vscode.window.showInformationMessage(
      'Preview shown. Apply changes?',
      'Apply Changes',
      'Cancel'
    );

    return {
      confirmed: result === 'Apply Changes',
      selectedOperations: result === 'Apply Changes' ? [operation] : []
    };
  }

  /**
   * Show batch preview and get confirmation
   */
  private async showBatchPreviewAndConfirm(
    operations: EditOperation[],
    batchPreview: BatchEditPreview
  ): Promise<ConfirmationResult> {
    // This would integrate with the EditPreviewPanel for batch operations
    const result = await vscode.window.showInformationMessage(
      'Batch preview shown. Apply changes?',
      'Apply All Changes',
      'Select Individual Changes',
      'Cancel'
    );

    switch (result) {
      case 'Apply All Changes':
        return {
          confirmed: true,
          selectedOperations: operations
        };
      
      case 'Select Individual Changes':
        return await this.showIndividualFileConfirmations(operations, batchPreview);
      
      default:
        return {
          confirmed: false,
          selectedOperations: []
        };
    }
  }

  /**
   * Load user preferences for confirmation workflow
   */
  private loadUserPreferences(): void {
    const config = vscode.workspace.getConfiguration('vidyadhara.confirmation');
    
    this.defaultOptions = {
      showPreview: config.get('showPreview', true),
      requireConfirmation: config.get('requireConfirmation', true),
      riskAssessment: config.get('riskAssessment', true),
      autoConfirmSafe: config.get('autoConfirmSafe', false),
      batchConfirmation: config.get('batchConfirmation', true)
    };
  }

  /**
   * Update user preferences
   */
  async updatePreferences(options: Partial<ConfirmationOptions>): Promise<void> {
    const config = vscode.workspace.getConfiguration('vidyadhara.confirmation');
    
    for (const [key, value] of Object.entries(options)) {
      await config.update(key, value, vscode.ConfigurationTarget.Global);
    }
    
    this.loadUserPreferences();
  }

  /**
   * Get current preferences
   */
  getPreferences(): ConfirmationOptions {
    return { ...this.defaultOptions };
  }
}
