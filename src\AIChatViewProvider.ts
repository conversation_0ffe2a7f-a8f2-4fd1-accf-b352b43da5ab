import * as vscode from 'vscode';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Local imports
import { Message, MessageHandler } from './messages';
import { ModeManager } from './managers/modeManager';
import { ChatMode, ChatModeConfig, CHAT_MODE_CONFIGS } from './models/chatMode';
import { AIChatProvider } from './AIChatProvider';
import { MemoryManager } from './managers/memoryManager';
import { ProcessManager } from './managers/processManager';
import { GithubManager } from './managers/githubManager';
import { DiagnosticsManager } from './managers/diagnosticsManager';
import { WebManager } from './managers/webManager';
import { CodeIndexer } from './codeIndexer';
import { CodeStructureAnalyzer } from './codeStructureAnalyzer';

// Webview message interface that extends the base Message type
interface WebviewMessage extends Message {
  command?: string;
  mode?: string;
  content?: string;
  [key: string]: any;
}

// Simple nonce generator
function getNonce(): string {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}

export class AIChatViewProvider implements vscode.WebviewViewProvider {
  private _view: vscode.WebviewView | undefined;
  private _webview: vscode.Webview | undefined;
  private _extensionUri: vscode.Uri;
  private _disposables: vscode.Disposable[] = [];
  private _messageHandler: MessageHandler | undefined;
  private _chatProvider: AIChatProvider;
  private _currentConversationId: string = '';
  private readonly _modeManager: ModeManager;
  private _isProcessingMessage: boolean = false;
  private _isHandlingModeMessage: boolean = false;
  private _pendingMessages: WebviewMessage[] = [];

  constructor(
    private readonly _context: vscode.ExtensionContext,
    private readonly _memoryManager: MemoryManager,
    private readonly _processManager: ProcessManager,
    private readonly _githubManager: GithubManager,
    private readonly _diagnosticsManager: DiagnosticsManager,
    private readonly _webManager: WebManager,
    private readonly _codeIndexer: CodeIndexer,
    private readonly _codeStructureAnalyzer: CodeStructureAnalyzer
  ) {
    this._modeManager = new ModeManager();
    this._extensionUri = _context.extensionUri;
    this._chatProvider = new AIChatProvider(
      _context,
      _memoryManager,
      _processManager,
      _githubManager,
      _diagnosticsManager,
      _webManager,
      _codeIndexer,
      _codeStructureAnalyzer,
      this._modeManager
    );
    this._currentConversationId = uuidv4();
  }

  public get webviewView(): vscode.WebviewView | undefined {
    return this._view;
  }

  public async resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ): Promise<void> {
    this._view = webviewView;
    this._webview = webviewView.webview;

    // Set up webview options
    webviewView.webview.options = {
      enableScripts: true,
      enableCommandUris: true,
      localResourceRoots: [
        this._extensionUri,
        vscode.Uri.joinPath(this._extensionUri, 'media')
      ]
    };

    // Set up message handler
    const messageHandler = async (message: WebviewMessage) => {
      if (!this._view?.webview) {
        console.warn('Webview not initialized');
        return;
      }

      const messageType = message.type || message.command;
      if (!messageType) {
        console.warn('Message has no type or command:', message);
        return;
      }

      // Skip logging for high-frequency messages to reduce noise
      if (messageType !== 'getCurrentMode') {
        console.log(`[AIChatViewProvider] Handling message: ${messageType}`, message);
      }

      try {
        // Handle mode-related messages
        if (['setMode', 'getCurrentMode', 'getModeControls'].includes(messageType)) {
          await this._handleModeMessage(message);
        } 
        // Handle webview ready event
        else if (messageType === 'webviewReady') {
          console.log('[AIChatViewProvider] Webview is ready');
          this.sendInitialization();
        }
        // Handle conversation list request
        else if (messageType === 'getConversations') {
          if (this._view?.webview) {
            this._view.webview.postMessage({
              type: 'conversations',
              conversations: [],
              timestamp: Date.now()
            });
          }
        }
        // Handle settings request
        else if (messageType === 'getSettings') {
          const config = vscode.workspace.getConfiguration('vidyadhara');
          if (this._view?.webview) {
            this._view.webview.postMessage({
              type: 'settings',
              settings: {
                githubToken: config.get('github.token', ''),
                githubOwner: config.get('github.owner', ''),
                githubRepo: config.get('github.repo', ''),
                githubBranch: config.get('github.defaultBranch', 'main'),
                defaultModel: config.get('defaultModel', 'anthropic/claude-3-opus-20240229'),
                maxContextLength: config.get('maxContextLength', 100000),
                theme: config.get('theme', 'system')
              },
              timestamp: Date.now()
            });
          }
        }
        // Handle save settings request
        else if (messageType === 'saveSettings') {
          await this.handleSaveSettings(message.settings);
        }
        // Handle unknown message types
        else {
          console.warn('Unhandled message type:', messageType);
          if (this._view?.webview) {
            this._view.webview.postMessage({
              type: 'error',
              message: `Unhandled message type: ${messageType}`,
              timestamp: Date.now()
            });
          }
        }
      } catch (error) {
        console.error('Error handling message:', error);
        if (this._view?.webview) {
          this._view.webview.postMessage({
            type: 'error',
            message: `Error handling message: ${error instanceof Error ? error.message : String(error)}`,
            timestamp: Date.now()
          });
        }
      }
    };

    // Register the message handler
    webviewView.webview.onDidReceiveMessage(messageHandler, undefined, this._disposables);

    // Set the HTML content
    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
    console.log('[AIChatViewProvider] Webview HTML content set');

    // Send initialization message after a short delay to ensure webview is ready
    setTimeout(() => {
      if (this._view?.webview) {
        this._view.webview.postMessage({
          type: 'initialized',
          timestamp: Date.now()
        });
      }
    }, 500);
  }

  private sendInitialization(): void {
    if (!this._view) {
      return;
    }

    const currentMode = this._modeManager.getCurrentMode();
    const modeConfig = this._modeManager.getModeConfig();
    const availableModes = this._modeManager.getAvailableModes();

    this._view.webview.postMessage({
      type: 'initialized',
      modeId: currentMode,
      modeName: modeConfig?.name || currentMode,
      modeIcon: modeConfig?.icon || '💬',
      timestamp: Date.now()
    });

    // Also send the mode controls
    this._view.webview.postMessage({
      type: 'updateActiveMode',
      modeId: currentMode,
      modeName: modeConfig?.name || currentMode,
      modeIcon: modeConfig?.icon || '💬',
      availableModes: Object.entries(availableModes).map(([id, config]) => ({
        id,
        name: config.name,
        icon: config.icon,
        description: config.description
      })),
      timestamp: Date.now()
    });
  }

  private async _handleModeMessage(message: WebviewMessage): Promise<void> {
    if (!this._view?.webview) {
      console.warn('[AIChatViewProvider] Webview not initialized');
      return;
    }

    const messageType = message.type || message.command;
    if (!messageType) {
      console.warn('[AIChatViewProvider] Message has no type or command:', message);
      return;
    }

    // Prevent reentrancy
    if (this._isHandlingModeMessage) {
      console.log(`[AIChatViewProvider] Already handling mode message, ignoring:`, messageType);
      return;
    }

    this._isHandlingModeMessage = true;

    try {
      if (messageType === 'setMode' && message.mode) {
        const modeId = message.mode;
        console.log(`[AIChatViewProvider] Setting mode to: ${modeId}`);
        
        const success = await this._modeManager.setMode(modeId as any);
        if (!success) {
          console.warn(`[AIChatViewProvider] Failed to set mode to: ${modeId}`);
          return;
        }
        
        const modeConfig = this._modeManager.getModeConfig();
        console.log(`[AIChatViewProvider] Successfully set mode to: ${modeId}`);

        if (this._view?.webview) {
          this._view.webview.postMessage({
            type: 'updateActiveMode',
            modeId,
            modeName: modeConfig?.name || modeId,
            modeIcon: modeConfig?.icon || '💬',
            timestamp: Date.now()
          });
        }
      } 
      else if (messageType === 'getCurrentMode' || messageType === 'getModeControls') {
        console.log(`[AIChatViewProvider] Handling ${messageType} request`);
        const currentMode = this._modeManager.getCurrentMode();
        const modeConfig = this._modeManager.getModeConfig();
        const availableModes = this._modeManager.getAvailableModes();

        if (this._view?.webview) {
          this._view.webview.postMessage({
            type: 'updateActiveMode',
            modeId: currentMode,
            modeName: modeConfig?.name || currentMode,
            modeIcon: modeConfig?.icon || '💬',
            availableModes: Object.entries(availableModes).map(([id, config]) => ({
              id,
              name: config.name,
              icon: config.icon,
              description: config.description
            })),
            timestamp: Date.now()
          });
          console.log(`[AIChatViewProvider] Sent updateActiveMode for ${currentMode}`);
        }
      }
    } catch (error) {
      console.error(`[AIChatViewProvider] Error handling mode message ${messageType}:`, error);
    } finally {
      this._isHandlingModeMessage = false;
    }
  }

  // Message handler for webview messages - consolidated implementation above

  private async handleSaveSettings(settings: any): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('vidyadhara');
      await config.update('github.token', settings.githubToken || '', vscode.ConfigurationTarget.Global);
      await config.update('github.owner', settings.githubOwner || '', vscode.ConfigurationTarget.Global);
      await config.update('github.repo', settings.githubRepo || '', vscode.ConfigurationTarget.Global);
      await config.update('github.defaultBranch', settings.githubBranch || 'main', vscode.ConfigurationTarget.Global);
      await config.update('defaultModel', settings.defaultModel || 'anthropic/claude-3-opus-20240229', vscode.ConfigurationTarget.Global);
      await config.update('maxContextLength', settings.maxContextLength || 100000, vscode.ConfigurationTarget.Global);
      await config.update('theme', settings.theme || 'system', vscode.ConfigurationTarget.Global);

      // Confirm settings saved
      this._view?.webview.postMessage({
        type: 'settingsSaved',
        success: true
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      this._view?.webview.postMessage({
        type: 'error',
        message: `Error saving settings: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private async handleOpenFile(filePath: string, line?: number, character?: number): Promise<void> {
    try {
      const document = await vscode.workspace.openTextDocument(vscode.Uri.file(filePath));
      await vscode.window.showTextDocument(document, {
        selection: new vscode.Range(
          new vscode.Position(line || 0, character || 0),
          new vscode.Position(line || 0, character || 0)
        )
      });
    } catch (error) {
      console.error('Error opening file:', error);
      this._view?.webview.postMessage({
        type: 'error',
        message: `Error opening file: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private handleUnknownMessageType(messageType: string): void {
    console.warn(`[AIChatViewProvider] Unknown message type: ${messageType}`);
    this._view?.webview.postMessage({ 
      type: 'error', 
      message: `Unknown message type: ${messageType}` 
    });
  }

  public async handleModeChange(modeId: string): Promise<{ modeId: string; modeName: string; modeIcon: string }> {
    console.log(`[AIChatViewProvider] Setting mode to: ${modeId}`);

    // Validate the mode
    if (!Object.values(ChatMode).includes(modeId as ChatMode)) {
      throw new Error(`Invalid mode: ${modeId}. Valid modes are: ${Object.values(ChatMode).join(', ')}`);
    }

    // Cast to ChatMode since we've validated it
    const chatMode = modeId as ChatMode;

    // Get the current mode
    const currentMode = this._modeManager.getCurrentMode();

    // Don't do anything if we're already in the requested mode
    if (currentMode === chatMode) {
      console.log(`[AIChatViewProvider] Already in mode: ${chatMode}`);
      const modeConfig = CHAT_MODE_CONFIGS[chatMode];
      return {
        modeId: chatMode,
        modeName: modeConfig?.name || chatMode,
        modeIcon: modeConfig?.icon || '💬'
      };
    }

    try {
      // Update mode in mode manager
      const modeSet = await this._modeManager.setMode(chatMode);
      if (!modeSet) {
        throw new Error(`Failed to set mode to: ${modeId}`);
      }

      // Update the chat provider's mode
      this._chatProvider.setMode(chatMode);

      // Get the mode configuration
      const modeConfig = CHAT_MODE_CONFIGS[chatMode];

      // Update VS Code context for when clauses
      await Promise.all([
        vscode.commands.executeCommand('setContext', 'vidyadhara.activeMode', chatMode),
        vscode.commands.executeCommand('setContext', 'vidyadhara.isAgentMode', chatMode.includes('agent')),
        vscode.commands.executeCommand('setContext', 'vidyadhara.isAutoMode', chatMode === 'agent-auto')
      ]);

      // Update the UI to reflect the new mode
      if (this._view) {
        this._view.webview.postMessage({
          type: 'updateActiveMode',
          modeId: chatMode,
          modeName: modeConfig?.name || chatMode,
          modeIcon: modeConfig?.icon || '💬',
          timestamp: Date.now()
        });
      }

      // Show a status bar message to indicate mode change
      vscode.window.setStatusBarMessage(`Mode changed to: ${modeConfig?.name || chatMode}`, 3000);

      return {
        modeId: chatMode,
        modeName: modeConfig?.name || chatMode,
        modeIcon: modeConfig?.icon || '💬'
      };

    } catch (error) {
      console.error(`[AIChatViewProvider] Error changing mode to ${modeId}:`, error);

      // Show error to user
      const errorMessage = error instanceof Error ? error.message : `Failed to change mode to ${modeId}`;
      vscode.window.showErrorMessage(errorMessage);

      // Re-throw the error to be handled by the caller
      throw error;
    }
  }

  private getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  private _getHtmlForWebview(webview: vscode.Webview): string {
    // Get the local path to main script run in the webview
    const scriptUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js')
    );

    // Get the local path to css styles
    const styleUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'media', 'styles.css')
    );

    // Use a nonce to whitelist which scripts can be run
    const nonce = this.getNonce();
    const cspSource = webview.cspSource;

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="Content-Security-Policy" content="
        default-src 'none';
        script-src 'nonce-${nonce}' 'unsafe-eval';
        style-src 'nonce-${nonce}' 'unsafe-inline' ${cspSource};
        img-src ${cspSource} https: data:;
        font-src ${cspSource};
        connect-src ${cspSource} https:;
      ">
      <title>Vidyadhara AI Chat</title>
      <link href="${styleUri}" rel="stylesheet" nonce="${nonce}" />
      <style nonce="${nonce}">
        :root {
          --vscode-editor-background: #1e1e1e;
          --vscode-foreground: #cccccc;
          --vscode-font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', 'Ubuntu', 'Droid Sans', sans-serif;
          --vscode-descriptionForeground: #a0a0a0;
        }
        body, html {
          margin: 0;
          padding: 0;
          width: 100%;
          height: 100%;
          overflow: hidden;
        }
        #app {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          background-color: var(--vscode-editor-background);
          color: var(--vscode-foreground);
          font-family: var(--vscode-font-family);
        }
        .chat-container {
          flex: 1;
          overflow-y: auto;
          padding: 16px;
        }
        #chat-messages {
          max-width: 800px;
          margin: 0 auto;
          width: 100%;
        }
        .input-container {
          padding: 16px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          background-color: var(--vscode-editor-background);
        }
        .input-row {
          display: flex;
          gap: 8px;
        }
        #message-input {
          flex: 1;
          padding: 8px 12px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          background-color: rgba(255, 255, 255, 0.05);
          color: var(--vscode-foreground);
          font-family: var(--vscode-font-family);
          resize: none;
        }
        #send-button, #stop-button {
          padding: 8px 16px;
          background-color: #0e639c;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
        #stop-button {
          background-color: #c42b1c;
          display: none;
        }
        #send-button:disabled, #stop-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        .loading {
          display: flex;
          flex: 1;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          color: var(--vscode-descriptionForeground);
        }
        .mode-controls {
          padding: 8px 16px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
        .mode-button {
          padding: 4px 12px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          background: rgba(255, 255, 255, 0.05);
          color: var(--vscode-foreground);
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }
        .mode-button.active {
          background: #0e639c;
          border-color: #0e639c;
        }
        .settings-button {
          position: absolute;
          top: 8px;
          right: 8px;
          background: none;
          border: none;
          color: var(--vscode-foreground);
          cursor: pointer;
          font-size: 16px;
          padding: 4px;
        }
        .settings-panel {
          display: none;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: var(--vscode-editor-background);
          z-index: 1000;
          padding: 16px;
          overflow-y: auto;
        }
        .settings-panel.show {
          display: block;
        }
        .settings-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }
        .settings-title {
          font-size: 18px;
          font-weight: bold;
        }
        .close-button {
          background: none;
          border: none;
          color: var(--vscode-foreground);
          cursor: pointer;
          font-size: 20px;
          padding: 4px;
        }
        .settings-group {
          margin-bottom: 16px;
        }
        .settings-label {
          display: block;
          margin-bottom: 4px;
          font-weight: bold;
        }
        .settings-input {
          width: 100%;
          padding: 8px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          background-color: rgba(255, 255, 255, 0.05);
          color: var(--vscode-foreground);
          font-family: var(--vscode-font-family);
        }
        .settings-button-group {
          display: flex;
          gap: 8px;
          justify-content: flex-end;
        }
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        }
        .btn-primary {
          background-color: #0e639c;
          color: white;
        }
        .btn-secondary {
          background-color: rgba(255, 255, 255, 0.1);
          color: var(--vscode-foreground);
        }
        .message {
          margin: 12px 0;
          padding: 12px;
          border-radius: 4px;
          line-height: 1.5;
        }
        .user-message {
          background-color: rgba(14, 99, 156, 0.15);
          margin-left: 20%;
        }
        .assistant-message {
          background-color: rgba(255, 255, 255, 0.05);
          margin-right: 20%;
        }
        .message-timestamp {
          font-size: 11px;
          color: var(--vscode-descriptionForeground);
          margin-top: 4px;
          text-align: right;
        }
      </style>
    </head>
    <body>
      <div id="app">
        <button class="settings-button" id="settings-button" title="Settings">⚙️</button>

        <div id="mode-controls" class="mode-controls">
          <!-- Mode buttons will be added by JavaScript -->
        </div>
        <div class="chat-container">
          <div id="chat-messages">
            <div class="loading">Loading Vidyadhara AI Chat...</div>
          </div>
        </div>
        <div class="input-container">
          <div class="input-row">
            <textarea 
              id="message-input" 
              placeholder="Type your message here..." 
              rows="1"
              autofocus
            ></textarea>
            <button id="send-button">Send</button>
            <button id="stop-button">Stop</button>
          </div>
        </div>
      </div>

      <!-- Templates -->
      <div id="settings-panel" class="settings-panel">
        <div class="settings-header">
          <div class="settings-title">Settings</div>
          <button id="close-settings-button" class="close-button">×</button>
        </div>

        <div class="settings-group">
          <label class="settings-label" for="github-token-input">GitHub Token</label>
          <input type="password" id="github-token-input" class="settings-input" placeholder="Enter your GitHub token">
        </div>

        <div class="settings-group">
          <label class="settings-label" for="github-owner-input">GitHub Owner</label>
          <input type="text" id="github-owner-input" class="settings-input" placeholder="Enter GitHub owner/organization">
        </div>

        <div class="settings-group">
          <label class="settings-label" for="github-repo-input">GitHub Repository</label>
          <input type="text" id="github-repo-input" class="settings-input" placeholder="Enter GitHub repository name">
        </div>

        <div class="settings-group">
          <label class="settings-label" for="github-branch-input">Default Branch</label>
          <input type="text" id="github-branch-input" class="settings-input" placeholder="Enter default branch (e.g., main)">
        </div>

        <div class="settings-group">
          <label class="settings-label" for="model-select">Default Model</label>
          <select id="model-select" class="settings-input">
            <option value="anthropic/claude-3-opus-20240229">Claude 3 Opus</option>
            <option value="anthropic/claude-3-sonnet-20240229">Claude 3 Sonnet</option>
            <option value="anthropic/claude-3-haiku-20240307">Claude 3 Haiku</option>
            <option value="openai/gpt-4o">GPT-4o</option>
            <option value="openai/gpt-4-turbo">GPT-4 Turbo</option>
            <option value="google/gemini-1.5-pro">Gemini 1.5 Pro</option>
          </select>
        </div>

        <div class="settings-button-group">
          <button id="save-settings-button" class="btn btn-primary">Save Settings</button>
        </div>
      </div>

      <template id="user-message-template">
        <div class="message user-message">
          <div class="message-content"></div>
          <div class="message-timestamp"></div>
        </div>
      </template>
      
      <template id="assistant-message-template">
        <div class="message assistant-message">
          <div class="message-content"></div>
          <div class="message-timestamp"></div>
        </div>
      </template>

      <script nonce="${nonce}">
        // Store the acquireVsCodeApi function
        const vscode = acquireVsCodeApi();
        
        // Function to update the active mode UI
        function updateActiveMode(message) {
          const modeControls = document.getElementById('mode-controls');
          if (!modeControls) return;
          
          // Clear existing mode buttons
          modeControls.innerHTML = '';
          
          // Add mode buttons if available
          if (message.availableModes && Array.isArray(message.availableModes)) {
            message.availableModes.forEach(mode => {
              const button = document.createElement('button');
              button.className = 'mode-button ' + (mode.id === message.modeId ? 'active' : '');
              button.textContent = mode.name || mode.id;
              button.title = mode.description || '';
              button.onclick = () => {
                vscode.postMessage({
                  command: 'setMode',
                  mode: mode.id
                });
              };
              modeControls.appendChild(button);
            });
          }
        }

        // Initialize state
        vscode.setState({ 
          initialized: false,
          isWaitingForResponse: false,
          currentMode: 'chat',
          modes: []
        });

        // Settings panel functionality
        const settingsButton = document.getElementById('settings-button');
        const settingsPanel = document.getElementById('settings-panel');
        const closeSettingsButton = document.getElementById('close-settings-button');
        const saveSettingsButton = document.getElementById('save-settings-button');

        // GitHub token input
        const githubTokenInput = document.getElementById('github-token-input');
        const githubOwnerInput = document.getElementById('github-owner-input');
        const githubRepoInput = document.getElementById('github-repo-input');
        const githubBranchInput = document.getElementById('github-branch-input');
        const modelSelect = document.getElementById('model-select');

        // Toggle settings panel
        settingsButton.addEventListener('click', () => {
          settingsPanel.classList.add('show');
          // Request current settings
          vscode.postMessage({ command: 'getSettings' });
        });

        // Close settings panel
        closeSettingsButton.addEventListener('click', () => {
          settingsPanel.classList.remove('show');
        });

        // Save settings
        saveSettingsButton.addEventListener('click', () => {
          const settings = {
            githubToken: githubTokenInput.value,
            githubOwner: githubOwnerInput.value,
            githubRepo: githubRepoInput.value,
            githubBranch: githubBranchInput.value,
            defaultModel: modelSelect.value
          };

          vscode.postMessage({
            command: 'saveSettings',
            settings: settings
          });

          // Close settings panel
          settingsPanel.classList.remove('show');
        });

        // Notify the extension that the webview is ready
        window.addEventListener('load', () => {
          console.log('Webview loaded, sending webviewReady');
          vscode.postMessage({ command: 'webviewReady' });
        });

        // Handle messages from the extension
        window.addEventListener('message', event => {
          const message = event.data;
          console.log('Webview received message:', message);
          
          switch (message.type) {
            case 'initialized':
              console.log('Webview initialized');
              const loadingEl = document.querySelector('.loading');
              if (loadingEl) loadingEl.style.display = 'none';
              break;
              
            case 'updateActiveMode':
              updateActiveMode(message);
              break;

            case 'settings':
              // Populate settings form
              if (message.settings) {
                githubTokenInput.value = message.settings.githubToken || '';
                githubOwnerInput.value = message.settings.githubOwner || '';
                githubRepoInput.value = message.settings.githubRepo || '';
                githubBranchInput.value = message.settings.githubBranch || 'main';
                modelSelect.value = message.settings.defaultModel || 'anthropic/claude-3-opus-20240229';
              }
              break;

            case 'settingsSaved':
              console.log('Settings saved successfully');
              break;

            case 'error':
              console.error('Error from extension:', message.message);
              break;

            default:
              console.log('Unhandled message type:', message.type);
          }
        });
      </script>
      <script nonce="${nonce}" src="${scriptUri}"></script>
    </body>
    </html>`;
  }
}
