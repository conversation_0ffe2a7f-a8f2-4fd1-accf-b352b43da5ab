import * as vscode from 'vscode';

export interface Task {
  id: string;
  name: string;
  description: string;
  state: TaskState;
  priority: TaskPriority;
  parentId?: string;
  childIds: string[];
  dependencies: string[];
  assignee?: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  estimatedHours?: number;
  actualHours?: number;
  tags: string[];
  metadata: {
    filePaths: string[];
    symbols: string[];
    commits: string[];
    branch?: string;
  };
}

export enum TaskState {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  BLOCKED = 'BLOCKED',
  REVIEW = 'REVIEW',
  COMPLETE = 'COMPLETE',
  CANCELLED = 'CANCELLED'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  subtasks: Array<{
    name: string;
    description: string;
    estimatedHours?: number;
  }>;
  estimatedHours?: number;
  tags: string[];
}

export interface TaskFilter {
  states?: TaskState[];
  priorities?: TaskPriority[];
  assignees?: string[];
  tags?: string[];
  dueDateRange?: { start?: Date; end?: Date };
  textSearch?: string;
  hasParent?: boolean;
  hasChildren?: boolean;
}

export interface TaskStatistics {
  total: number;
  byState: Map<TaskState, number>;
  byPriority: Map<TaskPriority, number>;
  completionRate: number;
  averageCompletionTime: number;
  overdueTasks: number;
}

export class TaskManager {
  private tasks: Map<string, Task> = new Map();
  private templates: Map<string, TaskTemplate> = new Map();
  private context: vscode.ExtensionContext;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
    this.loadTasks();
    this.loadTemplates();
  }

  /**
   * Create a new task
   */
  async createTask(taskData: Partial<Task>): Promise<Task> {
    const task: Task = {
      id: this.generateTaskId(),
      name: taskData.name || 'Untitled Task',
      description: taskData.description || '',
      state: taskData.state || TaskState.NOT_STARTED,
      priority: taskData.priority || TaskPriority.MEDIUM,
      parentId: taskData.parentId,
      childIds: [],
      dependencies: taskData.dependencies || [],
      assignee: taskData.assignee,
      dueDate: taskData.dueDate,
      createdAt: new Date(),
      updatedAt: new Date(),
      estimatedHours: taskData.estimatedHours,
      actualHours: taskData.actualHours,
      tags: taskData.tags || [],
      metadata: {
        filePaths: taskData.metadata?.filePaths || [],
        symbols: taskData.metadata?.symbols || [],
        commits: taskData.metadata?.commits || [],
        branch: taskData.metadata?.branch
      }
    };

    this.tasks.set(task.id, task);

    // Update parent task if specified
    if (task.parentId) {
      const parent = this.tasks.get(task.parentId);
      if (parent) {
        parent.childIds.push(task.id);
        parent.updatedAt = new Date();
      }
    }

    await this.saveTasks();
    this.notifyTaskCreated(task);

    return task;
  }

  /**
   * Update an existing task
   */
  async updateTask(taskId: string, updates: Partial<Task>): Promise<Task> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    const oldState = task.state;
    
    // Apply updates
    Object.assign(task, updates, {
      id: task.id, // Prevent ID changes
      updatedAt: new Date()
    });

    // Handle state transitions
    if (updates.state && updates.state !== oldState) {
      await this.handleStateTransition(task, oldState, updates.state);
    }

    await this.saveTasks();
    this.notifyTaskUpdated(task, oldState);

    return task;
  }

  /**
   * Delete a task and all its subtasks
   */
  async deleteTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    // Delete all subtasks recursively
    for (const childId of task.childIds) {
      await this.deleteTask(childId);
    }

    // Remove from parent's children list
    if (task.parentId) {
      const parent = this.tasks.get(task.parentId);
      if (parent) {
        parent.childIds = parent.childIds.filter(id => id !== taskId);
        parent.updatedAt = new Date();
      }
    }

    // Remove dependencies
    this.removeDependencies(taskId);

    this.tasks.delete(taskId);
    await this.saveTasks();
    this.notifyTaskDeleted(task);
  }

  /**
   * Get task by ID
   */
  getTask(taskId: string): Task | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * Get all tasks matching filter criteria
   */
  getTasks(filter?: TaskFilter): Task[] {
    let tasks = Array.from(this.tasks.values());

    if (!filter) {
      return tasks;
    }

    if (filter.states) {
      tasks = tasks.filter(task => filter.states!.includes(task.state));
    }

    if (filter.priorities) {
      tasks = tasks.filter(task => filter.priorities!.includes(task.priority));
    }

    if (filter.assignees) {
      tasks = tasks.filter(task => task.assignee && filter.assignees!.includes(task.assignee));
    }

    if (filter.tags && filter.tags.length > 0) {
      tasks = tasks.filter(task => 
        filter.tags!.some(tag => task.tags.includes(tag))
      );
    }

    if (filter.dueDateRange) {
      tasks = tasks.filter(task => {
        if (!task.dueDate) return false;
        
        const { start, end } = filter.dueDateRange!;
        if (start && task.dueDate < start) return false;
        if (end && task.dueDate > end) return false;
        
        return true;
      });
    }

    if (filter.textSearch) {
      const searchTerm = filter.textSearch.toLowerCase();
      tasks = tasks.filter(task =>
        task.name.toLowerCase().includes(searchTerm) ||
        task.description.toLowerCase().includes(searchTerm) ||
        task.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    if (filter.hasParent !== undefined) {
      tasks = tasks.filter(task => 
        filter.hasParent ? !!task.parentId : !task.parentId
      );
    }

    if (filter.hasChildren !== undefined) {
      tasks = tasks.filter(task =>
        filter.hasChildren ? task.childIds.length > 0 : task.childIds.length === 0
      );
    }

    return tasks;
  }

  /**
   * Get task hierarchy starting from root tasks
   */
  getTaskHierarchy(): Task[] {
    const rootTasks = this.getTasks({ hasParent: false });
    return this.buildTaskTree(rootTasks);
  }

  /**
   * Get task statistics
   */
  getTaskStatistics(filter?: TaskFilter): TaskStatistics {
    const tasks = this.getTasks(filter);
    
    const byState = new Map<TaskState, number>();
    const byPriority = new Map<TaskPriority, number>();
    
    // Initialize maps
    Object.values(TaskState).forEach(state => byState.set(state, 0));
    Object.values(TaskPriority).forEach(priority => byPriority.set(priority, 0));

    let totalCompletionTime = 0;
    let completedTasksWithTime = 0;
    let overdueTasks = 0;
    const now = new Date();

    for (const task of tasks) {
      // Count by state
      byState.set(task.state, (byState.get(task.state) || 0) + 1);
      
      // Count by priority
      byPriority.set(task.priority, (byPriority.get(task.priority) || 0) + 1);

      // Calculate completion time
      if (task.state === TaskState.COMPLETE && task.completedAt) {
        const completionTime = task.completedAt.getTime() - task.createdAt.getTime();
        totalCompletionTime += completionTime;
        completedTasksWithTime++;
      }

      // Count overdue tasks
      if (task.dueDate && task.dueDate < now && task.state !== TaskState.COMPLETE) {
        overdueTasks++;
      }
    }

    const completedTasks = byState.get(TaskState.COMPLETE) || 0;
    const completionRate = tasks.length > 0 ? completedTasks / tasks.length : 0;
    const averageCompletionTime = completedTasksWithTime > 0 
      ? totalCompletionTime / completedTasksWithTime 
      : 0;

    return {
      total: tasks.length,
      byState,
      byPriority,
      completionRate,
      averageCompletionTime,
      overdueTasks
    };
  }

  /**
   * Create task from template
   */
  async createTaskFromTemplate(templateId: string, overrides?: Partial<Task>): Promise<Task> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    const mainTask = await this.createTask({
      name: template.name,
      description: template.description,
      estimatedHours: template.estimatedHours,
      tags: template.tags,
      ...overrides
    });

    // Create subtasks
    for (const subtaskTemplate of template.subtasks) {
      await this.createTask({
        name: subtaskTemplate.name,
        description: subtaskTemplate.description,
        estimatedHours: subtaskTemplate.estimatedHours,
        parentId: mainTask.id
      });
    }

    return mainTask;
  }

  /**
   * Private helper methods
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async handleStateTransition(task: Task, oldState: TaskState, newState: TaskState): Promise<void> {
    // Set completion time
    if (newState === TaskState.COMPLETE && oldState !== TaskState.COMPLETE) {
      task.completedAt = new Date();
    } else if (newState !== TaskState.COMPLETE && oldState === TaskState.COMPLETE) {
      task.completedAt = undefined;
    }

    // Auto-update parent task state based on children
    if (task.parentId) {
      await this.updateParentTaskState(task.parentId);
    }

    // Check dependencies
    if (newState === TaskState.COMPLETE) {
      await this.checkDependentTasks(task.id);
    }
  }

  private async updateParentTaskState(parentId: string): Promise<void> {
    const parent = this.tasks.get(parentId);
    if (!parent) return;

    const children = parent.childIds.map(id => this.tasks.get(id)).filter(Boolean) as Task[];
    
    if (children.length === 0) return;

    const allComplete = children.every(child => child.state === TaskState.COMPLETE);
    const anyInProgress = children.some(child => child.state === TaskState.IN_PROGRESS);
    const anyBlocked = children.some(child => child.state === TaskState.BLOCKED);

    let newState = parent.state;

    if (allComplete && parent.state !== TaskState.COMPLETE) {
      newState = TaskState.COMPLETE;
    } else if (anyBlocked && parent.state !== TaskState.BLOCKED) {
      newState = TaskState.BLOCKED;
    } else if (anyInProgress && parent.state === TaskState.NOT_STARTED) {
      newState = TaskState.IN_PROGRESS;
    }

    if (newState !== parent.state) {
      await this.updateTask(parentId, { state: newState });
    }
  }

  private async checkDependentTasks(completedTaskId: string): Promise<void> {
    // Find tasks that depend on this completed task
    const dependentTasks = Array.from(this.tasks.values())
      .filter(task => task.dependencies.includes(completedTaskId));

    for (const task of dependentTasks) {
      // Check if all dependencies are complete
      const allDependenciesComplete = task.dependencies.every(depId => {
        const depTask = this.tasks.get(depId);
        return depTask && depTask.state === TaskState.COMPLETE;
      });

      // If all dependencies are complete and task is blocked, unblock it
      if (allDependenciesComplete && task.state === TaskState.BLOCKED) {
        await this.updateTask(task.id, { state: TaskState.NOT_STARTED });
      }
    }
  }

  private removeDependencies(taskId: string): void {
    // Remove this task from other tasks' dependencies
    for (const task of this.tasks.values()) {
      if (task.dependencies.includes(taskId)) {
        task.dependencies = task.dependencies.filter(id => id !== taskId);
        task.updatedAt = new Date();
      }
    }
  }

  private buildTaskTree(tasks: Task[]): Task[] {
    return tasks.map(task => ({
      ...task,
      children: this.buildTaskTree(
        task.childIds.map(id => this.tasks.get(id)).filter(Boolean) as Task[]
      )
    })) as any;
  }

  private async loadTasks(): Promise<void> {
    try {
      const tasksJson = this.context.globalState.get<string>('tasks');
      if (tasksJson) {
        const tasksData = JSON.parse(tasksJson);
        this.tasks = new Map(tasksData.map((task: any) => [task.id, {
          ...task,
          createdAt: new Date(task.createdAt),
          updatedAt: new Date(task.updatedAt),
          completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
          dueDate: task.dueDate ? new Date(task.dueDate) : undefined
        }]));
      }
    } catch (error) {
      console.error('Failed to load tasks:', error);
    }
  }

  private async saveTasks(): Promise<void> {
    try {
      const tasksArray = Array.from(this.tasks.values());
      await this.context.globalState.update('tasks', JSON.stringify(tasksArray));
    } catch (error) {
      console.error('Failed to save tasks:', error);
    }
  }

  private async loadTemplates(): Promise<void> {
    try {
      const templatesJson = this.context.globalState.get<string>('taskTemplates');
      if (templatesJson) {
        const templatesData = JSON.parse(templatesJson);
        this.templates = new Map(templatesData.map((template: TaskTemplate) => [template.id, template]));
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  }

  private notifyTaskCreated(task: Task): void {
    vscode.window.showInformationMessage(`Task created: ${task.name}`);
  }

  private notifyTaskUpdated(task: Task, oldState: TaskState): void {
    if (task.state !== oldState) {
      vscode.window.showInformationMessage(`Task "${task.name}" moved to ${task.state}`);
    }
  }

  private notifyTaskDeleted(task: Task): void {
    vscode.window.showInformationMessage(`Task deleted: ${task.name}`);
  }

  /**
   * Validate state transition
   */
  validateStateTransition(currentState: TaskState, newState: TaskState): { valid: boolean; reason?: string } {
    const validTransitions: Map<TaskState, TaskState[]> = new Map([
      [TaskState.NOT_STARTED, [TaskState.IN_PROGRESS, TaskState.BLOCKED, TaskState.CANCELLED]],
      [TaskState.IN_PROGRESS, [TaskState.COMPLETE, TaskState.BLOCKED, TaskState.REVIEW, TaskState.CANCELLED]],
      [TaskState.BLOCKED, [TaskState.NOT_STARTED, TaskState.IN_PROGRESS, TaskState.CANCELLED]],
      [TaskState.REVIEW, [TaskState.COMPLETE, TaskState.IN_PROGRESS, TaskState.CANCELLED]],
      [TaskState.COMPLETE, [TaskState.IN_PROGRESS, TaskState.REVIEW]], // Allow reopening
      [TaskState.CANCELLED, [TaskState.NOT_STARTED]] // Allow reactivation
    ]);

    const allowedStates = validTransitions.get(currentState) || [];

    if (!allowedStates.includes(newState)) {
      return {
        valid: false,
        reason: `Cannot transition from ${currentState} to ${newState}`
      };
    }

    return { valid: true };
  }

  /**
   * Get task progress percentage
   */
  getTaskProgress(taskId: string): number {
    const task = this.tasks.get(taskId);
    if (!task) return 0;

    if (task.childIds.length === 0) {
      // Leaf task - progress based on state
      switch (task.state) {
        case TaskState.NOT_STARTED: return 0;
        case TaskState.IN_PROGRESS: return 50;
        case TaskState.BLOCKED: return 25;
        case TaskState.REVIEW: return 90;
        case TaskState.COMPLETE: return 100;
        case TaskState.CANCELLED: return 0;
        default: return 0;
      }
    } else {
      // Parent task - progress based on children
      const children = task.childIds.map(id => this.tasks.get(id)).filter(Boolean) as Task[];
      if (children.length === 0) return 0;

      const totalProgress = children.reduce((sum, child) => sum + this.getTaskProgress(child.id), 0);
      return totalProgress / children.length;
    }
  }

  /**
   * Track time spent on task
   */
  async startTimeTracking(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    // Store start time in metadata
    task.metadata = {
      ...task.metadata,
      timeTrackingStart: new Date().toISOString()
    } as any;

    // Auto-transition to IN_PROGRESS if not already
    if (task.state === TaskState.NOT_STARTED) {
      await this.updateTask(taskId, { state: TaskState.IN_PROGRESS });
    }

    await this.saveTasks();
  }

  /**
   * Stop time tracking and update actual hours
   */
  async stopTimeTracking(taskId: string): Promise<number> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    const startTimeStr = (task.metadata as any).timeTrackingStart;
    if (!startTimeStr) {
      throw new Error('Time tracking was not started for this task');
    }

    const startTime = new Date(startTimeStr);
    const endTime = new Date();
    const hoursSpent = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);

    // Update actual hours
    const newActualHours = (task.actualHours || 0) + hoursSpent;

    // Remove tracking start time
    const { timeTrackingStart, ...cleanMetadata } = task.metadata as any;

    await this.updateTask(taskId, {
      actualHours: newActualHours,
      metadata: cleanMetadata
    });

    return hoursSpent;
  }

  /**
   * Get tasks that are currently being time tracked
   */
  getActiveTimeTrackedTasks(): Task[] {
    return Array.from(this.tasks.values()).filter(task =>
      (task.metadata as any).timeTrackingStart
    );
  }

  /**
   * Auto-update task states based on dependencies
   */
  async updateDependentTaskStates(): Promise<void> {
    const allTasks = Array.from(this.tasks.values());

    for (const task of allTasks) {
      if (task.dependencies.length > 0) {
        const dependencyStates = task.dependencies.map(depId => {
          const depTask = this.tasks.get(depId);
          return depTask ? depTask.state : TaskState.CANCELLED;
        });

        const allDependenciesComplete = dependencyStates.every(state => state === TaskState.COMPLETE);
        const anyDependencyBlocked = dependencyStates.some(state => state === TaskState.BLOCKED);
        const anyDependencyCancelled = dependencyStates.some(state => state === TaskState.CANCELLED);

        let newState = task.state;

        if (anyDependencyCancelled && task.state !== TaskState.CANCELLED) {
          newState = TaskState.BLOCKED; // Block if dependency is cancelled
        } else if (anyDependencyBlocked && task.state !== TaskState.BLOCKED) {
          newState = TaskState.BLOCKED;
        } else if (allDependenciesComplete && task.state === TaskState.BLOCKED) {
          newState = TaskState.NOT_STARTED; // Unblock when dependencies complete
        }

        if (newState !== task.state) {
          await this.updateTask(task.id, { state: newState });
        }
      }
    }
  }

  /**
   * Get task state history (if we were tracking it)
   */
  getTaskStateHistory(taskId: string): Array<{ state: TaskState; timestamp: Date; duration?: number }> {
    // This would require storing state history in task metadata
    // For now, return empty array as placeholder
    return [];
  }

  /**
   * Calculate task velocity (tasks completed per time period)
   */
  calculateTaskVelocity(days: number = 30): { tasksPerDay: number; hoursPerDay: number } {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const completedTasks = Array.from(this.tasks.values()).filter(task =>
      task.state === TaskState.COMPLETE &&
      task.completedAt &&
      task.completedAt >= cutoffDate
    );

    const totalHours = completedTasks.reduce((sum, task) => sum + (task.actualHours || 0), 0);

    return {
      tasksPerDay: completedTasks.length / days,
      hoursPerDay: totalHours / days
    };
  }

  /**
   * Predict task completion date based on current progress and velocity
   */
  predictTaskCompletion(taskId: string): Date | null {
    const task = this.tasks.get(taskId);
    if (!task || task.state === TaskState.COMPLETE) return null;

    const progress = this.getTaskProgress(taskId);
    if (progress >= 100) return new Date(); // Already complete

    const velocity = this.calculateTaskVelocity();
    if (velocity.tasksPerDay === 0) return null; // No historical data

    const remainingWork = 100 - progress;
    const estimatedDaysRemaining = remainingWork / (velocity.tasksPerDay * 100);

    const completionDate = new Date();
    completionDate.setDate(completionDate.getDate() + estimatedDaysRemaining);

    return completionDate;
  }
}
