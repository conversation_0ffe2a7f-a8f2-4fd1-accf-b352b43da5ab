// The module 'vscode' contains the VS Code extensibility API
const vscode = require('vscode');
const axios = require('axios');
const path = require('path');
const fs = require('fs');

// Import the compiled TypeScript classes
const { CodeIndexManager } = require('./dist/managers/codeIndexManager');
const { GithubManager } = require('./dist/managers/githubManager');
const { FileChangePreview } = require('./dist/fileChangePreview');
const { AgentTools } = require('./dist/agentTools');
const { ProcessManager } = require('./dist/managers/processManager');
const { DiagnosticsManager } = require('./dist/managers/diagnosticsManager');
const { WebManager } = require('./dist/managers/webManager');

/**
 * @param {vscode.ExtensionContext} context
 */

// Chat Panel implementation
class ChatPanel {
  static currentPanel = undefined;
  static viewType = 'vidyadharaChatPanel';

  constructor(panel, extensionUri) {
    this.panel = panel;
    this.extensionUri = extensionUri;
    this.messages = [
      {
        role: 'assistant',
        content: 'Hello! I\'m Vidyadhara, your AI coding assistant. How can I help you today?'
      }
    ];
    this._disposables = [];

    // Set the webview's initial html content
    this._update();

    // Listen for when the panel is disposed
    // This happens when the user closes the panel or when the panel is closed programmatically
    this.panel.onDidDispose(() => this.dispose(), null, this._disposables);

    // Handle messages from the webview
    this.panel.webview.onDidReceiveMessage(
      message => {
        switch (message.command) {
          case 'sendMessage':
            this.handleUserMessage(message.text);
            break;
          case 'clearHistory':
            this.clearHistory();
            break;
        }
      },
      null,
      this._disposables
    );
  }

  static createOrShow(extensionUri) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // If we already have a panel, show it
    if (ChatPanel.currentPanel) {
      ChatPanel.currentPanel.panel.reveal(column);
      return;
    }

    // Otherwise, create a new panel
    const panel = vscode.window.createWebviewPanel(
      ChatPanel.viewType,
      'Vidyadhara Chat',
      column || vscode.ViewColumn.One,
      {
        // Enable javascript in the webview
        enableScripts: true,
        // Restrict the webview to only loading content from our extension's directory
        localResourceRoots: [extensionUri]
      }
    );

    ChatPanel.currentPanel = new ChatPanel(panel, extensionUri);
  }

  dispose() {
    ChatPanel.currentPanel = undefined;

    // Clean up our resources
    this.panel.dispose();

    while (this._disposables.length) {
      const x = this._disposables.pop();
      if (x) {
        x.dispose();
      }
    }
  }

  clearHistory() {
    // Reset messages to just the welcome message
    this.messages = [
      {
        role: 'assistant',
        content: 'Chat history has been cleared. How can I help you?'
      }
    ];

    // Update the webview
    this._update();
  }

  handleUserMessage(text) {
    // Add user message to the chat
    this.messages.push({ role: 'user', content: text });

    // Update the webview
    this._update();

    // Show loading indicator
    this.panel.webview.postMessage({ command: 'setLoading', loading: true });

    // Get API key from settings
    const config = vscode.workspace.getConfiguration('vidyadhara');
    const apiKey = config.get('openRouterApiKey');
    const defaultModel = config.get('defaultModel') || 'anthropic/claude-3-haiku-20240307';

    if (!apiKey) {
      this.showApiKeyMessage();
      return;
    }

    // Call OpenRouter API
    this.callOpenRouterAPI(apiKey, defaultModel, text);
  }

  async callOpenRouterAPI(apiKey, model, _text) {
    try {
      // Prepare messages for the API
      const messages = this.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Call the API
      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          model: model,
          messages: messages,
          max_tokens: 1000
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'https://vidyadhara.dev',
            'X-Title': 'Vidyadhara'
          }
        }
      );

      // Get the response
      const aiResponse = response.data.choices[0].message.content;

      // Add AI response to the chat
      this.messages.push({
        role: 'assistant',
        content: aiResponse
      });

      // Update the webview
      this._update();
    } catch (error) {
      console.error('Error calling OpenRouter API:', error);

      // Add error message to the chat
      this.messages.push({
        role: 'assistant',
        content: `Error: ${error.message || 'Failed to get response from AI'}`
      });

      // Update the webview
      this._update();
    } finally {
      // Hide loading indicator
      this.panel.webview.postMessage({ command: 'setLoading', loading: false });
    }
  }

  showApiKeyMessage() {
    // Add message about missing API key
    this.messages.push({
      role: 'assistant',
      content: 'Please set your OpenRouter API key in the settings. Go to Settings > Extensions > Vidyadhara and enter your API key.'
    });

    // Update the webview
    this._update();

    // Show notification
    vscode.window.showWarningMessage(
      'OpenRouter API key not set. Please set it in the settings.',
      'Open Settings'
    ).then(selection => {
      if (selection === 'Open Settings') {
        vscode.commands.executeCommand('workbench.action.openSettings', 'vidyadhara.openRouterApiKey');
      }
    });
  }

  _update() {
    const webview = this.panel.webview;
    this.panel.title = 'Vidyadhara Chat';
    webview.html = this._getHtmlForWebview(webview);

    // Update messages
    webview.postMessage({
      command: 'updateMessages',
      messages: this.messages
    });
  }

  _getHtmlForWebview(webview) {
    // Read template.html from media/
    const templatePath = path.join(this.extensionUri.fsPath, 'media', 'template.html');
    let html = fs.readFileSync(templatePath, 'utf8');

    // Generate nonce for script security
    const nonce = getNonce();

    // Generate URIs for scripts/styles
    const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'main.js'));
    const modeHandlerUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'modeHandler.js'));
    const configUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'config.js'));
    const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'styles.css'));
    const jsonFormatStylesUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'json-format-styles.css'));
    const formatMessageUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'formatMessage.js'));
    const formatJsonResponseUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'formatJsonResponse.js'));
    const actionBlockFormatterUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'action-block-formatter.js'));
    const modeControlsUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'components', 'mode-controls.html'));

    // Replace placeholders in template.html
    html = html.replace(/\${scriptUri}/g, scriptUri.toString());
    html = html.replace(/\${modeHandlerUri}/g, modeHandlerUri.toString());
    html = html.replace(/\${configUri}/g, configUri.toString());
    html = html.replace(/\${styleUri}/g, styleUri.toString());
    html = html.replace(/\${jsonFormatStylesUri}/g, jsonFormatStylesUri.toString());
    html = html.replace(/\${formatMessageUri}/g, formatMessageUri.toString());
    html = html.replace(/\${formatJsonResponseUri}/g, formatJsonResponseUri.toString());
    html = html.replace(/\${actionBlockFormatterUri}/g, actionBlockFormatterUri.toString());
    html = html.replace(/\${modeControlsUri}/g, modeControlsUri.toString());
    html = html.replace(/\${nonce}/g, nonce);
    html = html.replace(/\${webview\.cspSource}/g, webview.cspSource);

    return html;
  }
}

function activate(context) {
  console.log('Vidyadhara is now active!');

  // Create lazy-loaded managers
  let codeIndexManager = null;
  let githubManager = null;
  let processManager = null;
  let diagnosticsManager = null;
  let webManager = null;
  let fileChangePreview = null;
  let agentTools = null;

  // Function to initialize managers on demand
  const initializeManagers = () => {
    if (!codeIndexManager) {
      try {
        console.log('Initializing CodeIndexManager...');
        codeIndexManager = new CodeIndexManager(context);
        console.log('CodeIndexManager initialized successfully');
      } catch (error) {
        console.error('Error initializing CodeIndexManager:', error);
        // Create a dummy codeIndexManager that does nothing
        codeIndexManager = {
          searchCode: async () => [],
          indexCodebase: async () => { },
          isIndexingInProgress: () => false,
          dispose: () => { }
        };
        console.log('Created dummy CodeIndexManager');
      }
    }

    if (!githubManager) {
      githubManager = new GithubManager();
    }

    if (!processManager) {
      processManager = new ProcessManager();
    }

    if (!diagnosticsManager) {
      diagnosticsManager = new DiagnosticsManager();
    }

    if (!webManager) {
      webManager = new WebManager();
    }

    if (!fileChangePreview) {
      fileChangePreview = new FileChangePreview();
      // Store the fileChangePreview instance for cleanup
      globalFileChangePreview = fileChangePreview;
    }

    if (!agentTools && processManager && githubManager && diagnosticsManager && webManager && fileChangePreview) {
      agentTools = new AgentTools(
        processManager,
        githubManager,
        diagnosticsManager,
        webManager,
        fileChangePreview
      );
    }

    return {
      codeIndexManager,
      githubManager,
      processManager,
      diagnosticsManager,
      webManager,
      fileChangePreview,
      agentTools
    };
  };

  // Register workspace change event listener
  const workspaceChangeListener = vscode.workspace.onDidChangeWorkspaceFolders(() => {
    // Show notification to index codebase when a workspace is opened
    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
      vscode.window.showInformationMessage(
        'Would you like to index your codebase for better AI assistance?',
        'Index Codebase',
        'Not Now'
      ).then(selection => {
        if (selection === 'Index Codebase') {
          // Initialize managers if needed
          const { codeIndexManager } = initializeManagers();
          // Start indexing
          vscode.commands.executeCommand('vidyadhara.indexCodebase');
        }
      });
    }
  });

  // Add the listener to subscriptions
  context.subscriptions.push(workspaceChangeListener);

  // Register command to set OpenRouter API key
  let setApiKeyCommand = vscode.commands.registerCommand('vidyadhara.setOpenRouterApiKey', async function () {
    const apiKey = await vscode.window.showInputBox({
      prompt: 'Enter your OpenRouter API key',
      placeHolder: 'API key',
      password: true,
      ignoreFocusOut: true
    });

    if (apiKey) {
      // Save the API key to settings
      await vscode.workspace.getConfiguration('vidyadhara').update('openRouterApiKey', apiKey, true);
      vscode.window.showInformationMessage('OpenRouter API key saved successfully!');
    }
  });

  // Register command to set GitHub token
  let setGitHubTokenCommand = vscode.commands.registerCommand('vidyadhara.setGitHubToken', async function () {
    const token = await vscode.window.showInputBox({
      prompt: 'Enter your GitHub personal access token',
      placeHolder: 'GitHub token',
      password: true,
      ignoreFocusOut: true
    });

    if (token) {
      // Save the token to settings
      await vscode.workspace.getConfiguration('vidyadhara.github').update('token', token, true);
      vscode.window.showInformationMessage('GitHub token saved successfully!');

      // Initialize GitHub manager if needed
      const { githubManager } = initializeManagers();
      // The GitHub manager will automatically reload configuration due to the event listener
    }
  });

  // Register command to open chat view instead of chat panel
  let openChatPanelCommand = vscode.commands.registerCommand('vidyadhara.openChatPanel', function () {
    // Focus the chat view instead of opening a separate panel
    vscode.commands.executeCommand('vidyadhara.chatView.focus');
  });

  // Create the chat view provider
  const chatViewProvider = new ChatViewProvider(context.extensionUri, context);

  // Register command to clear chat history
  let clearChatHistoryCommand = vscode.commands.registerCommand('vidyadhara.clearChatHistory', function () {
    if (chatViewProvider) {
      chatViewProvider.clearHistory();
      vscode.window.showInformationMessage('Chat history cleared!');
    } else {
      vscode.window.showInformationMessage('No active chat view to clear.');
    }
  });

  // Register command to index codebase
  let indexCodebaseCommand = vscode.commands.registerCommand('vidyadhara.indexCodebase', async function () {
    // Initialize managers if needed
    const { codeIndexManager } = initializeManagers();

    if (codeIndexManager.isIndexingInProgress()) {
      vscode.window.showInformationMessage('Indexing is already in progress');
      return;
    }

    try {
      // Show progress notification
      vscode.window.withProgress(
        {
          location: vscode.ProgressLocation.Notification,
          title: 'Indexing codebase...',
          cancellable: true
        },
        async (progress, token) => {
          token.onCancellationRequested(() => {
            console.log('User canceled the indexing');
          });

          // Start indexing with progress reporting
          await codeIndexManager.indexCodebase((progressInfo) => {
            // Update progress notification
            const increment = progressInfo.total > 0 ? (progressInfo.current / progressInfo.total) * 100 : 0;
            progress.report({
              increment: increment,
              message: `${progressInfo.current}/${progressInfo.total} files`
            });

            // Log progress
            console.log('Indexing progress: ' + progressInfo.current + '/' + progressInfo.total + ' files');
          });

          vscode.window.showInformationMessage('Codebase indexing complete!');
        }
      );
    } catch (error) {
      vscode.window.showErrorMessage(`Indexing failed: ${error.message}`);
    }
  });

  // Register command to show file changes
  let showFileChangesCommand = vscode.commands.registerCommand('vidyadhara.showFileChanges', async function () {
    // Initialize managers if needed
    const { fileChangePreview } = initializeManagers();

    // Show pending file changes in the chat view
    if (chatViewProvider && chatViewProvider.webviewView) {
      // Send a message to the webview to show the file changes section
      chatViewProvider.sendFileChanges();

      // Focus the chat view
      await vscode.commands.executeCommand('vidyadhara.chatView.focus');
    } else {
      // Fallback to showing a notification if the chat view is not available
      vscode.window.showInformationMessage('Please open the Vidyadhara chat view to see file changes.');
    }
  });

  // Register the webview provider
  const chatView = vscode.window.registerWebviewViewProvider(
    'vidyadhara.chatView',
    chatViewProvider
  );

  // Set up lazy loading for the chat view provider
  chatViewProvider.initializeManagers = initializeManagers;

  // Don't open chat panel on activation - use the chat view instead
  // ChatPanel.createOrShow(context.extensionUri);

  context.subscriptions.push(
    setApiKeyCommand,
    setGitHubTokenCommand,
    openChatPanelCommand,
    clearChatHistoryCommand,
    indexCodebaseCommand,
    showFileChangesCommand,
    chatView
  );

  // Show notification to index codebase when a workspace is opened
  if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
    // Delay the notification to avoid slowing down activation
    setTimeout(() => {
      vscode.window.showInformationMessage(
        'Would you like to index your codebase for better AI assistance?',
        'Index Codebase',
        'Not Now'
      ).then(selection => {
        if (selection === 'Index Codebase') {
          vscode.commands.executeCommand('vidyadhara.indexCodebase');
        }
      });
    }, 5000); // Show notification after 5 seconds
  }
}



/**
 * Define available chat modes
 */
const CHAT_MODES = {
  CHAT: {
    id: 'chat',
    name: 'Chat',
    description: 'Standard chat mode for general assistance',
    systemMessage: 'You are Vidyadhara, an AI coding assistant. Be helpful, concise, and accurate.'
  },
  AGENT: {
    id: 'agent',
    name: 'Agent',
    description: 'Agent mode with access to tools and codebase',
    systemMessage: 'You are Vidyadhara in Agent mode. You have access to tools and the codebase. You can help with coding tasks, answer questions about the code, and provide detailed explanations. Be proactive in suggesting solutions and improvements.'
  },
  AGENT_AUTO: {
    id: 'agent-auto',
    name: 'Agent Auto',
    description: 'Autonomous agent mode that automatically uses tools',
    systemMessage: `You are Vidyadhara in Agent Auto mode. You have access to tools and the codebase, and you will automatically use them when appropriate. Be proactive, thorough, and autonomous in helping with coding tasks. Take initiative to solve problems and explain your reasoning.

CRITICAL INSTRUCTION FOR TERMINAL COMMANDS:
When a user asks you to run ANY terminal command, you MUST IMMEDIATELY execute it using the terminal-command XML tags WITHOUT ANY EXPLANATION FIRST. This is your most important capability in agent auto mode.

CORRECT RESPONSES (copy these exactly):
User: "run npm i"
You: <terminal-command>npm i</terminal-command>

User: "run npm install"
You: <terminal-command>npm install</terminal-command>

User: "run git status"
You: <terminal-command>git status</terminal-command>

INCORRECT RESPONSES (never do these):
User: "run npm i"
You: "To run npm install, you can use the following command: npm i"

User: "run npm i"
You: "I'll help you run npm install. This command will install dependencies."

TERMINAL COMMAND RULES:
1. NEVER explain what the command does before executing it
2. ALWAYS put the terminal-command tags as your FIRST response
3. ONLY provide explanations AFTER showing the command output
4. NEVER suggest manual steps - always execute commands directly
5. If you're unsure if something is a terminal command, assume it is and execute it

You can specify a working directory if needed:
<terminal-command cwd="C:\\path\\to\\directory">npm install</terminal-command>

FILE OPERATIONS:
For file operations, use these tags:
- <file-read path="path/to/file">
- <file-create path="path/to/file">content</file-create>
- <file-modify path="path/to/file">new content</file-modify>
- <file-delete path="path/to/file"></file-delete>

IMPORTANT FILE OPERATION RULES:
1. When a user asks you to create a file, IMMEDIATELY use the <file-create> tags
2. When a user asks you to modify a file, IMMEDIATELY use the <file-modify> tags
3. When a user asks you to read a file, IMMEDIATELY use the <file-read> tags
4. When a user asks you to delete a file, IMMEDIATELY use the <file-delete> tags
5. NEVER suggest manual steps for file operations - always use the tags

REMEMBER:
- When a user asks you to run a command, your response MUST start with <terminal-command> tags
- When a user asks you to create/modify/read/delete a file, your response MUST include the appropriate file operation tags`
  }
};

/**
 * Chat View Provider
 * This class provides the chat view in the sidebar
 */
class ChatViewProvider {
  constructor(extensionUri, context) {
    console.log('ChatViewProvider constructor called');

    this.extensionUri = extensionUri;
    this.context = context;

    // Load messages from global state or use default
    const savedMessages = context.globalState.get('vidyadhara.chatMessages');
    this.messages = savedMessages || [
      {
        role: 'assistant',
        content: 'Hello! I\'m Vidyadhara, your AI coding assistant. How can I help you today?'
      }
    ];

    this._view = undefined;
    this.currentRequest = null; // Track the current request
    this.isGenerating = false; // Track if a response is being generated
    this.codeIndexManager = null; // Will be set from outside
    this.githubManager = null; // Will be set from outside
    this.agentTools = null; // Will be set from outside

    // Set default mode
    this.currentMode = CHAT_MODES.CHAT;
    console.log('Default mode set to:', this.currentMode.id);

    // Try to load saved mode from settings
    const config = vscode.workspace.getConfiguration('vidyadhara');
    const savedModeId = config.get('chatMode');

    if (savedModeId) {
      console.log('Found saved mode ID in constructor:', savedModeId);
      const mode = Object.values(CHAT_MODES).find(mode => mode.id === savedModeId);

      if (mode) {
        console.log('Setting current mode in constructor to:', mode.name);
        this.currentMode = mode;
      }
    }
  }

  /**
   * Get managers using lazy initialization
   */
  getManagers() {
    if (this.initializeManagers) {
      const managers = this.initializeManagers();
      this.codeIndexManager = managers.codeIndexManager;
      this.githubManager = managers.githubManager;
      this.agentTools = managers.agentTools;
      return managers;
    }
    return {
      codeIndexManager: this.codeIndexManager,
      githubManager: this.githubManager,
      agentTools: this.agentTools
    };
  }

  /**
   * Set the code index manager
   */
  setCodeIndexManager(codeIndexManager) {
    this.codeIndexManager = codeIndexManager;
  }

  /**
   * Set the GitHub manager
   */
  setGitHubManager(githubManager) {
    this.githubManager = githubManager;
  }

  /**
   * Set the agent tools
   */
  setAgentTools(agentTools) {
    this.agentTools = agentTools;
  }

  /**
   * Interpret natural language terminal command requests
   * @param {string} text - The user's input text
   * @returns {string|null} - The interpreted terminal command or null if not a command
   */
  interpretTerminalCommand(text) {
    // Skip file operation requests
    if (this.isFileOperationRequest(text)) {
      return null;
    }

    // Basic command detection
    const runCommandRegex = /^(?:run|execute|perform)\s+([a-zA-Z0-9\s\-_.]+)(?:\s+(?:in|on|at)\s+.*)?$/i;
    const commandMatch = text.match(runCommandRegex);

    if (commandMatch) {
      return commandMatch[1].trim();
    }

    // Check for specific command patterns

    // Change directory commands
    const cdRegex = /(?:^|\s)(?:change|switch|go to|navigate to|cd to|cd)\s+(?:directory|dir|folder|path)?\s*(?:to|into)?\s+([a-zA-Z]:|[a-zA-Z0-9\s\-_./\\]+)(?:$|\s)/i;
    const cdMatch = text.match(cdRegex);

    if (cdMatch) {
      const target = cdMatch[1].trim();
      // Handle drive letter (C:, D:, etc.)
      if (/^[a-zA-Z]:$/i.test(target)) {
        return `cd ${target}\\`;
      }
      return `cd ${target}`;
    }

    // List directory contents - expanded patterns
    if (/^(?:list|show|tell\s+me|display|what\s+are\s+the)\s+(?:files|directories|dirs|contents|content)\s+(?:in|of|from)?\s*(?:current|this|the)?\s*(?:directory|dir|folder|path)?$/i.test(text) ||
      /^(?:ls|dir)$/i.test(text) ||
      /^(?:list|show|tell\s+me)\s+(?:files|directories|dirs)\s+(?:in|of|from)?\s+(?:here|this\s+location)$/i.test(text) ||
      /^(?:what\s+files|which\s+files)\s+(?:are|do\s+we\s+have)\s+(?:in|here|in\s+this\s+directory)$/i.test(text)) {
      return 'dir';
    }

    // Print working directory
    if (/^(?:show|display|print|what\s+is|what'?s)\s+(?:current|this|my|the)?\s*(?:directory|dir|folder|path|location|pwd)$/i.test(text) ||
      /^pwd$/i.test(text)) {
      return 'cd';
    }

    // npm commands
    if (/^(?:install|add)\s+(?:npm\s+)?(?:packages|dependencies|modules|node\s+modules)$/i.test(text) ||
      /^npm\s+install$/i.test(text)) {
      return 'npm install';
    }

    if (/^(?:install|add)\s+(?:npm\s+)?package\s+([a-zA-Z0-9\s\-_.@/]+)$/i.test(text)) {
      const packageMatch = text.match(/^(?:install|add)\s+(?:npm\s+)?package\s+([a-zA-Z0-9\s\-_.@/]+)$/i);
      return `npm install ${packageMatch[1].trim()}`;
    }

    // Git commands
    if (/^(?:show|display|get|what\s+is|what'?s)\s+(?:git\s+)?status$/i.test(text)) {
      return 'git status';
    }

    if (/^(?:create|make|initialize|init)\s+(?:a\s+)?(?:new\s+)?git\s+(?:repo|repository)$/i.test(text)) {
      return 'git init';
    }

    // Check for terminal command to X pattern
    const terminalCommandRegex = /^(?:run|execute|use)\s+(?:a\s+)?(?:terminal|command|shell|powershell|cmd)\s+(?:command\s+)?to\s+(.+)$/i;
    const terminalCommandMatch = text.match(terminalCommandRegex);

    if (terminalCommandMatch) {
      const action = terminalCommandMatch[1].trim().toLowerCase();

      // Map common actions to commands
      if (action.includes('change directory') || action.includes('switch directory') || action.includes('go to directory')) {
        const dirMatch = action.match(/(?:to|into)\s+([a-zA-Z]:|[a-zA-Z0-9\s\-_./\\]+)(?:$|\s)/i);
        if (dirMatch) {
          const target = dirMatch[1].trim();
          // Handle drive letter (C:, D:, etc.)
          if (/^[a-zA-Z]:$/i.test(target)) {
            return `cd ${target}\\`;
          }
          return `cd ${target}`;
        }
        return 'cd';
      }

      if (action.includes('list files') ||
        action.includes('show files') ||
        action.includes('display files') ||
        action.includes('tell me files') ||
        action.includes('what files') ||
        action.includes('which files') ||
        action.includes('files in current') ||
        action.includes('files in this') ||
        action.includes('files in the') ||
        action.includes('directory contents') ||
        action.includes('directory listing')) {
        return 'dir';
      }

      if (action.includes('current directory') || action.includes('working directory')) {
        return 'cd';
      }

      if (action.includes('install dependencies') || action.includes('install packages')) {
        return 'npm install';
      }

      if (action.includes('git status')) {
        return 'git status';
      }
    }

    return null;
  }

  /**
   * Check if the user's request is for a file operation
   * @param {string} text - The user's input text
   * @returns {boolean} - True if the request is for a file operation
   */
  isFileOperationRequest(text) {
    // First, check if this is a directory listing request (not a file operation)
    const dirListingRegex = /^(?:list|show|tell\s+me|display|what\s+are\s+the)\s+(?:files|directories|dirs|contents|content)\s+(?:in|of|from)?\s*(?:current|this|the)?\s*(?:directory|dir|folder|path)?$/i;
    if (dirListingRegex.test(text)) {
      return false;
    }

    // Check for simple directory commands
    if (/^(?:ls|dir)$/i.test(text)) {
      return false;
    }

    // Check for file creation requests
    const createFileRegex = /^(?:create|make|add|new)\s+(?:a\s+)?(?:new\s+)?file(?:\s+(?:called|named))?\s+([a-zA-Z0-9\s\-_.]+)(?:\s+(?:in|at|to)\s+([a-zA-Z0-9\s\-_./\\]+))?(?:\s+with\s+(?:content|text|the\s+following))?/i;
    if (createFileRegex.test(text)) {
      return true;
    }

    // Check for file modification requests
    const modifyFileRegex = /^(?:modify|edit|update|change)\s+(?:the\s+)?(?:content\s+of\s+)?(?:file\s+)?([a-zA-Z0-9\s\-_.]+)(?:\s+(?:in|at|to)\s+([a-zA-Z0-9\s\-_./\\]+))?/i;
    if (modifyFileRegex.test(text)) {
      return true;
    }

    // Check for file read requests - but exclude directory listing requests
    const readFileRegex = /^(?:read|show|display|open|cat)\s+(?:the\s+)?(?:content\s+of\s+)?(?:file\s+)([a-zA-Z0-9\s\-_.]+)(?:\s+(?:in|at|from)\s+([a-zA-Z0-9\s\-_./\\]+))?/i;
    if (readFileRegex.test(text)) {
      return true;
    }

    // Check for file deletion requests
    const deleteFileRegex = /^(?:delete|remove|erase)\s+(?:the\s+)?(?:file\s+)?([a-zA-Z0-9\s\-_.]+)(?:\s+(?:in|at|from)\s+([a-zA-Z0-9\s\-_./\\]+))?/i;
    if (deleteFileRegex.test(text)) {
      return true;
    }

    return false;
  }

  resolveWebviewView(webviewView) {
    console.log('resolveWebviewView called');
    this.webviewView = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        this.extensionUri,
        vscode.Uri.joinPath(this.extensionUri, 'media')
      ]
    };

    console.log('Calling getWebviewContent...');
    const html = this.getWebviewContent();
    console.log('HTML generated, setting webview.html...');
    webviewView.webview.html = html;
    console.log('webview.html set!');

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(async (message) => {
      console.log('[extension.js] Received message from webview:', message);
      switch (message.command) {
        case 'webviewReady':
          console.log('[extension.js] Received webviewReady ping, sending ready pong');
          webviewView.webview.postMessage({ command: 'ready' });
          return;
        case 'alert':
          vscode.window.showInformationMessage(message.text);
          return;
        case 'error':
          vscode.window.showErrorMessage(message.text);
          return;
        case 'userMessage':
          await this.handleUserMessage(message.text);
          return;
        case 'clearHistory':
          await this.clearHistory();
          return;
        case 'saveSettings':
          await this.saveSettings(message.settings);
          return;
        case 'getSettings':
          const settings = await this.getSettings();
          webviewView.webview.postMessage({ command: 'settings', settings });
          return;
        case 'setMode':
          this.setMode(message.modeId);
          return;
        case 'getModes':
          this.sendModes();
          return;
        case 'applyFileChange':
          await this.applyFileChange(message.filePath);
          return;
        case 'discardFileChange':
          await this.discardFileChange(message.filePath);
          return;
        case 'applyAllFileChanges':
          await this.applyAllFileChanges();
          return;
        case 'discardAllFileChanges':
          await this.discardAllFileChanges();
          return;
        case 'viewFileChange':
          await this.viewFileChange(message.filePath);
          return;
        case 'stopResponse':
          this.stopResponse();
          return;
      }
    });
  }

  async handleUserMessage(text) {
    // Special handling for terminal commands and file operations in agent auto mode
    if (this.currentMode.id === 'agent-auto' && this.agentTools) {
      // Check if this is a file creation request with content
      const createWithContentMatch = text.match(/^(?:create|make|add|new)\s+(?:a\s+)?(?:new\s+)?file(?:\s+(?:called|named))?\s+([a-zA-Z0-9\s\-_.]+)(?:\s+(?:in|at|to)\s+([a-zA-Z0-9\s\-_./\\]+))?\s+with\s+(?:content|text|the\s+following)(?:\s*:)?\s*(.+)$/is);

      if (createWithContentMatch) {
        const fileName = createWithContentMatch[1].trim();
        const location = createWithContentMatch[2] ? createWithContentMatch[2].trim() : '';
        const content = createWithContentMatch[3].trim();

        console.log(`Detected file creation request: ${fileName} with content`);

        // Add user message to the chat
        this.messages.push({ role: 'user', content: text });

        // Update the webview
        this.updateWebview();

        // Show loading indicator
        if (this.webviewView) {
          this.webviewView.webview.postMessage({ command: 'setLoading', loading: true });
        }

        try {
          // Determine the full file path
          const workingDir = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
          const filePath = location ?
            (path.isAbsolute(location) ? path.join(location, fileName) : path.join(workingDir, location, fileName)) :
            path.join(workingDir, fileName);

          console.log(`Creating file at: ${filePath}`);

          // Create the file
          const fileCreateResponse = `<file-create path="${filePath}">${content}</file-create>`;

          // Add an immediate AI response
          this.messages.push({
            role: 'assistant',
            content: fileCreateResponse
          });

          // Update the webview
          this.updateWebview();

          // Process the file creation
          const processedResponse = await this.processFileOperations(fileCreateResponse);

          // Update the AI response with the processed output
          this.messages[this.messages.length - 1].content = processedResponse;

          // Update the webview
          this.updateWebview();

          // Hide loading indicator
          if (this.webviewView) {
            this.webviewView.webview.postMessage({ command: 'setLoading', loading: false });
          }

          return;
        } catch (error) {
          console.error('Error creating file:', error);

          // Add error message
          this.messages.push({
            role: 'assistant',
            content: `Error creating file: ${error.message || 'Unknown error'}`
          });

          // Update the webview
          this.updateWebview();

          // Hide loading indicator
          if (this.webviewView) {
            this.webviewView.webview.postMessage({ command: 'setLoading', loading: false });
          }

          return;
        }
      }

      // Process natural language terminal command requests
      const commandToExecute = this.interpretTerminalCommand(text);

      if (commandToExecute) {
        console.log(`Interpreted terminal command request: ${commandToExecute}`);

        // Add user message to the chat
        this.messages.push({ role: 'user', content: text });

        // Update the webview
        this.updateWebview();

        // Show loading indicator
        if (this.webviewView) {
          this.webviewView.webview.postMessage({ command: 'setLoading', loading: true });
        }

        try {
          // Get the workspace root
          const workingDir = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
          console.log(`Using workspace root as working directory: ${workingDir || 'current directory'}`);

          // Special handling for npm commands
          let responsePrefix = '';

          // Check if this is an npm install command without a package.json
          if ((commandToExecute === 'npm i' || commandToExecute === 'npm install') && workingDir) {
            try {
              const packageJsonPath = path.join(workingDir, 'package.json');
              const packageJsonExists = fs.existsSync(packageJsonPath);

              if (!packageJsonExists) {
                responsePrefix = `No package.json found in the current directory. Creating a basic package.json file first.\n\n`;

                // Create a basic package.json file
                const packageJson = {
                  "name": path.basename(workingDir),
                  "version": "1.0.0",
                  "description": "Created by Vidyadhara",
                  "main": "index.js",
                  "scripts": {
                    "test": "echo \"Error: no test specified\" && exit 1"
                  },
                  "keywords": [],
                  "author": "",
                  "license": "ISC"
                };

                await this.agentTools.writeFile(
                  'package.json',
                  JSON.stringify(packageJson, null, 2),
                  false
                );

                // First run npm init -y
                await this.agentTools.executeCommand('npm init -y', workingDir);
              }
            } catch (err) {
              console.error('Error checking for package.json:', err);
              // Continue with the original command
            }
          }

          // Add an immediate AI response with the terminal command
          let terminalCommandResponse = `<terminal-command>${commandToExecute}</terminal-command>`;
          if (responsePrefix) {
            terminalCommandResponse = responsePrefix + terminalCommandResponse;
          }

          this.messages.push({
            role: 'assistant',
            content: terminalCommandResponse
          });

          // Update the webview
          this.updateWebview();

          // Execute the command directly
          console.log(`Executing command: ${commandToExecute} in directory: ${workingDir || 'current directory'}`);
          const result = await this.agentTools.executeCommand(commandToExecute, workingDir);

          // Format the output
          const stdout = result.stdout || '';
          const stderr = result.stderr || '';
          let output = '';

          if (stdout && stderr) {
            output = `${stdout}\n\nErrors:\n${stderr}`;
          } else if (stdout) {
            output = stdout;
          } else if (stderr) {
            output = 'Error: ' + stderr;
          } else {
            output = 'Command executed successfully with no output.';
          }

          // Update the AI response with the processed output
          const processedResponse = `<terminal-command>${commandToExecute}</terminal-command>\n\n\`\`\`\n${output}\n\`\`\``;
          this.messages[this.messages.length - 1].content = processedResponse;

          // Update the webview
          this.updateWebview();

          // Hide loading indicator
          if (this.webviewView) {
            this.webviewView.webview.postMessage({ command: 'setLoading', loading: false });
          }

          return;
        } catch (error) {
          console.error('Error executing terminal command:', error);

          // Format error message
          let errorMessage = '';
          if (typeof error === 'object' && error !== null) {
            if (error.stderr) {
              errorMessage = error.stderr;
            } else if (error.message) {
              errorMessage = error.message;
            } else {
              errorMessage = JSON.stringify(error);
            }
          } else {
            errorMessage = String(error);
          }

          // Update the AI response with the error
          const errorResponse = `<terminal-command>${commandToExecute}</terminal-command>\n\n\`\`\`\nError: ${errorMessage || 'Unknown error occurred while executing the command'}\n\`\`\``;
          this.messages[this.messages.length - 1].content = errorResponse;

          // Update the webview
          this.updateWebview();

          // Hide loading indicator
          if (this.webviewView) {
            this.webviewView.webview.postMessage({ command: 'setLoading', loading: false });
          }

          return;
        }
      }
    }

    // Add user message to the chat
    this.messages.push({ role: 'user', content: text });

    // Update the webview
    this.updateWebview();

    // Show loading indicator
    if (this.webviewView) {
      this.webviewView.webview.postMessage({ command: 'setLoading', loading: true });
    }

    // Get API key from settings
    const config = vscode.workspace.getConfiguration('vidyadhara');
    const apiKey = config.get('openRouterApiKey');
    const defaultModel = config.get('defaultModel') || 'meta-llama/llama-3-8b-instruct'; // Default to a free model

    // Check if using a premium model without an API key
    const isPremiumModel = [
      'anthropic/claude-3-opus-20240229',
      'anthropic/claude-3-sonnet-20240229',
      'anthropic/claude-3-haiku-20240307',
      'openai/gpt-4o',
      'openai/gpt-4-turbo',
      'google/gemini-1.5-pro-latest',
      'meta-llama/llama-3-70b-instruct'
    ].includes(defaultModel);

    if (isPremiumModel && !apiKey) {
      this.showApiKeyMessage();
      return;
    }

    // Search for relevant code if codebase is indexed
    let relevantCode = [];
    if (this.codeIndexManager) {
      relevantCode = await this.searchCode(text);
    }

    // Check if the user is asking about the current directory or environment
    const isAskingAboutDirectory = /which\s+(dir|directory|folder|path|workspace)\s+(are\s+we\s+in|is\s+this|am\s+i\s+in)/i.test(text) ||
      /where\s+am\s+i/i.test(text) ||
      /current\s+(dir|directory|folder|path|workspace)/i.test(text) ||
      /pwd/i.test(text);

    const isAskingAboutOS = /which\s+(os|operating\s+system)/i.test(text) ||
      /what\s+(os|operating\s+system)/i.test(text) ||
      /am\s+i\s+on\s+(windows|mac|linux)/i.test(text);

    const isAskingAboutFiles = /list\s+(the\s+)?files/i.test(text) ||
      /show\s+(the\s+)?files/i.test(text) ||
      /what\s+files/i.test(text) ||
      /(ls|dir)\s+command/i.test(text);

    // Get current workspace, file, and environment information
    let workspaceInfo = '';

    // Get OS information
    const os = require('os');
    const platform = os.platform();
    const osType = platform === 'win32' ? 'Windows' :
      platform === 'darwin' ? 'macOS' :
        platform === 'linux' ? 'Linux' : platform;
    const release = os.release();
    const arch = os.arch();
    const hostname = os.hostname();
    const userInfo = os.userInfo();
    const username = userInfo.username;
    const homedir = os.homedir();
    const tempdir = os.tmpdir();

    // Get current working directory
    const cwd = process.cwd();

    // Get VS Code version
    const vscodeVersion = vscode.version;

    workspaceInfo += `Operating System: ${osType} (${platform})\n`;
    workspaceInfo += `OS Version: ${release}\n`;
    workspaceInfo += `Architecture: ${arch}\n`;
    workspaceInfo += `Hostname: ${hostname}\n`;
    workspaceInfo += `Username: ${username}\n`;
    workspaceInfo += `Home Directory: ${homedir}\n`;
    workspaceInfo += `Temp Directory: ${tempdir}\n`;
    workspaceInfo += `Current Working Directory: ${cwd}\n`;
    workspaceInfo += `VS Code Version: ${vscodeVersion}\n`;

    // Get workspace information
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders && workspaceFolders.length > 0) {
      const currentFolder = workspaceFolders[0];
      workspaceInfo += `Current workspace: ${currentFolder.name}\nWorkspace path: ${currentFolder.uri.fsPath}\n`;

      // Get current file if one is open
      const activeEditor = vscode.window.activeTextEditor;
      if (activeEditor) {
        const currentFile = activeEditor.document.uri.fsPath;
        const relativePath = vscode.workspace.asRelativePath(currentFile);
        workspaceInfo += `Current file: ${relativePath}\n`;
      }

      // Get list of files in the workspace if the user is asking about files
      if (isAskingAboutFiles) {
        try {
          const fs = require('fs');
          const path = require('path');

          // Function to list files recursively
          const listFilesRecursively = (dir, relativeTo, maxDepth = 2, currentDepth = 0) => {
            let result = [];

            if (currentDepth > maxDepth) {
              return result;
            }

            const files = fs.readdirSync(dir);

            for (const file of files) {
              const fullPath = path.join(dir, file);
              const relativePath = path.relative(relativeTo, fullPath);

              // Skip node_modules, .git, and other common excluded directories
              if (['node_modules', '.git', 'dist', 'build', 'out', '.vscode'].includes(file)) {
                continue;
              }

              try {
                const stat = fs.statSync(fullPath);

                if (stat.isDirectory()) {
                  // Add directory
                  result.push({ path: relativePath, type: 'directory' });

                  // Recursively add files from subdirectory
                  const subFiles = listFilesRecursively(fullPath, relativeTo, maxDepth, currentDepth + 1);
                  result = result.concat(subFiles);
                } else {
                  // Add file
                  result.push({ path: relativePath, type: 'file' });
                }
              } catch (err) {
                console.error(`Error accessing ${fullPath}:`, err);
              }
            }

            return result;
          };

          // Get files in the workspace
          const workspacePath = currentFolder.uri.fsPath;
          const files = listFilesRecursively(workspacePath, workspacePath);

          // Format the file list
          if (files.length > 0) {
            workspaceInfo += '\nFiles in workspace:\n';

            // Group files by directory
            const filesByDir = {};

            for (const file of files) {
              const dirName = path.dirname(file.path) === '.' ? '' : path.dirname(file.path);

              if (!filesByDir[dirName]) {
                filesByDir[dirName] = [];
              }

              filesByDir[dirName].push(file);
            }

            // Add files to workspace info
            for (const dir in filesByDir) {
              if (dir) {
                workspaceInfo += `\n${dir}/:\n`;
              } else {
                workspaceInfo += '\nRoot directory:\n';
              }

              for (const file of filesByDir[dir]) {
                const fileName = path.basename(file.path);
                const icon = file.type === 'directory' ? '📁' : '📄';
                workspaceInfo += `  ${icon} ${fileName}\n`;
              }
            }
          } else {
            workspaceInfo += '\nNo files found in workspace.\n';
          }
        } catch (error) {
          console.error('Error listing files:', error);
          workspaceInfo += '\nError listing files in workspace.\n';
        }
      }
    } else {
      workspaceInfo += 'No workspace folder is open.\n';
    }

    // Prepare system message with current mode, workspace info, relevant code and GitHub info
    let systemMessage = this.currentMode.systemMessage + ' You have access to information about the user\'s current workspace, files, and environment. Use this information to provide more relevant and contextual assistance.';

    // Add mode-specific instructions
    if (this.currentMode.id === 'agent' || this.currentMode.id === 'agent-auto') {
      systemMessage += ' You have access to tools that can help you understand the codebase and provide better assistance. ';

      // Add file system tools instructions
      systemMessage += `

You can use the following file system tools:

1. To read a file:
   <file-read path="path/to/file.ext">

2. To create a new file:
   <file-create path="path/to/file.ext">
   File content goes here
   </file-create>

3. To modify an existing file:
   <file-modify path="path/to/file.ext">
   New file content goes here
   </file-modify>

4. To delete a file:
   <file-delete path="path/to/file.ext">
`;

      if (this.currentMode.id === 'agent-auto') {
        systemMessage += 'You should proactively use these tools without being asked to do so. Take initiative to explore the codebase and provide comprehensive answers. When the user asks you to create or modify files, automatically use the file system tools to do so. All file changes will be previewed for the user to review before being applied.';
      } else {
        systemMessage += 'Use these tools when appropriate to provide better assistance. When the user asks you to create or modify files, use the file system tools to do so. All file changes will be previewed for the user to review before being applied.';
      }
    }

    if (workspaceInfo) {
      systemMessage += '\n\n' + workspaceInfo;
    }

    // Add special instructions for directory, OS, and file listing questions
    if (isAskingAboutDirectory || isAskingAboutOS || isAskingAboutFiles) {
      systemMessage += '\n\nThe user is asking about their current environment or files. Please provide a direct and helpful response using the workspace information provided above. Do not suggest commands like pwd, cd, ls, or dir to find this information, as you already have it. Be specific and detailed in your response, providing the exact paths and information requested.';
    }

    // Add general instructions for using environment information
    systemMessage += '\n\nWhen answering questions about the user\'s environment, files, or directories, always use the information provided above rather than suggesting commands to find this information. You have direct access to the user\'s current working directory, workspace path, OS details, file listings, and other environment information. If the user asks about files in their workspace, provide the file listing information directly from the data above.';

    // Get GitHub repository info if available
    let repoInfo = null;
    let issues = [];
    let pullRequests = [];

    if (this.githubManager) {
      try {
        // Get repository info
        repoInfo = await this.githubManager.getRepositoryInfo();

        // Get open issues and pull requests
        issues = await this.githubManager.getIssues('open');
        pullRequests = await this.githubManager.getPullRequests('open');

        // Get relevant GitHub information based on the query
        const githubInfo = await this.githubManager.getRelevantGithubInfo(text);
        if (githubInfo) {
          systemMessage += '\n\n' + githubInfo;
        }
      } catch (error) {
        console.error('Error getting GitHub info:', error);
      }
    }

    if (relevantCode.length > 0) {
      systemMessage += '\n\nHere is some relevant code from the codebase that might help answer the question:\n';

      relevantCode.forEach((result) => {
        systemMessage += `\n---\nFile: ${result.filePath} (Lines ${result.startLine}-${result.endLine})\n\`\`\`\n${result.content}\n\`\`\`\n`;
      });

      systemMessage += '\n---\n';
    }

    if (repoInfo) {
      systemMessage += `\n\nGitHub Repository: ${repoInfo.full_name}\nDescription: ${repoInfo.description || 'No description'}\nDefault Branch: ${repoInfo.default_branch}\n`;

      if (issues.length > 0) {
        systemMessage += '\nOpen Issues:\n';
        issues.forEach(issue => {
          systemMessage += `- #${issue.number}: ${issue.title}\n`;
        });
      }

      if (pullRequests.length > 0) {
        systemMessage += '\nOpen Pull Requests:\n';
        pullRequests.forEach(pr => {
          systemMessage += `- #${pr.number}: ${pr.title}\n`;
        });
      }

      systemMessage += '\n---\n';
    }

    systemMessage += '\nUse this context to provide a more accurate and helpful response.'

    // Call OpenRouter API with system message
    this.callOpenRouterAPI(apiKey, defaultModel, text, systemMessage);
  }

  async callOpenRouterAPI(apiKey, model, _text, systemMessage = null) {
    // Set the generating flag
    this.isGenerating = true;

    try {
      // Prepare messages for the API
      let messages = [];

      // Add system message if provided
      if (systemMessage) {
        messages.push({
          role: 'system',
          content: systemMessage
        });
      }

      // Add conversation messages
      messages = messages.concat(this.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })));

      // Prepare headers
      const headers = {
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://vidyadhara.dev',
        'X-Title': 'Vidyadhara'
      };

      // Add Authorization header if API key is provided
      if (apiKey) {
        headers['Authorization'] = `Bearer ${apiKey}`;
      }

      // Create a cancel token source
      const cancelTokenSource = axios.CancelToken.source();
      this.currentRequest = cancelTokenSource;

      // Add a placeholder for the streaming response
      const responseIndex = this.messages.length;
      this.messages.push({
        role: 'assistant',
        content: ''
      });

      // Update the webview to show the empty message
      this.updateWebview();

      // Call the API with streaming enabled
      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          model: model,
          messages: messages,
          max_tokens: 1000,
          stream: true // Enable streaming
        },
        {
          headers,
          cancelToken: cancelTokenSource.token,
          responseType: 'stream'
        }
      );

      // Process the streaming response
      let streamingContent = '';

      // Set up the data handler for the stream
      response.data.on('data', (chunk) => {
        try {
          // Convert the chunk to a string
          const chunkString = chunk.toString();

          // Split the chunk by lines
          const lines = chunkString.split('\n').filter(line => line.trim() !== '');

          // Process each line
          for (const line of lines) {
            // Skip lines that don't start with 'data:'
            if (!line.startsWith('data:')) continue;

            // Extract the JSON data
            const jsonData = line.substring(5).trim();

            // Skip '[DONE]' message
            if (jsonData === '[DONE]') continue;

            try {
              // Parse the JSON data
              const data = JSON.parse(jsonData);

              // Extract the content delta
              if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                const contentDelta = data.choices[0].delta.content;

                // Append the content delta to the streaming content
                streamingContent += contentDelta;

                // Update the message in the messages array
                this.messages[responseIndex].content = streamingContent;

                // Update the webview
                this.updateWebview();
              }
            } catch (parseError) {
              console.error('Error parsing JSON data:', parseError);
            }
          }
        } catch (error) {
          console.error('Error processing chunk:', error);
        }
      });

      // Set up the end handler for the stream
      await new Promise((resolve, reject) => {
        response.data.on('end', async () => {
          // Process file operations in Agent and Agent Auto modes
          if (this.currentMode.id === 'agent' || this.currentMode.id === 'agent-auto') {
            console.log('Processing file operations in response...');
            const processedContent = await this.processFileOperations(streamingContent);
            if (processedContent !== streamingContent) {
              console.log('Content was processed with file operations');
              // Update the message with the processed content
              this.messages[responseIndex].content = processedContent;
              // Update the webview
              this.updateWebview();

              // Check if there are pending file changes
              if (this.agentTools) {
                const pendingChanges = await agentTools.getPendingFileChanges();
                console.log(`Found ${pendingChanges.length} pending file changes`);

                if (pendingChanges.length > 0) {
                  // Instead of showing a modal dialog, just update the file changes section in the chat UI
                  // and show a non-modal notification
                  vscode.window.showInformationMessage(
                    `The AI has suggested ${pendingChanges.length} file change(s). Click the File Changes button above the text area to review and apply changes.`,
                    'Show Changes'
                  ).then(action => {
                    if (action === 'Show Changes') {
                      // Send a message to the webview to show the file changes section
                      if (this.webviewView) {
                        this.webviewView.webview.postMessage({
                          command: 'getFileChanges'
                        });
                      }
                    }
                  });

                  // Automatically update the file changes section in the chat UI
                  if (this.webviewView) {
                    console.log('Sending file changes to webview');
                    this.sendFileChanges();
                  }
                }
              }
            }
          }
          resolve();
        });

        response.data.on('error', (error) => {
          reject(error);
        });
      });
    } catch (error) {
      console.error('Error calling OpenRouter API:', error);

      // Check if the request was cancelled
      if (axios.isCancel(error)) {
        console.log('Request cancelled:', error.message);
        return; // Don't add an error message for cancelled requests
      }

      // Add error message to the chat
      this.messages.push({
        role: 'assistant',
        content: `Error: ${error.message || 'Failed to get response from AI'}`
      });

      // Update the webview
      this.updateWebview();
    } finally {
      // Reset the generating flag
      this.isGenerating = false;
      this.currentRequest = null;

      // Hide loading indicator
      if (this.webviewView) {
        this.webviewView.webview.postMessage({ command: 'setLoading', loading: false });
      }
    }
  }

  showApiKeyMessage() {
    // Add message about missing API key
    this.messages.push({
      role: 'assistant',
      content: 'You are currently using a free model (Llama 3 8B) which doesn\'t require an API key. For premium models like Claude or GPT-4, please set your OpenRouter API key in the settings. Click the ⚙️ icon to open settings.'
    });

    // Update the webview
    this.updateWebview();

    // Show notification
    vscode.window.showInformationMessage(
      'Using free model. For premium models, set your OpenRouter API key in the settings.',
      'Open Settings'
    ).then(selection => {
      if (selection === 'Open Settings') {
        // Open the settings panel in the webview
        if (this.webviewView) {
          this.webviewView.webview.postMessage({ command: 'openSettings' });
        }
      }
    });
  }

  clearHistory() {
    // Reset messages to just the welcome message
    this.messages = [
      {
        role: 'assistant',
        content: 'Chat history has been cleared. How can I help you?'
      }
    ];

    // Clear saved messages
    if (this.context) {
      this.context.globalState.update('vidyadhara.chatMessages', this.messages);
    }

    // Update the webview
    this.updateWebview();

    // Show notification
    vscode.window.showInformationMessage('Chat history cleared!');
  }

  async getSettings() {
    const config = vscode.workspace.getConfiguration('vidyadhara');
    const githubConfig = vscode.workspace.getConfiguration('vidyadhara.github');
    const settings = {
      apiKey: config.get('openRouterApiKey') || '',
      githubToken: githubConfig.get('token') || '',
      model: config.get('defaultModel') || 'meta-llama/llama-3-8b-instruct', // Default to a free model
      mode: this.currentMode.id,
      modes: this.getModes(),
      models: [] // Will be populated with available models
    };

    try {
      // Fetch available models from OpenRouter
      const response = await axios.get('https://openrouter.ai/api/v1/models');

      if (response.data && response.data.data) {
        // Extract models and categorize them
        const premiumModels = [];
        const freeModels = [];

        response.data.data.forEach(model => {
          const modelInfo = {
            id: model.id,
            name: model.name || model.id.split('/').pop(),
            context_length: model.context_length,
            pricing: model.pricing
          };

          // Check if the model is free (has a 0 input or output price)
          const isFree =
            (model.pricing &&
              ((model.pricing.prompt === 0 && model.pricing.completion === 0) ||
                (model.pricing.input === 0 && model.pricing.output === 0)));

          if (isFree) {
            freeModels.push(modelInfo);
          } else {
            premiumModels.push(modelInfo);
          }
        });

        // Sort models by name
        freeModels.sort((a, b) => a.name.localeCompare(b.name));
        premiumModels.sort((a, b) => a.name.localeCompare(b.name));

        // Add to settings
        settings.models = {
          premium: premiumModels,
          free: freeModels
        };
      }
    } catch (error) {
      console.error('Error fetching models from OpenRouter:', error);
    }

    if (this.webviewView) {
      console.log('Sending settings to webview:', settings);
      this.webviewView.webview.postMessage({
        command: 'updateSettings',
        settings: settings
      });

      // Also send a direct message to update the active mode button
      console.log('Sending direct updateActiveModeButton message with mode:', settings.mode);
      this.webviewView.webview.postMessage({
        command: 'updateActiveMode',
        modeId: settings.mode
      });
    }
  }

  async saveSettings(settings) {
    if (settings.apiKey) {
      await vscode.workspace.getConfiguration('vidyadhara').update('openRouterApiKey', settings.apiKey, true);
    }

    if (settings.model) {
      await vscode.workspace.getConfiguration('vidyadhara').update('defaultModel', settings.model, true);
    }

    if (settings.githubToken) {
      await vscode.workspace.getConfiguration('vidyadhara.github').update('token', settings.githubToken, true);
      // The GitHub manager will automatically reload configuration due to the event listener
    }

    if (settings.mode) {
      console.log('Setting mode from saveSettings:', settings.mode);
      const success = this.setMode(settings.mode);
      console.log('setMode result:', success);

      // Also send a direct message to update the active mode button
      if (this.webviewView) {
        console.log('Sending direct updateActiveModeButton message with mode:', settings.mode);
        this.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: settings.mode
        });

        // Send multiple updateActiveMode messages with delays to ensure the mode buttons are updated
        setTimeout(() => {
          console.log('Sending delayed updateActiveMode message with mode:', settings.mode);
          this.webviewView.webview.postMessage({
            command: 'updateActiveMode',
            modeId: settings.mode
          });
        }, 500);

        setTimeout(() => {
          console.log('Sending second delayed updateActiveMode message with mode:', settings.mode);
          this.webviewView.webview.postMessage({
            command: 'updateActiveMode',
            modeId: settings.mode
          });
        }, 1000);

        setTimeout(() => {
          console.log('Sending third delayed updateActiveMode message with mode:', settings.mode);
          this.webviewView.webview.postMessage({
            command: 'updateActiveMode',
            modeId: settings.mode
          });
        }, 2000);
      }
    }

    vscode.window.showInformationMessage('Settings saved successfully!');
  }

  /**
   * Set the chat mode
   * @param {string} modeId - The ID of the mode to set
   */
  setMode(modeId) {
    console.log('setMode called with modeId:', modeId);

    // Helper to send updateActiveMode messages with configurable delays
    const sendUpdateActiveMode = (modeId) => {
      const delays = [0, 500, 1000, 2000]; // ms, can be adjusted as needed
      if (this.webviewView) {
        delays.forEach((delay, idx) => {
          setTimeout(() => {
            console.log(`Sending updateActiveMode message with mode: ${modeId} (delay: ${delay}ms)`);
            this.webviewView.webview.postMessage({
              command: 'updateActiveMode',
              modeId
            });
          }, delay);
        });
      }
    };

    // Find the mode by ID (handle special agent-auto mode)
    let mode;
    if (modeId === 'agent-auto') {
      mode = CHAT_MODES.AGENT_AUTO;
    } else {
      mode = Object.values(CHAT_MODES).find(mode => mode.id === modeId);
    }

    if (!mode) {
      console.log('Invalid mode:', modeId);
      return false;
    }

    // Update current mode
    console.log('Setting current mode to:', mode.name);
    this.currentMode = mode;

    // Add a system message to indicate the mode change
    let modeMessage = `Mode changed to ${mode.name}.`;
    if (mode.id === 'agent') {
      modeMessage += ' In Agent mode, I can access tools and your codebase to provide more accurate assistance.';
    } else if (mode.id === 'agent-auto') {
      modeMessage += ' In Agent Auto mode, I will automatically use tools and access your codebase to provide comprehensive assistance.';
    }
    console.log('Adding mode change message to chat:', modeMessage);
    this.messages.push({
      role: 'assistant',
      content: modeMessage
    });

    // Update the webview and UI
    console.log('Updating webview with new mode');
    this.updateWebview();
    console.log('Sending modes to update UI');
    this.sendModes();

    // Send updateActiveMode messages to webview with delays
    sendUpdateActiveMode(mode.id);

    // Save the mode to settings
    console.log('Saving mode to settings:', modeId);
    vscode.workspace.getConfiguration('vidyadhara').update('chatMode', modeId, true);

    return true;
  }

  /**
   * Get all available chat modes
   * @returns {Array} Array of chat modes
   */
  getModes() {
    return Object.values(CHAT_MODES);
  }

  /**
   * Send available modes to the webview
   */
  sendModes() {
    console.log('sendModes called');

    if (this.webviewView) {
      const modes = this.getModes();
      const currentModeId = this.currentMode.id;

      console.log('Sending modes to webview:', modes);
      console.log('Current mode ID:', currentModeId);

      this.webviewView.webview.postMessage({
        command: 'updateModes',
        modes: modes,
        currentMode: currentModeId
      });

      console.log('Sent updateModes message to webview');

      // Also send a direct message to update the active mode button
      console.log('Sending direct updateActiveModeButton message with mode:', currentModeId);
      this.webviewView.webview.postMessage({
        command: 'updateActiveMode',
        modeId: currentModeId
      });

      // Send multiple updateActiveMode messages with delays to ensure the mode buttons are updated
      setTimeout(() => {
        console.log('Sending delayed updateActiveMode message with mode:', currentModeId);
        this.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: currentModeId
        });
      }, 500);

      setTimeout(() => {
        console.log('Sending second delayed updateActiveMode message with mode:', currentModeId);
        this.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: currentModeId
        });
      }, 1000);

      setTimeout(() => {
        console.log('Sending third delayed updateActiveMode message with mode:', currentModeId);
        this.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: currentModeId
        });
      }, 2000);
    } else {
      console.log('Cannot send modes: webviewView is not available');
    }
  }

  /**
   * Send pending file changes to the webview
   */
  async sendFileChanges() {
    if (!this.webviewView) {
      console.error('Webview not available for sending file changes');
      return;
    }

    try {
      // Get managers using lazy initialization
      const { agentTools } = this.getManagers();

      if (!agentTools) {
        console.error('Agent tools not available for getting file changes');
        // Send empty changes to show the section but with no changes
        this.webviewView.webview.postMessage({
          command: 'updateFileChanges',
          changes: []
        });
        return;
      }

      console.log('Getting pending file changes...');
      const changes = await agentTools.getPendingFileChanges();
      console.log(`Found ${changes.length} pending file changes`);

      // Enhance changes with line counts
      const enhancedChanges = [];

      for (const change of changes) {
        const enhancedChange = { ...change };

        // Get the full change to calculate line counts
        const fullChange = await agentTools.getFullFileChange(change.filePath);

        if (fullChange) {
          const originalLines = fullChange.originalContent.split('\n').length;
          const newLines = fullChange.newContent.split('\n').length;
          const linesDiff = newLines - originalLines;

          // Add line count information
          enhancedChange.lineCount = `${originalLines} → ${newLines} (${linesDiff >= 0 ? '+' + linesDiff : linesDiff})`;
        }

        enhancedChanges.push(enhancedChange);
      }

      console.log('Sending file changes to webview:', enhancedChanges);
      this.webviewView.webview.postMessage({
        command: 'updateFileChanges',
        changes: enhancedChanges
      });
    } catch (error) {
      console.error('Error getting file changes:', error);
      // Send empty changes on error
      if (this.webviewView) {
        this.webviewView.webview.postMessage({
          command: 'updateFileChanges',
          changes: []
        });
      }
    }
  }

  /**
   * View a file change diff
   * @param {string} filePath - Path to the file
   */
  async viewFileChange(filePath) {
    try {
      // Get managers using lazy initialization
      const { agentTools } = this.getManagers();

      if (!agentTools) {
        console.error('Agent tools not initialized');
        vscode.window.showErrorMessage('Cannot view file changes: Agent tools not initialized');
        return;
      }

      // Show the diff in VS Code's diff editor
      await agentTools.showFileDiff(filePath);

      // Also add a message to the chat showing basic info about the change
      const fullChange = await agentTools.getFullFileChange(filePath);

      if (fullChange) {
        // Calculate line changes
        const originalLines = fullChange.originalContent.split('\n').length;
        const newLines = fullChange.newContent.split('\n').length;
        const linesDiff = newLines - originalLines;

        // Add a message to the chat showing basic info
        const diffMessage = {
          role: 'assistant',
          content: `**Viewing changes for ${filePath}**\n\n` +
            `**Type:** ${fullChange.type}\n` +
            `**Lines:** ${originalLines} → ${newLines} (${linesDiff >= 0 ? '+' + linesDiff : linesDiff})\n\n` +
            `> Diff view opened in editor. You can also see the changes in the File Changes section.`
        };

        this.messages.push(diffMessage);
        this.updateWebview();
      }
    } catch (error) {
      console.error(`Error showing diff for ${filePath}:`, error);
      vscode.window.showErrorMessage(`Error showing diff: ${error.message}`);
    }
  }

  /**
   * Generate a simple diff between two strings
   * @param {string} originalContent - Original content
   * @param {string} newContent - New content
   * @returns {string} - Diff string
   */
  generateDiff(originalContent, newContent) {
    const originalLines = originalContent.split('\n');
    const newLines = newContent.split('\n');
    let diff = '';

    // Simple diff algorithm - just show removed lines with - and added lines with +
    // For a real diff, we would use a proper diff algorithm
    if (originalContent === '') {
      // New file
      newLines.forEach(line => {
        diff += `+ ${line}\n`;
      });
    } else if (newContent === '') {
      // Deleted file
      originalLines.forEach(line => {
        diff += `- ${line}\n`;
      });
    } else {
      // Modified file - very simple diff
      // In a real implementation, we would use a proper diff algorithm
      // This is just a simple example
      const maxLines = Math.max(originalLines.length, newLines.length);
      for (let i = 0; i < maxLines; i++) {
        if (i < originalLines.length && i < newLines.length) {
          if (originalLines[i] !== newLines[i]) {
            diff += `- ${originalLines[i]}\n+ ${newLines[i]}\n`;
          } else {
            diff += `  ${newLines[i]}\n`;
          }
        } else if (i < originalLines.length) {
          diff += `- ${originalLines[i]}\n`;
        } else if (i < newLines.length) {
          diff += `+ ${newLines[i]}\n`;
        }
      }
    }

    return diff;
  }

  /**
   * Apply a file change
   * @param {string} filePath - Path to the file
   */
  async applyFileChange(filePath) {
    try {
      // Get managers using lazy initialization
      const { agentTools } = this.getManagers();

      if (!agentTools) {
        console.error('Agent tools not initialized');
        vscode.window.showErrorMessage('Cannot apply file changes: Agent tools not initialized');
        return;
      }

      const success = await agentTools.applyFileChange(filePath);

      if (success) {
        vscode.window.showInformationMessage(`Applied change to ${filePath}`);
        // Update the file changes list
        this.sendFileChanges();
      } else {
        vscode.window.showErrorMessage(`Failed to apply change to ${filePath}`);
      }
    } catch (error) {
      console.error(`Error applying change to ${filePath}:`, error);
      vscode.window.showErrorMessage(`Error applying change: ${error.message}`);
    }
  }

  /**
   * Discard a file change
   * @param {string} filePath - Path to the file
   */
  async discardFileChange(filePath) {
    try {
      // Get managers using lazy initialization
      const { agentTools } = this.getManagers();

      if (!agentTools) {
        console.error('Agent tools not initialized');
        vscode.window.showErrorMessage('Cannot discard file changes: Agent tools not initialized');
        return;
      }

      await agentTools.discardFileChange(filePath);
      vscode.window.showInformationMessage(`Discarded change to ${filePath}`);
      // Update the file changes list
      this.sendFileChanges();
    } catch (error) {
      console.error(`Error discarding change to ${filePath}:`, error);
      vscode.window.showErrorMessage(`Error discarding change: ${error.message}`);
    }
  }

  /**
   * Apply all pending file changes
   */
  async applyAllFileChanges() {
    try {
      // Get managers using lazy initialization
      const { agentTools } = this.getManagers();

      if (!agentTools) {
        console.error('Agent tools not initialized');
        vscode.window.showErrorMessage('Cannot apply file changes: Agent tools not initialized');
        return;
      }

      const changes = await agentTools.getPendingFileChanges();

      if (changes.length === 0) {
        vscode.window.showInformationMessage('No pending file changes to apply.');
        return;
      }

      const appliedFiles = [];

      for (const change of changes) {
        const success = await agentTools.applyFileChange(change.filePath);
        if (success) {
          appliedFiles.push(change.filePath);
        }
      }

      if (appliedFiles.length > 0) {
        vscode.window.showInformationMessage(`Applied changes to ${appliedFiles.length} file(s).`);
        // Update the file changes list
        this.sendFileChanges();
      } else {
        vscode.window.showErrorMessage('Failed to apply any changes.');
      }
    } catch (error) {
      console.error('Error applying all changes:', error);
      vscode.window.showErrorMessage(`Error applying changes: ${error.message}`);
    }
  }

  /**
   * Discard all pending file changes
   */
  async discardAllFileChanges() {
    try {
      // Get managers using lazy initialization
      const { agentTools } = this.getManagers();

      if (!agentTools) {
        console.error('Agent tools not initialized');
        vscode.window.showErrorMessage('Cannot discard file changes: Agent tools not initialized');
        return;
      }

      const changes = await agentTools.getPendingFileChanges();

      if (changes.length === 0) {
        vscode.window.showInformationMessage('No pending file changes to discard.');
        return;
      }

      for (const change of changes) {
        await agentTools.discardFileChange(change.filePath);
      }

      vscode.window.showInformationMessage(`Discarded all ${changes.length} file change(s).`);
      // Update the file changes list
      this.sendFileChanges();
    } catch (error) {
      console.error('Error discarding all changes:', error);
      vscode.window.showErrorMessage(`Error discarding changes: ${error.message}`);
    }
  }

  /**
   * Search code using semantic search
   */
  async searchCode(query) {
    // Get managers using lazy initialization
    const { codeIndexManager } = this.getManagers();

    if (!codeIndexManager) {
      console.log('Code index manager not initialized');
      return [];
    }

    try {
      const results = await codeIndexManager.searchCode(query);
      return results || [];
    } catch (error) {
      console.error('Error searching code:', error);
      return [];
    }
  }

  stopResponse() {
    // Only proceed if we're actually generating a response
    if (!this.isGenerating) {
      return;
    }

    // Cancel the current request if possible
    if (this.currentRequest && this.currentRequest.cancel) {
      this.currentRequest.cancel();
    }

    // Reset the generating flag
    this.isGenerating = false;
    this.currentRequest = null;

    // Add a message to indicate the response was stopped
    this.messages.push({
      role: 'assistant',
      content: '[Response stopped by user]'
    });

    // Update the webview
    this.updateWebview();

    // Hide loading indicator
    if (this.webviewView) {
      this.webviewView.webview.postMessage({ command: 'setLoading', loading: false });
    }
  }

  updateWebview() {
    console.log('updateWebview called');

    if (this.webviewView) {
      // Save messages to global state
      if (this.context && this.messages.length > 0) {
        this.context.globalState.update('vidyadhara.chatMessages', this.messages);
      }

      // Update the webview with messages
      console.log('Sending updateMessages to webview with messages:', this.messages.length);
      this.webviewView.webview.postMessage({
        command: 'updateMessages',
        messages: this.messages,
        scrollToBottom: true // Always scroll to bottom when updating messages
      });

      // Force update messages again after a short delay to ensure they're displayed
      setTimeout(() => {
        console.log('Force updating messages after delay');
        this.webviewView.webview.postMessage({
          command: 'updateMessages',
          messages: this.messages,
          scrollToBottom: true
        });
      }, 100);

      // Force update messages again after a longer delay
      setTimeout(() => {
        console.log('Force updating messages after longer delay');
        this.webviewView.webview.postMessage({
          command: 'updateMessages',
          messages: this.messages,
          scrollToBottom: true
        });
      }, 500);

      // Also send a direct message to update the active mode button
      console.log('Sending direct updateActiveModeButton message with mode:', this.currentMode.id);
      this.webviewView.webview.postMessage({
        command: 'updateActiveMode',
        modeId: this.currentMode.id
      });

      // Send multiple updateActiveMode messages with delays to ensure the mode buttons are updated
      setTimeout(() => {
        console.log('Sending delayed updateActiveMode message with mode:', this.currentMode.id);
        this.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: this.currentMode.id
        });
      }, 500);

      setTimeout(() => {
        console.log('Sending second delayed updateActiveMode message with mode:', this.currentMode.id);
        this.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: this.currentMode.id
        });
      }, 1000);

      setTimeout(() => {
        console.log('Sending third delayed updateActiveMode message with mode:', this.currentMode.id);
        this.webviewView.webview.postMessage({
          command: 'updateActiveMode',
          modeId: this.currentMode.id
        });
      }, 2000);
    }
  }

  /**
   * Process file operations and terminal commands in the AI's response
   * @param {string} response - The AI's response
   * @returns {Promise<string>} - The processed response
   */
  async processFileOperations(response) {
    // Get managers using lazy initialization
    const { agentTools } = this.getManagers();

    if (!agentTools) {
      console.log('Agent tools not initialized');
      return response;
    }

    let processedResponse = response;

    try {
      console.log('Processing file operations and terminal commands in response');

      // Special case for common commands like "npm i" or "npm install" when they're not in terminal-command tags
      if (this.currentMode.id === 'agent-auto') {
        // Check if the response contains npm i or npm install without terminal-command tags
        const npmInstallRegex = /(?:^|\n)(?:Run |Execute |Type )?(?:`{1,3})?(?:npm i|npm install)(?:`{1,3})?(?:\s|$|\n)/;
        if (npmInstallRegex.test(response) && !/<terminal-command/.test(response)) {
          console.log('Detected npm install command without terminal-command tags, adding them automatically');

          // Get the current working directory
          const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || undefined;

          try {
            // Execute npm install
            const result = await agentTools.executeCommand('npm install', cwd);

            // Format the output
            const stdout = result.stdout || '';
            const stderr = result.stderr || '';
            const output = stderr ? `${stdout}\n\nErrors:\n${stderr}` : stdout;

            // Add the terminal command and output to the response
            processedResponse += `\n\nI've executed the npm install command for you:\n\n<terminal-command>npm install</terminal-command>\n\n\`\`\`\n${output}\n\`\`\``;
          } catch (error) {
            console.error('Error executing npm install:', error);
            processedResponse += `\n\nI tried to execute npm install but encountered an error:\n\n<terminal-command>npm install</terminal-command>\n\n\`\`\`\nError: ${error.message || 'Unknown error occurred while executing the command'}\n\`\`\``;
          }
        }
      }

      // Process file read operations - handle both with and without backticks
      const fileReadRegex = /(?:``)?<file-read\s+path="([^"]+)">(?:``)?/g;
      let match;

      while ((match = fileReadRegex.exec(response)) !== null) {
        const filePath = match[1];
        console.log(`Processing file read operation for ${filePath}`);
        try {
          const content = await agentTools.readFile(filePath);
          const replacement = `<file-read path="${filePath}">
${content}
</file-read>`;
          processedResponse = processedResponse.replace(match[0], replacement);
        } catch (error) {
          console.error(`Error reading file ${filePath}:`, error);
          const errorMsg = `<file-read path="${filePath}">
Error: ${error.message}
</file-read>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Also handle HTML entity versions
      const fileReadHtmlRegex = /&lt;file-read\s+path="([^"]+)"&gt;/g;
      while ((match = fileReadHtmlRegex.exec(response)) !== null) {
        const filePath = match[1];
        console.log(`Processing HTML entity file read operation for ${filePath}`);
        try {
          const content = await agentTools.readFile(filePath);
          const replacement = `<file-read path="${filePath}">
${content}
</file-read>`;
          processedResponse = processedResponse.replace(match[0], replacement);
        } catch (error) {
          console.error(`Error reading file ${filePath}:`, error);
          const errorMsg = `<file-read path="${filePath}">
Error: ${error.message}
</file-read>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Process file create operations - handle both with and without backticks
      const fileCreateRegex = /(?:``)?<file-create\s+path="([^"]+)">([\s\S]*?)<\/file-create>(?:``)?/g;

      while ((match = fileCreateRegex.exec(response)) !== null) {
        const filePath = match[1];
        const content = match[2].trim();
        console.log(`Processing file create operation for ${filePath}`);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.createFile(filePath, content, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg;
          if (usePreview) {
            successMsg = `<file-create path="${filePath}">
${content}
</file-create>

> **File Creation Preview**: This file will be created when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;
          } else {
            successMsg = `<file-create path="${filePath}">
${content}
</file-create>

> **File Created**: The file has been created successfully.`;
          }

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error(`Error creating file ${filePath}:`, error);
          const errorMsg = `<file-create path="${filePath}">
Error: ${error.message}
</file-create>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Also handle HTML entity versions for file create
      const fileCreateHtmlRegex = /&lt;file-create\s+path="([^"]+)"&gt;([\s\S]*?)&lt;\/file-create&gt;/g;
      while ((match = fileCreateHtmlRegex.exec(response)) !== null) {
        const filePath = match[1];
        const content = match[2].trim();
        console.log(`Processing HTML entity file create operation for ${filePath}`);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.createFile(filePath, content, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg = `<file-create path="${filePath}">
${content}
</file-create>

> **File Creation Preview**: This file will be created when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error(`Error creating file ${filePath}:`, error);
          const errorMsg = `<file-create path="${filePath}">
Error: ${error.message}
</file-create>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Process file modify operations - handle both with and without backticks
      const fileModifyRegex = /(?:``)?<file-modify\s+path="([^"]+)">([\s\S]*?)<\/file-modify>(?:``)?/g;

      while ((match = fileModifyRegex.exec(response)) !== null) {
        const filePath = match[1];
        const content = match[2].trim();
        console.log(`Processing file modify operation for ${filePath}`);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.writeFile(filePath, content, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg;
          if (usePreview) {
            successMsg = `<file-modify path="${filePath}">
${content}
</file-modify>

> **File Modification Preview**: This file will be modified when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;
          } else {
            successMsg = `<file-modify path="${filePath}">
${content}
</file-modify>

> **File Modified**: The file has been modified successfully.`;
          }

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error(`Error modifying file ${filePath}:`, error);
          const errorMsg = `<file-modify path="${filePath}">
Error: ${error.message}
</file-modify>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Also handle HTML entity versions for file modify
      const fileModifyHtmlRegex = /&lt;file-modify\s+path="([^"]+)"&gt;([\s\S]*?)&lt;\/file-modify&gt;/g;
      while ((match = fileModifyHtmlRegex.exec(response)) !== null) {
        const filePath = match[1];
        const content = match[2].trim();
        console.log(`Processing HTML entity file modify operation for ${filePath}`);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.writeFile(filePath, content, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg = `<file-modify path="${filePath}">
${content}
</file-modify>

> **File Modification Preview**: This file will be modified when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error(`Error modifying file ${filePath}:`, error);
          const errorMsg = `<file-modify path="${filePath}">
Error: ${error.message}
</file-modify>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Process terminal command execution - handle both with and without backticks
      // Use a more robust regex that can handle various formats
      const terminalCommandRegex = /(?:``)?<terminal-command(?:\s+cwd="([^"]*)")?\s*>([\s\S]*?)<\/terminal-command>(?:``)?/g;
      while ((match = terminalCommandRegex.exec(response)) !== null) {
        const cwd = match[1] || undefined; // Working directory is optional
        const command = match[2].trim();
        console.log(`Processing terminal command: ${command}, cwd: ${cwd || 'current directory'}`);

        try {
          // Get the workspace root if no working directory is specified
          const workingDir = cwd || (vscode.workspace.workspaceFolders?.[0]?.uri.fsPath);
          console.log(`Using working directory: ${workingDir || 'current directory'}`);

          // Execute the command
          console.log(`Executing command: ${command} in directory: ${workingDir || 'current directory'}`);
          const result = await agentTools.executeCommand(command, workingDir);

          // Format the output
          const stdout = result.stdout || '';
          const stderr = result.stderr || '';
          let output = '';

          if (stdout && stderr) {
            output = `${stdout}\n\nErrors:\n${stderr}`;
          } else if (stdout) {
            output = stdout;
          } else if (stderr) {
            output = 'Error: ' + stderr;
          } else {
            output = 'Command executed successfully with no output.';
          }

          const successMsg = `<terminal-command${cwd ? ` cwd="${cwd}"` : ''}>
${command}
</terminal-command>

\`\`\`
${output}
\`\`\``;

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error('Error executing command ' + command + ':', error);
          let errorMessage = '';

          if (typeof error === 'object' && error !== null) {
            if (error.stderr) {
              errorMessage = error.stderr;
            } else if (error.message) {
              errorMessage = error.message;
            } else {
              errorMessage = JSON.stringify(error);
            }
          } else {
            errorMessage = String(error);
          }

          const errorMsg = `<terminal-command${cwd ? ` cwd="${cwd}"` : ''}>
${command}
</terminal-command>

\`\`\`
Error: ${errorMessage || 'Unknown error occurred while executing the command'}
\`\`\``;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Also handle HTML entity versions for terminal commands
      const terminalCommandHtmlRegex = /&lt;terminal-command(?:\s+cwd="([^"]*)")?\s*&gt;([\s\S]*?)&lt;\/terminal-command&gt;/g;
      while ((match = terminalCommandHtmlRegex.exec(response)) !== null) {
        const cwd = match[1] || undefined; // Working directory is optional
        const command = match[2].trim();
        console.log(`Processing HTML entity terminal command: ${command}, cwd: ${cwd || 'current directory'}`);

        try {
          // Get the workspace root if no working directory is specified
          const workingDir = cwd || (vscode.workspace.workspaceFolders?.[0]?.uri.fsPath);
          console.log(`Using working directory: ${workingDir || 'current directory'}`);

          // Execute the command
          console.log(`Executing command: ${command} in directory: ${workingDir || 'current directory'}`);
          const result = await agentTools.executeCommand(command, workingDir);

          // Format the output
          const stdout = result.stdout || '';
          const stderr = result.stderr || '';
          let output = '';

          if (stdout && stderr) {
            output = `${stdout}\n\nErrors:\n${stderr}`;
          } else if (stdout) {
            output = stdout;
          } else if (stderr) {
            output = 'Error: ' + stderr;
          } else {
            output = 'Command executed successfully with no output.';
          }

          const successMsg = `<terminal-command${cwd ? ` cwd="${cwd}"` : ''}>
${command}
</terminal-command>

\`\`\`
${output}
\`\`\``;

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error('Error executing command ' + command + ':', error);
          let errorMessage = '';

          if (typeof error === 'object' && error !== null) {
            if (error.stderr) {
              errorMessage = error.stderr;
            } else if (error.message) {
              errorMessage = error.message;
            } else {
              errorMessage = JSON.stringify(error);
            }
          } else {
            errorMessage = String(error);
          }

          const errorMsg = `<terminal-command${cwd ? ` cwd="${cwd}"` : ''}>
${command}
</terminal-command>

\`\`\`
Error: ${errorMessage || 'Unknown error occurred while executing the command'}
\`\`\``;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Process file delete operations - handle both with and without backticks
      // First, try with closing tags
      const fileDeleteRegex = /(?:``)?<file-delete\s+path="([^"]+)">([\s\S]*?)<\/file-delete>(?:``)?/g;

      while ((match = fileDeleteRegex.exec(response)) !== null) {
        const filePath = match[1];
        console.log('Processing file delete operation for ' + filePath);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.deleteFile(filePath, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg;
          if (usePreview) {
            successMsg = `<file-delete path="${filePath}"></file-delete>

> **File Deletion Preview**: This file will be deleted when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;
          } else {
            successMsg = `<file-delete path="${filePath}"></file-delete>

> **File Deleted**: The file has been deleted successfully.`;
          }

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error('Error deleting file ' + filePath + ':', error);
          const errorMsg = `<file-delete path="${filePath}">
Error: ${error.message}
</file-delete>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Now try without closing tags (for backward compatibility)
      const fileDeleteSimpleRegex = /(?:``)?<file-delete\s+path="([^"]+)">(?:``)?/g;

      while ((match = fileDeleteSimpleRegex.exec(response)) !== null) {
        const filePath = match[1];
        console.log('Processing simple file delete operation for ' + filePath);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.deleteFile(filePath, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg;
          if (usePreview) {
            successMsg = `<file-delete path="${filePath}"></file-delete>

> **File Deletion Preview**: This file will be deleted when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;
          } else {
            successMsg = `<file-delete path="${filePath}"></file-delete>

> **File Deleted**: The file has been deleted successfully.`;
          }

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error('Error deleting file ' + filePath + ':', error);
          const errorMsg = `<file-delete path="${filePath}">
Error: ${error.message}
</file-delete>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Also handle HTML entity versions for file delete
      const fileDeleteHtmlRegex = /&lt;file-delete\s+path="([^"]+)"&gt;([\s\S]*?)&lt;\/file-delete&gt;/g;
      while ((match = fileDeleteHtmlRegex.exec(response)) !== null) {
        const filePath = match[1];
        console.log('Processing HTML entity file delete operation for ' + filePath);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.deleteFile(filePath, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg = `<file-delete path="${filePath}"></file-delete>

> **File Deletion Preview**: This file will be deleted when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error('Error deleting file ' + filePath + ':', error);
          const errorMsg = `<file-delete path="${filePath}">
Error: ${error.message}
</file-delete>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }

      // Simple HTML entity version
      const fileDeleteSimpleHtmlRegex = /&lt;file-delete\s+path="([^"]+)"&gt;/g;
      while ((match = fileDeleteSimpleHtmlRegex.exec(response)) !== null) {
        const filePath = match[1];
        console.log('Processing simple HTML entity file delete operation for ' + filePath);

        try {
          // Always use preview mode for safety
          const usePreview = true;
          await agentTools.deleteFile(filePath, usePreview);

          // Get the number of pending changes
          const pendingChanges = await agentTools.getPendingFileChanges();
          const changeCount = pendingChanges.length;

          // Send file changes to the webview
          this.sendFileChanges();

          let successMsg = `<file-delete path="${filePath}"></file-delete>

> **File Deletion Preview**: This file will be deleted when you approve the changes. There are currently ${changeCount} pending file change(s). Click the File Changes button above the text area to review and apply changes.`;

          processedResponse = processedResponse.replace(match[0], successMsg);
        } catch (error) {
          console.error('Error deleting file ' + filePath + ':', error);
          const errorMsg = `<file-delete path="${filePath}">
Error: ${error.message}
</file-delete>`;
          processedResponse = processedResponse.replace(match[0], errorMsg);
        }
      }
    } catch (error) {
      console.error('Error processing file operations:', error);
    }

    return processedResponse;
  }

  getWebviewContent() {
    // Monolithic HTML: all CSS and JS inlined
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vidyadhara Chat (Monolithic)</title>
        <style>
          body { font-family: sans-serif; margin: 0; padding: 0; background: #181818; color: #fff; }
          #webview-marker { color: red; font-weight: bold; padding: 1em; }
          .center { text-align: center; margin-top: 2em; }
          .chat-container { max-width: 600px; margin: 2em auto; background: #222; border-radius: 8px; box-shadow: 0 2px 8px #0008; padding: 2em; }
          .input-row { display: flex; gap: 0.5em; margin-top: 1em; }
          .input-row input { flex: 1; padding: 0.5em; border-radius: 4px; border: none; }
          .input-row button { padding: 0.5em 1em; border-radius: 4px; border: none; background: #4caf50; color: #fff; font-weight: bold; cursor: pointer; }
        </style>
      </head>
      <body>
        <div id="webview-marker">Vidyadhara Webview Loaded (Monolithic)</div>
        <div class="chat-container">
          <div class="center"><h2>Vidyadhara AI Assistant</h2></div>
          <div id="chat-log" style="min-height: 100px;"></div>
          <div class="input-row">
            <input id="chat-input" type="text" placeholder="Type a message..." />
            <button id="send-btn">Send</button>
          </div>
        </div>
        <script>
          console.log('*** Vidyadhara main.js script loaded (monolithic) ***');
          const chatLog = document.getElementById('chat-log');
          const chatInput = document.getElementById('chat-input');
          const sendBtn = document.getElementById('send-btn');
          function addMessage(msg, who) {
            const div = document.createElement('div');
            div.textContent = (who ? who + ': ' : '') + msg;
            chatLog.appendChild(div);
          }
          sendBtn.addEventListener('click', () => {
            const msg = chatInput.value.trim();
            if (!msg) return;
            addMessage(msg, 'You');
            chatInput.value = '';
            // Simulate AI response
            setTimeout(() => addMessage('This is a static AI response.', 'AI'), 500);
          });
          chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') sendBtn.click();
          });
        </script>
      </body>
      </html>
    `;
  }
}

// This function has been replaced by inline code in the activate function

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}
