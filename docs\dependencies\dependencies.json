{"scripts\\dependency-analyzer.ts": {"imports": [], "exports": []}, "scripts\\simple-deps.ts": {"imports": ["fs", "path", "typescript"], "exports": []}, "src\\agentTools.ts": {"imports": ["vscode", "path", "fs", "util", "axios"], "exports": []}, "src\\AIChatProvider.ts": {"imports": ["vscode", "axios", "uuid"], "exports": []}, "src\\AIChatViewProvider.ts": {"imports": ["vscode", "path", "uuid"], "exports": []}, "src\\codeIndexer.ts": {"imports": ["vscode", "path", "fs", "util"], "exports": []}, "src\\codeStructureAnalyzer.ts": {"imports": ["vscode", "path", "fs", "util", "typescript"], "exports": []}, "src\\editors\\batchEditor.ts": {"imports": ["vscode"], "exports": []}, "src\\editors\\confirmationWorkflow.ts": {"imports": ["vscode"], "exports": []}, "src\\editors\\editHistory.ts": {"imports": ["vscode", "fs", "path", "util"], "exports": []}, "src\\editors\\editPreview.ts": {"imports": ["vscode"], "exports": []}, "src\\editors\\errorHandler.ts": {"imports": ["vscode", "fs", "path", "util"], "exports": []}, "src\\editors\\stringReplaceEditor.ts": {"imports": ["vscode", "fs", "path", "util"], "exports": []}, "src\\extension.ts": {"imports": ["vscode"], "exports": []}, "src\\fileChangePreview.ts": {"imports": ["vscode", "path", "fs", "util"], "exports": []}, "src\\managers\\codeIndexManager.ts": {"imports": ["vscode"], "exports": []}, "src\\managers\\diagnosticsManager.ts": {"imports": ["vscode"], "exports": []}, "src\\managers\\githubManager.ts": {"imports": ["vscode", "axios"], "exports": []}, "src\\managers\\memoryManager.ts": {"imports": ["vscode", "uuid"], "exports": []}, "src\\managers\\modeManager.ts": {"imports": ["vscode"], "exports": []}, "src\\managers\\processManager.ts": {"imports": ["vscode", "child_process", "uuid"], "exports": []}, "src\\managers\\webManager.ts": {"imports": ["axios", "vscode"], "exports": []}, "src\\messages.ts": {"imports": ["vscode"], "exports": []}, "src\\models\\chatMode.ts": {"imports": [], "exports": []}, "src\\models\\conversation.ts": {"imports": [], "exports": []}, "src\\models\\memory.ts": {"imports": [], "exports": []}, "src\\models\\message.ts": {"imports": [], "exports": []}, "src\\retrieval\\codebaseRetrieval.ts": {"imports": ["vscode", "fs", "path", "util"], "exports": []}, "src\\retrieval\\crossReferenceTracker.ts": {"imports": ["vscode", "path", "import"], "exports": []}, "src\\retrieval\\dependencyMapper.ts": {"imports": ["vscode", "path", "fs"], "exports": []}, "src\\retrieval\\RelationshipGraph.clean.ts": {"imports": ["vscode"], "exports": []}, "src\\retrieval\\relationshipGraph.fixed.ts": {"imports": ["vscode"], "exports": []}, "src\\retrieval\\relationshipGraph.new.ts": {"imports": ["vscode"], "exports": []}, "src\\retrieval\\relationshipGraph.ts": {"imports": ["vscode"], "exports": []}, "src\\retrieval\\symbolAnalyzer.ts": {"imports": ["vscode", "path"], "exports": []}, "src\\tasks\\taskManager.ts": {"imports": ["vscode"], "exports": []}, "src\\tasks\\taskScheduler.ts": {"imports": ["vscode"], "exports": []}, "src\\test\\editors\\stringReplaceEditor.test.ts": {"imports": ["assert", "vscode", "fs", "path"], "exports": []}, "src\\test\\runTest.ts": {"imports": ["path", "@vscode"], "exports": []}, "src\\test\\suite\\comprehensive.test.ts": {"imports": ["assert", "vscode", "path", "fs"], "exports": []}, "src\\ui\\diagnosticsPanelProvider.ts": {"imports": ["vscode", "path", "fs"], "exports": []}, "src\\ui\\editPreviewPanel.ts": {"imports": ["vscode", "path"], "exports": []}, "src\\ui\\fileChangePanelProvider.ts": {"imports": ["vscode", "path", "fs"], "exports": []}, "src\\ui\\githubPanelProvider.ts": {"imports": ["vscode", "path", "fs"], "exports": []}, "src\\ui\\memoryPanelProvider.ts": {"imports": ["vscode", "path", "fs"], "exports": []}, "src\\ui\\processPanelProvider.ts": {"imports": ["vscode", "path", "fs"], "exports": []}, "src\\ui\\taskPanel.ts": {"imports": ["vscode"], "exports": []}, "src\\ui\\webPanelProvider.ts": {"imports": ["vscode", "path", "fs"], "exports": []}, "src\\util.ts": {"imports": [], "exports": []}, "src\\utils\\embeddingUtils.ts": {"imports": ["vscode"], "exports": []}, "src\\utils\\fileUtils.ts": {"imports": ["vscode", "fs", "path", "util"], "exports": []}, "src\\utils\\processUtils.ts": {"imports": ["child_process", "util"], "exports": []}, "src\\utils\\vectorUtils.ts": {"imports": [], "exports": []}}