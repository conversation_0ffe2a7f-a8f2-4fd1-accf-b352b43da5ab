/* Task Panel Styles */

:root {
  --vscode-editor-background: var(--vscode-editor-background, #1e1e1e);
  --vscode-editor-foreground: var(--vscode-editor-foreground, #d4d4d4);
  --vscode-button-background: var(--vscode-button-background, #0e639c);
  --vscode-button-foreground: var(--vscode-button-foreground, #ffffff);
  --vscode-button-hoverBackground: var(--vscode-button-hoverBackground, #1177bb);
  --vscode-input-background: var(--vscode-input-background, #3c3c3c);
  --vscode-input-foreground: var(--vscode-input-foreground, #cccccc);
  --vscode-panel-border: var(--vscode-panel-border, #2d2d30);
}

body {
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  margin: 0;
  padding: 0;
  line-height: 1.4;
}

.container {
  max-width: 100%;
  margin: 0;
  padding: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.btn-primary {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn-primary:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.btn-secondary {
  background-color: var(--vscode-button-secondaryBackground, #3c3c3c);
  color: var(--vscode-button-secondaryForeground, #cccccc);
}

.btn-secondary:hover {
  background-color: var(--vscode-button-secondaryHoverBackground, #4c4c4c);
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  font-size: 14px;
}

.btn-icon:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
}

/* Dashboard */
.dashboard {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-widget-border);
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--vscode-descriptionForeground);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
}

/* Filters */
.filters {
  display: flex;
  gap: 16px;
  align-items: end;
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-widget-border);
  border-radius: 6px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.filter-group select,
.filter-group input {
  padding: 4px 8px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 3px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 13px;
}

/* Task Views */
.task-views {
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  overflow: hidden;
}

.view-tabs {
  display: flex;
  background-color: var(--vscode-editorGroupHeader-tabsBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.tab-btn {
  padding: 12px 16px;
  border: none;
  background: none;
  color: var(--vscode-tab-inactiveForeground);
  cursor: pointer;
  font-size: 13px;
  border-bottom: 2px solid transparent;
}

.tab-btn.active {
  color: var(--vscode-tab-activeForeground);
  border-bottom-color: var(--vscode-tab-activeBorder);
  background-color: var(--vscode-tab-activeBackground);
}

.tab-btn:hover:not(.active) {
  color: var(--vscode-tab-hoverForeground);
  background-color: var(--vscode-tab-hoverBackground);
}

.view-content {
  display: none;
  padding: 16px;
}

.view-content.active {
  display: block;
}

/* Task List */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-widget-border);
  border-radius: 6px;
  padding: 16px;
  transition: border-color 0.2s ease;
}

.task-item:hover {
  border-color: var(--vscode-focusBorder);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-name {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.task-state {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.state-not_started {
  background-color: #6c757d;
  color: white;
}

.state-in_progress {
  background-color: #007acc;
  color: white;
}

.state-blocked {
  background-color: #dc3545;
  color: white;
}

.state-review {
  background-color: #ffc107;
  color: black;
}

.state-complete {
  background-color: #28a745;
  color: white;
}

.state-cancelled {
  background-color: #6c757d;
  color: white;
}

.task-priority {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
}

.priority-low {
  background-color: #28a745;
  color: white;
}

.priority-medium {
  background-color: #ffc107;
  color: black;
}

.priority-high {
  background-color: #fd7e14;
  color: white;
}

.priority-critical {
  background-color: #dc3545;
  color: white;
}

.task-actions {
  display: flex;
  gap: 4px;
}

.task-details {
  margin-bottom: 8px;
}

.task-description {
  margin: 0 0 8px 0;
  color: var(--vscode-descriptionForeground);
}

.task-assignee,
.task-due-date,
.task-hours {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  margin-bottom: 4px;
}

.task-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag {
  background-color: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
}

.subtasks {
  margin-left: 20px;
  margin-top: 12px;
  border-left: 2px solid var(--vscode-panel-border);
  padding-left: 16px;
}

/* Kanban Board */
.kanban-board {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  min-height: 500px;
}

.kanban-column {
  flex: 0 0 300px;
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-widget-border);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.column-header {
  padding: 12px 16px;
  background-color: var(--vscode-editorGroupHeader-tabsBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.column-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.task-count {
  background-color: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.column-tasks {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kanban-task {
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: 12px;
  cursor: grab;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.kanban-task:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.kanban-task h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.kanban-task p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.assignee {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  padding: 2px 6px;
  border-radius: 10px;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  width: 90%;
  max-width: 600px;
  max-height: 90%;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
}

.close {
  font-size: 24px;
  cursor: pointer;
  color: var(--vscode-descriptionForeground);
}

.close:hover {
  color: var(--vscode-editor-foreground);
}

.modal-body {
  padding: 16px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid var(--vscode-panel-border);
}

/* Form */
.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 13px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 3px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 13px;
  font-family: inherit;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group select[multiple] {
  min-height: 100px;
}

/* Responsive */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .kanban-board {
    flex-direction: column;
  }
  
  .kanban-column {
    flex: none;
  }
  
  .form-row {
    flex-direction: column;
  }
}
