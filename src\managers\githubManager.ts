import * as vscode from 'vscode';
import axios from 'axios';
import { executeCommand } from '../utils/processUtils';

export class GithubManager {
  private apiBaseUrl: string = 'https://api.github.com';
  private token: string = '';
  private owner: string = '';
  private repo: string = '';
  private defaultBranch: string = 'main';

  constructor() {
    this.loadConfiguration();

    // Listen for configuration changes
    vscode.workspace.onDidChangeConfiguration(e => {
      if (e.affectsConfiguration('vidyadhara.github')) {
        this.loadConfiguration();
      }
    });
  }

  public updateConfig(config: {
    token?: string;
    owner?: string;
    repo?: string;
    branch?: string;
  }): void {
    if (config.token !== undefined) this.token = config.token;
    if (config.owner !== undefined) this.owner = config.owner;
    if (config.repo !== undefined) this.repo = config.repo;
    if (config.branch !== undefined) this.defaultBranch = config.branch;
    
    // Save to configuration
    const extensionConfig = vscode.workspace.getConfiguration('vidyadhara.github');
    extensionConfig.update('token', this.token, vscode.ConfigurationTarget.Global);
    extensionConfig.update('owner', this.owner, vscode.ConfigurationTarget.Global);
    extensionConfig.update('repo', this.repo, vscode.ConfigurationTarget.Global);
    extensionConfig.update('defaultBranch', this.defaultBranch, vscode.ConfigurationTarget.Global);
  }

  public async testConnection(): Promise<boolean> {
    if (!this.token || !this.owner || !this.repo) {
      return false;
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}`,
        {
          headers: this.getHeaders(),
          validateStatus: () => true // Don't throw on 4xx/5xx
        }
      );

      // 200-299 means success, 404 means not found, 401/403 means auth failed
      if (response.status === 200) {
        // Verify we have at least read access
        const permissions = response.data?.permissions;
        return !!permissions?.pull || !!permissions?.push || !!permissions?.admin;
      }
      
      return false;
    } catch (error) {
      console.error('Error testing GitHub connection:', error);
      return false;
    }
  }

  private loadConfiguration(): void {
    const config = vscode.workspace.getConfiguration('vidyadhara.github');
    this.token = config.get<string>('token') || '';
    this.owner = config.get<string>('owner') || '';
    this.repo = config.get<string>('repo') || '';
    this.defaultBranch = config.get<string>('defaultBranch') || 'main';

    if (!this.token || !this.owner || !this.repo) {
      this.detectRepositoryInfo();
    }
  }

  private async detectRepositoryInfo(): Promise<void> {
    try {
      // Try to get repository info from git
      const { stdout: remoteUrl } = await executeCommand('git config --get remote.origin.url');

      if (remoteUrl) {
        // Parse GitHub URL to extract owner and repo
        const match = remoteUrl.trim().match(/github\.com[:/]([^/]+)\/([^.]+)(?:\.git)?$/);

        if (match) {
          this.owner = match[1];
          this.repo = match[2];

          // Save to configuration
          const config = vscode.workspace.getConfiguration('vidyadhara.github');
          await config.update('owner', this.owner, vscode.ConfigurationTarget.Workspace);
          await config.update('repo', this.repo, vscode.ConfigurationTarget.Workspace);
        }
      }
    } catch (error) {
      console.error('Error detecting repository info:', error);
    }
  }

  public async getRepositoryInfo(): Promise<any> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(`${this.apiBaseUrl}/repos/${this.owner}/${this.repo}`, {
        headers: this.getHeaders()
      });

      return response.data;
    } catch (error) {
      console.error('Error getting repository info:', error);
      throw error;
    }
  }

  public async createPullRequest(title: string, body: string, head: string, base: string): Promise<any> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.post(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/pulls`,
        {
          title,
          body,
          head,
          base
        },
        {
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error creating pull request:', error);
      throw error;
    }
  }

  public async getIssues(state: string = 'open'): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/issues`,
        {
          params: { state },
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting issues:', error);
      throw error;
    }
  }

  public async createIssue(title: string, body: string, labels: string[] = []): Promise<any> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.post(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/issues`,
        {
          title,
          body,
          labels
        },
        {
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error creating issue:', error);
      throw error;
    }
  }

  public async getPullRequests(state: string = 'open'): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/pulls`,
        {
          params: { state },
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting pull requests:', error);
      throw error;
    }
  }

  public async getBranches(): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/branches`,
        {
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting branches:', error);
      throw error;
    }
  }

  public async getCommits(branch: string = 'main'): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/commits`,
        {
          params: { sha: branch },
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting commits:', error);
      throw error;
    }
  }

  /**
   * Get pull request details
   * @param prNumber Pull request number
   * @returns Pull request details
   */
  public async getPullRequestDetails(prNumber: number): Promise<any> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/pulls/${prNumber}`,
        {
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error getting pull request #${prNumber}:`, error);
      throw error;
    }
  }

  /**
   * Get pull request files
   * @param prNumber Pull request number
   * @returns Files changed in the pull request
   */
  public async getPullRequestFiles(prNumber: number): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/pulls/${prNumber}/files`,
        {
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error getting files for pull request #${prNumber}:`, error);
      throw error;
    }
  }

  /**
   * Get pull request reviews
   * @param prNumber Pull request number
   * @returns Reviews for the pull request
   */
  public async getPullRequestReviews(prNumber: number): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/pulls/${prNumber}/reviews`,
        {
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error getting reviews for pull request #${prNumber}:`, error);
      throw error;
    }
  }

  /**
   * Create a review for a pull request
   * @param prNumber Pull request number
   * @param body Review body
   * @param event Review event (APPROVE, REQUEST_CHANGES, COMMENT)
   * @param comments Review comments
   * @returns Created review
   */
  public async createPullRequestReview(
    prNumber: number,
    body: string,
    event: 'APPROVE' | 'REQUEST_CHANGES' | 'COMMENT',
    comments: Array<{
      path: string;
      position: number;
      body: string;
    }> = []
  ): Promise<any> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.post(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/pulls/${prNumber}/reviews`,
        {
          body,
          event,
          comments
        },
        {
          headers: this.getHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error creating review for pull request #${prNumber}:`, error);
      throw error;
    }
  }

  /**
   * Get GitHub Actions workflows
   * @returns Workflows in the repository
   */
  public async getWorkflows(): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/actions/workflows`,
        {
          headers: this.getHeaders()
        }
      );

      return response.data.workflows;
    } catch (error) {
      console.error('Error getting workflows:', error);
      throw error;
    }
  }

  /**
   * Get workflow runs
   * @param workflowId Workflow ID
   * @returns Runs for the workflow
   */
  public async getWorkflowRuns(workflowId: number): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/actions/workflows/${workflowId}/runs`,
        {
          headers: this.getHeaders()
        }
      );

      return response.data.workflow_runs;
    } catch (error) {
      console.error(`Error getting runs for workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Get check runs for a commit
   * @param commitSha Commit SHA
   * @returns Check runs for the commit
   */
  public async getCheckRuns(commitSha: string): Promise<any[]> {
    if (!this.token || !this.owner || !this.repo) {
      throw new Error('GitHub configuration is incomplete. Please set token, owner, and repo in settings.');
    }

    try {
      const response = await axios.get(
        `${this.apiBaseUrl}/repos/${this.owner}/${this.repo}/commits/${commitSha}/check-runs`,
        {
          headers: {
            ...this.getHeaders(),
            'Accept': 'application/vnd.github.v3+json'
          }
        }
      );

      return response.data.check_runs;
    } catch (error) {
      console.error(`Error getting check runs for commit ${commitSha}:`, error);
      throw error;
    }
  }

  /**
   * Get relevant GitHub information based on a query
   * @param query Query string
   * @returns Formatted GitHub information
   */
  public async getRelevantGithubInfo(query: string): Promise<string> {
    try {
      // Analyze the query to determine what information to retrieve
      const lowerQuery = query.toLowerCase();
      let info = '';

      // Get repository info for all queries
      const repoInfo = await this.getRepositoryInfo();
      info += `# Repository: ${repoInfo.full_name}\n\n`;

      // Check for PR-related queries
      if (lowerQuery.includes('pr') || lowerQuery.includes('pull request')) {
        const prs = await this.getPullRequests('open');

        info += `## Open Pull Requests (${prs.length})\n\n`;
        for (let i = 0; i < Math.min(prs.length, 5); i++) {
          const pr = prs[i];
          info += `- #${pr.number}: ${pr.title}\n`;
          info += `  - Branch: ${pr.head.ref} → ${pr.base.ref}\n`;
          info += `  - Author: ${pr.user.login}\n`;
          info += `  - Created: ${new Date(pr.created_at).toLocaleDateString()}\n`;

          // Get PR reviews if specifically asked about reviews
          if (lowerQuery.includes('review')) {
            try {
              const reviews = await this.getPullRequestReviews(pr.number);
              if (reviews.length > 0) {
                info += `  - Reviews:\n`;
                for (const review of reviews) {
                  info += `    - ${review.user.login}: ${review.state}\n`;
                }
              } else {
                info += `  - No reviews yet\n`;
              }
            } catch (error) {
              console.error(`Error getting reviews for PR #${pr.number}:`, error);
            }
          }
        }
      }

      // Check for issue-related queries
      if (lowerQuery.includes('issue') || lowerQuery.includes('bug') || lowerQuery.includes('feature')) {
        const issues = await this.getIssues('open');

        info += `## Open Issues (${issues.length})\n\n`;
        for (let i = 0; i < Math.min(issues.length, 5); i++) {
          const issue = issues[i];
          info += `- #${issue.number}: ${issue.title}\n`;
          info += `  - Author: ${issue.user.login}\n`;
          info += `  - Created: ${new Date(issue.created_at).toLocaleDateString()}\n`;

          // Add labels if available
          if (issue.labels && issue.labels.length > 0) {
            info += `  - Labels: ${issue.labels.map((l: any) => l.name).join(', ')}\n`;
          }
        }
      }

      // Check for workflow/CI-related queries
      if (lowerQuery.includes('workflow') || lowerQuery.includes('action') || lowerQuery.includes('ci') || lowerQuery.includes('build')) {
        try {
          const workflows = await this.getWorkflows();

          info += `## GitHub Actions Workflows (${workflows.length})\n\n`;
          for (let i = 0; i < Math.min(workflows.length, 5); i++) {
            const workflow = workflows[i];
            info += `- ${workflow.name}\n`;
            info += `  - Path: ${workflow.path}\n`;
            info += `  - State: ${workflow.state}\n`;

            // Get recent runs for this workflow
            try {
              const runs = await this.getWorkflowRuns(workflow.id);
              if (runs.length > 0) {
                const latestRun = runs[0];
                info += `  - Latest run: ${latestRun.status} (${latestRun.conclusion})\n`;
                info += `  - Run at: ${new Date(latestRun.created_at).toLocaleString()}\n`;
              }
            } catch (error) {
              console.error(`Error getting runs for workflow ${workflow.id}:`, error);
            }
          }
        } catch (error) {
          console.error('Error getting workflows:', error);
        }
      }

      // Check for branch-related queries
      if (lowerQuery.includes('branch') || lowerQuery.includes('branches')) {
        const branches = await this.getBranches();

        info += `## Branches (${branches.length})\n\n`;
        for (let i = 0; i < Math.min(branches.length, 10); i++) {
          const branch = branches[i];
          info += `- ${branch.name}${branch.name === this.defaultBranch ? ' (default)' : ''}\n`;
        }
      }

      // Check for commit-related queries
      if (lowerQuery.includes('commit') || lowerQuery.includes('commits')) {
        const commits = await this.getCommits(this.defaultBranch);

        info += `## Recent Commits to ${this.defaultBranch}\n\n`;
        for (let i = 0; i < Math.min(commits.length, 5); i++) {
          const commit = commits[i];
          info += `- ${commit.sha.substring(0, 7)}: ${commit.commit.message.split('\n')[0]}\n`;
          info += `  - Author: ${commit.commit.author.name}\n`;
          info += `  - Date: ${new Date(commit.commit.author.date).toLocaleString()}\n`;

          // Get check runs for this commit
          try {
            const checkRuns = await this.getCheckRuns(commit.sha);
            if (checkRuns.length > 0) {
              info += `  - Checks: ${checkRuns.map(check => `${check.name} (${check.conclusion || check.status})`).join(', ')}\n`;
            }
          } catch (error) {
            console.error(`Error getting check runs for commit ${commit.sha}:`, error);
          }
        }
      }

      // If no specific information was requested, provide a summary
      if (info === `# Repository: ${repoInfo.full_name}\n\n`) {
        const issues = await this.getIssues('open');
        const prs = await this.getPullRequests('open');

        info += `## Summary\n\n`;
        info += `- Description: ${repoInfo.description || 'No description'}\n`;
        info += `- Default branch: ${repoInfo.default_branch}\n`;
        info += `- Stars: ${repoInfo.stargazers_count}\n`;
        info += `- Forks: ${repoInfo.forks_count}\n`;
        info += `- Open issues: ${repoInfo.open_issues_count}\n`;
        info += `- Open pull requests: ${prs.length}\n`;

        info += `\n## Open Issues (${issues.length})\n\n`;
        for (let i = 0; i < Math.min(issues.length, 5); i++) {
          const issue = issues[i];
          info += `- #${issue.number}: ${issue.title}\n`;
        }

        info += `\n## Open Pull Requests (${prs.length})\n\n`;
        for (let i = 0; i < Math.min(prs.length, 5); i++) {
          const pr = prs[i];
          info += `- #${pr.number}: ${pr.title}\n`;
        }
      }

      return info;
    } catch (error) {
      console.error('Error getting GitHub info:', error);
      return 'Error retrieving GitHub information. Please check your GitHub configuration.';
    }
  }

  private getHeaders(): Record<string, string> {
    return {
      'Authorization': `token ${this.token}`,
      'Accept': 'application/vnd.github.v3+json',
      'Content-Type': 'application/json'
    };
  }
}
