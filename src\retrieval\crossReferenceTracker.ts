import * as vscode from 'vscode';
import * as path from 'path';
import { CodeSymbol } from './symbolAnalyzer';

export interface CrossReference {
  symbol: CodeSymbol;
  referencedBy: ReferenceLocation[];
  references: ReferenceLocation[];
  totalUsages: number;
  isDeadCode: boolean;
  isHotspot: boolean;
}

export interface ReferenceLocation {
  filePath: string;
  line: number;
  column: number;
  context: string;
  type: 'definition' | 'call' | 'import' | 'type_reference' | 'assignment';
  scope: string;
}

export interface RefactoringOpportunity {
  type: 'rename' | 'extract_method' | 'inline' | 'move' | 'remove_unused';
  symbol: CodeSymbol;
  description: string;
  impact: ReferenceLocation[];
  effort: 'low' | 'medium' | 'high';
  benefit: string;
}

export interface UnusedCodeReport {
  unusedSymbols: CodeSymbol[];
  unusedImports: Array<{ filePath: string; line: number; import: string }>;
  deadFiles: string[];
  totalSavings: { lines: number; files: number };
}

export class CrossReferenceTracker {
  private crossReferences: Map<string, CrossReference> = new Map();
  private reverseIndex: Map<string, Set<string>> = new Map(); // file -> symbols
  private workspaceRoot: string;

  constructor() {
    this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
  }

  /**
   * Build cross-reference index for all symbols
   */
  async buildCrossReferenceIndex(
    symbols: Map<string, CodeSymbol[]>,
    codebase: Map<string, string>
  ): Promise<void> {
    console.log('Building cross-reference index...');
    
    this.crossReferences.clear();
    this.reverseIndex.clear();

    // First pass: index all symbols
    for (const [filePath, fileSymbols] of symbols) {
      const symbolIds = new Set<string>();
      
      for (const symbol of fileSymbols) {
        const symbolId = this.generateSymbolId(symbol);
        symbolIds.add(symbolId);
        
        const crossRef: CrossReference = {
          symbol,
          referencedBy: [],
          references: [],
          totalUsages: 0,
          isDeadCode: false,
          isHotspot: false
        };
        
        this.crossReferences.set(symbolId, crossRef);
      }
      
      this.reverseIndex.set(filePath, symbolIds);
    }

    // Second pass: find all references
    for (const [filePath, content] of codebase) {
      await this.analyzeFileReferences(filePath, content, symbols);
    }

    // Third pass: analyze usage patterns
    this.analyzeUsagePatterns();

    console.log(`Cross-reference index built: ${this.crossReferences.size} symbols indexed`);
  }

  /**
   * Find all references to a specific symbol
   */
  async findAllReferences(symbol: CodeSymbol): Promise<ReferenceLocation[]> {
    const symbolId = this.generateSymbolId(symbol);
    const crossRef = this.crossReferences.get(symbolId);
    
    if (!crossRef) {
      return [];
    }

    return [...crossRef.referencedBy, ...crossRef.references];
  }

  /**
   * Find all symbols referenced by a specific symbol
   */
  async findSymbolReferences(symbol: CodeSymbol): Promise<ReferenceLocation[]> {
    const symbolId = this.generateSymbolId(symbol);
    const crossRef = this.crossReferences.get(symbolId);
    
    return crossRef ? crossRef.references : [];
  }

  /**
   * Find all symbols that reference a specific symbol
   */
  async findSymbolReferencedBy(symbol: CodeSymbol): Promise<ReferenceLocation[]> {
    const symbolId = this.generateSymbolId(symbol);
    const crossRef = this.crossReferences.get(symbolId);
    
    return crossRef ? crossRef.referencedBy : [];
  }

  /**
   * Detect unused code (dead code analysis)
   */
  async detectUnusedCode(): Promise<UnusedCodeReport> {
    const unusedSymbols: CodeSymbol[] = [];
    const unusedImports: Array<{ filePath: string; line: number; import: string }> = [];
    const deadFiles: string[] = [];

    // Find unused symbols
    for (const crossRef of this.crossReferences.values()) {
      if (crossRef.totalUsages === 0 && !this.isEntryPoint(crossRef.symbol)) {
        unusedSymbols.push(crossRef.symbol);
        crossRef.isDeadCode = true;
      }
    }

    // Find unused imports
    // This would require analyzing import statements and checking if imported symbols are used

    // Find dead files
    for (const filePath of this.reverseIndex.keys()) {
      const fileSymbols = this.reverseIndex.get(filePath);
      if (fileSymbols) {
        const allUnused = Array.from(fileSymbols).every(symbolId => {
          const crossRef = this.crossReferences.get(symbolId);
          return crossRef && crossRef.isDeadCode;
        });
        
        if (allUnused && !this.isEntryFile(filePath)) {
          deadFiles.push(filePath);
        }
      }
    }

    const totalSavings = {
      lines: unusedSymbols.reduce((sum, symbol) => sum + this.estimateSymbolLines(symbol), 0),
      files: deadFiles.length
    };

    return {
      unusedSymbols,
      unusedImports,
      deadFiles,
      totalSavings
    };
  }

  /**
   * Identify refactoring opportunities
   */
  async identifyRefactoringOpportunities(): Promise<RefactoringOpportunity[]> {
    const opportunities: RefactoringOpportunity[] = [];

    for (const crossRef of this.crossReferences.values()) {
      // Suggest renaming for poorly named symbols
      if (this.isPoorlyNamed(crossRef.symbol)) {
        opportunities.push({
          type: 'rename',
          symbol: crossRef.symbol,
          description: `Consider renaming '${crossRef.symbol.name}' to be more descriptive`,
          impact: crossRef.referencedBy,
          effort: crossRef.totalUsages > 10 ? 'high' : 'low',
          benefit: 'Improves code readability and maintainability'
        });
      }

      // Suggest extracting methods for complex functions
      if (crossRef.symbol.type === 'function' && this.isComplexFunction(crossRef.symbol)) {
        opportunities.push({
          type: 'extract_method',
          symbol: crossRef.symbol,
          description: `Function '${crossRef.symbol.name}' is complex and could be broken down`,
          impact: crossRef.referencedBy,
          effort: 'medium',
          benefit: 'Reduces complexity and improves testability'
        });
      }

      // Suggest inlining for simple, rarely used functions
      if (crossRef.symbol.type === 'function' && 
          crossRef.totalUsages <= 2 && 
          this.isSimpleFunction(crossRef.symbol)) {
        opportunities.push({
          type: 'inline',
          symbol: crossRef.symbol,
          description: `Function '${crossRef.symbol.name}' is simple and rarely used, consider inlining`,
          impact: crossRef.referencedBy,
          effort: 'low',
          benefit: 'Reduces code complexity and improves performance'
        });
      }

      // Suggest removing unused code
      if (crossRef.isDeadCode) {
        opportunities.push({
          type: 'remove_unused',
          symbol: crossRef.symbol,
          description: `Symbol '${crossRef.symbol.name}' is unused and can be removed`,
          impact: [],
          effort: 'low',
          benefit: 'Reduces codebase size and maintenance burden'
        });
      }

      // Suggest moving symbols to more appropriate locations
      if (this.shouldBeMoved(crossRef)) {
        opportunities.push({
          type: 'move',
          symbol: crossRef.symbol,
          description: `Symbol '${crossRef.symbol.name}' might belong in a different module`,
          impact: crossRef.referencedBy,
          effort: 'medium',
          benefit: 'Improves code organization and cohesion'
        });
      }
    }

    return opportunities.sort((a, b) => {
      const effortScore = { low: 1, medium: 2, high: 3 };
      return effortScore[a.effort] - effortScore[b.effort];
    });
  }

  /**
   * Get usage statistics for a symbol
   */
  getSymbolUsageStats(symbol: CodeSymbol): {
    totalUsages: number;
    usagesByFile: Map<string, number>;
    usagesByType: Map<string, number>;
    isHotspot: boolean;
    trend: 'increasing' | 'decreasing' | 'stable';
  } {
    const symbolId = this.generateSymbolId(symbol);
    const crossRef = this.crossReferences.get(symbolId);
    
    if (!crossRef) {
      return {
        totalUsages: 0,
        usagesByFile: new Map(),
        usagesByType: new Map(),
        isHotspot: false,
        trend: 'stable'
      };
    }

    const usagesByFile = new Map<string, number>();
    const usagesByType = new Map<string, number>();

    for (const ref of crossRef.referencedBy) {
      usagesByFile.set(ref.filePath, (usagesByFile.get(ref.filePath) || 0) + 1);
      usagesByType.set(ref.type, (usagesByType.get(ref.type) || 0) + 1);
    }

    return {
      totalUsages: crossRef.totalUsages,
      usagesByFile,
      usagesByType,
      isHotspot: crossRef.isHotspot,
      trend: 'stable' // Would need historical data to determine trend
    };
  }

  /**
   * Update references when code changes
   */
  async updateReferences(
    filePath: string,
    oldContent: string,
    newContent: string,
    symbols: Map<string, CodeSymbol[]>
  ): Promise<void> {
    // Remove old references for this file
    this.removeFileReferences(filePath);

    // Add new references
    await this.analyzeFileReferences(filePath, newContent, symbols);

    // Reanalyze usage patterns
    this.analyzeUsagePatterns();
  }

  /**
   * Private helper methods
   */
  private async analyzeFileReferences(
    filePath: string,
    content: string,
    allSymbols: Map<string, CodeSymbol[]>
  ): Promise<void> {
    const lines = content.split('\n');

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      
      // Find symbol references in this line
      for (const [, symbols] of allSymbols) {
        for (const symbol of symbols) {
          const references = this.findSymbolReferencesInLine(
            line,
            symbol,
            filePath,
            lineIndex + 1,
            lines
          );
          
          for (const ref of references) {
            this.addReference(symbol, ref);
          }
        }
      }
    }
  }

  private findSymbolReferencesInLine(
    line: string,
    symbol: CodeSymbol,
    filePath: string,
    lineNumber: number,
    allLines: string[]
  ): ReferenceLocation[] {
    const references: ReferenceLocation[] = [];
    const symbolName = symbol.name;
    let index = 0;

    while (true) {
      index = line.indexOf(symbolName, index);
      if (index === -1) break;

      // Check if it's a whole word match
      const before = index > 0 ? line[index - 1] : ' ';
      const after = index + symbolName.length < line.length ? line[index + symbolName.length] : ' ';
      
      if (!/\w/.test(before) && !/\w/.test(after)) {
        const refType = this.determineReferenceType(line, index, symbol);
        const scope = this.determineScope(allLines, lineNumber - 1);
        
        references.push({
          filePath,
          line: lineNumber,
          column: index,
          context: line.trim(),
          type: refType,
          scope
        });
      }
      
      index++;
    }

    return references;
  }

  private determineReferenceType(
    line: string,
    symbolIndex: number,
    symbol: CodeSymbol
  ): ReferenceLocation['type'] {
    const beforeSymbol = line.substring(0, symbolIndex).trim();
    const afterSymbol = line.substring(symbolIndex + symbol.name.length).trim();

    // Check if it's the definition line
    if (line.includes(`${symbol.type} ${symbol.name}`) || 
        line.includes(`function ${symbol.name}`) ||
        line.includes(`class ${symbol.name}`)) {
      return 'definition';
    }

    // Check if it's a function call
    if (afterSymbol.startsWith('(')) {
      return 'call';
    }

    // Check if it's an import
    if (beforeSymbol.includes('import') || line.includes('from')) {
      return 'import';
    }

    // Check if it's an assignment
    if (beforeSymbol.endsWith('=') || afterSymbol.startsWith('=')) {
      return 'assignment';
    }

    // Check if it's a type reference
    if (beforeSymbol.includes(':') || beforeSymbol.includes('<') || afterSymbol.startsWith('<')) {
      return 'type_reference';
    }

    return 'call'; // Default to call/reference
  }

  private determineScope(lines: string[], lineIndex: number): string {
    // Look backwards to find the containing scope
    for (let i = lineIndex; i >= 0; i--) {
      const line = lines[i].trim();
      
      if (line.includes('function ') || line.includes('class ') || line.includes('namespace ')) {
        return line;
      }
    }
    
    return 'global';
  }

  private addReference(symbol: CodeSymbol, reference: ReferenceLocation): void {
    const symbolId = this.generateSymbolId(symbol);
    const crossRef = this.crossReferences.get(symbolId);
    
    if (crossRef) {
      if (reference.filePath === symbol.filePath && reference.line === symbol.line) {
        // This is the definition, don't count as usage
        return;
      }
      
      crossRef.referencedBy.push(reference);
      crossRef.totalUsages++;
    }
  }

  private removeFileReferences(filePath: string): void {
    for (const crossRef of this.crossReferences.values()) {
      crossRef.referencedBy = crossRef.referencedBy.filter(ref => ref.filePath !== filePath);
      crossRef.references = crossRef.references.filter(ref => ref.filePath !== filePath);
      
      // Recalculate total usages
      crossRef.totalUsages = crossRef.referencedBy.length;
    }
  }

  private analyzeUsagePatterns(): void {
    for (const crossRef of this.crossReferences.values()) {
      // Mark as hotspot if heavily used
      crossRef.isHotspot = crossRef.totalUsages > 20;
      
      // Mark as dead code if not used
      crossRef.isDeadCode = crossRef.totalUsages === 0 && !this.isEntryPoint(crossRef.symbol);
    }
  }

  private generateSymbolId(symbol: CodeSymbol): string {
    return `${symbol.filePath}:${symbol.name}:${symbol.line}:${symbol.type}`;
  }

  private isEntryPoint(symbol: CodeSymbol): boolean {
    // Check if symbol is an entry point (main function, exported symbol, etc.)
    return symbol.name === 'main' || 
           symbol.name === 'index' ||
           symbol.scope.includes('export');
  }

  private isEntryFile(filePath: string): boolean {
    const fileName = path.basename(filePath);
    return fileName === 'index.ts' || 
           fileName === 'index.js' || 
           fileName === 'main.ts' ||
           fileName === 'main.js';
  }

  private estimateSymbolLines(symbol: CodeSymbol): number {
    // Estimate number of lines for a symbol
    switch (symbol.type) {
      case 'function': return 10;
      case 'class': return 20;
      case 'interface': return 5;
      default: return 1;
    }
  }

  private isPoorlyNamed(symbol: CodeSymbol): boolean {
    const name = symbol.name;
    
    // Check for single letter names (except common ones like 'i', 'x', 'y')
    if (name.length === 1 && !['i', 'j', 'k', 'x', 'y', 'z'].includes(name)) {
      return true;
    }
    
    // Check for generic names
    const genericNames = ['temp', 'tmp', 'data', 'item', 'obj', 'val', 'var'];
    if (genericNames.includes(name.toLowerCase())) {
      return true;
    }
    
    return false;
  }

  private isComplexFunction(symbol: CodeSymbol): boolean {
    // This would need access to the function body to calculate complexity
    // For now, return false as placeholder
    return false;
  }

  private isSimpleFunction(symbol: CodeSymbol): boolean {
    // This would need access to the function body to determine simplicity
    // For now, return false as placeholder
    return false;
  }

  private shouldBeMoved(crossRef: CrossReference): boolean {
    // Analyze if symbol should be moved based on usage patterns
    const usagesByFile = new Map<string, number>();
    
    for (const ref of crossRef.referencedBy) {
      usagesByFile.set(ref.filePath, (usagesByFile.get(ref.filePath) || 0) + 1);
    }
    
    // If most usages are in a different file, suggest moving
    const maxUsageFile = Array.from(usagesByFile.entries())
      .sort((a, b) => b[1] - a[1])[0];
    
    return maxUsageFile && 
           maxUsageFile[0] !== crossRef.symbol.filePath && 
           maxUsageFile[1] > crossRef.totalUsages * 0.7;
  }
}
