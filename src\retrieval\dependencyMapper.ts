import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { CodeSymbol, SymbolRelationship } from './symbolAnalyzer';

export interface DependencyNode {
  id: string;
  filePath: string;
  name: string;
  type: 'file' | 'module' | 'symbol';
  size: number;
  dependencies: string[];
  dependents: string[];
  level: number;
  isCircular: boolean;
  metadata: {
    lastModified: Date;
    lineCount: number;
    complexity: number;
    isExternal: boolean;
  };
}

export interface DependencyGraph {
  nodes: Map<string, DependencyNode>;
  edges: Map<string, DependencyEdge>;
  circularDependencies: CircularDependency[];
  levels: Map<number, string[]>;
  metrics: DependencyMetrics;
}

export interface DependencyEdge {
  from: string;
  to: string;
  type: 'import' | 'require' | 'dynamic' | 'reference';
  weight: number;
  isOptional: boolean;
}

export interface CircularDependency {
  cycle: string[];
  severity: 'low' | 'medium' | 'high';
  impact: string;
  suggestions: string[];
}

export interface DependencyMetrics {
  totalNodes: number;
  totalEdges: number;
  averageDependencies: number;
  maxDepth: number;
  circularCount: number;
  externalDependencies: number;
  couplingScore: number;
  cohesionScore: number;
}

export interface ImpactAnalysis {
  affectedFiles: string[];
  affectedSymbols: CodeSymbol[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  estimatedEffort: number;
  recommendations: string[];
}

export class DependencyMapper {
  private dependencyGraph: DependencyGraph;
  private workspaceRoot: string;

  constructor() {
    this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    this.dependencyGraph = this.initializeGraph();
  }

  /**
   * Build complete dependency graph for the codebase
   */
  async buildDependencyGraph(codebase: Map<string, string>): Promise<DependencyGraph> {
    console.log('Building dependency graph...');
    
    // Reset graph
    this.dependencyGraph = this.initializeGraph();

    // Step 1: Create nodes for all files
    await this.createFileNodes(codebase);

    // Step 2: Analyze imports and dependencies
    await this.analyzeDependencies(codebase);

    // Step 3: Calculate dependency levels
    this.calculateDependencyLevels();

    // Step 4: Detect circular dependencies
    this.detectCircularDependencies();

    // Step 5: Calculate metrics
    this.calculateMetrics();

    console.log(`Dependency graph built: ${this.dependencyGraph.nodes.size} nodes, ${this.dependencyGraph.edges.size} edges`);
    
    return this.dependencyGraph;
  }

  /**
   * Analyze impact of changing a specific file or symbol
   */
  async analyzeChangeImpact(
    targetPath: string,
    changeType: 'modify' | 'delete' | 'rename',
    codebase: Map<string, string>
  ): Promise<ImpactAnalysis> {
    const targetNode = this.findNodeByPath(targetPath);
    if (!targetNode) {
      throw new Error(`Target not found in dependency graph: ${targetPath}`);
    }

    const affectedFiles = new Set<string>();
    const affectedSymbols: CodeSymbol[] = [];
    
    // Find all dependents (files that depend on this file)
    this.findAllDependents(targetNode.id, affectedFiles);

    // Calculate risk level based on number of dependents and their importance
    const riskLevel = this.calculateRiskLevel(affectedFiles.size, targetNode);

    // Estimate effort based on complexity and dependencies
    const estimatedEffort = this.estimateChangeEffort(targetNode, affectedFiles.size, changeType);

    // Generate recommendations
    const recommendations = this.generateChangeRecommendations(targetNode, changeType, affectedFiles.size);

    return {
      affectedFiles: Array.from(affectedFiles),
      affectedSymbols,
      riskLevel,
      estimatedEffort,
      recommendations
    };
  }

  /**
   * Find circular dependencies in the codebase
   */
  detectCircularDependencies(): CircularDependency[] {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: string[][] = [];

    for (const nodeId of this.dependencyGraph.nodes.keys()) {
      if (!visited.has(nodeId)) {
        this.findCyclesFromNode(nodeId, visited, recursionStack, [], cycles);
      }
    }

    // Convert cycles to CircularDependency objects
    const circularDependencies = cycles.map(cycle => this.analyzeCycle(cycle));
    
    this.dependencyGraph.circularDependencies = circularDependencies;
    return circularDependencies;
  }

  /**
   * Get dependency path between two files
   */
  findDependencyPath(fromPath: string, toPath: string): string[] | null {
    const fromNode = this.findNodeByPath(fromPath);
    const toNode = this.findNodeByPath(toPath);

    if (!fromNode || !toNode) {
      return null;
    }

    return this.findShortestPath(fromNode.id, toNode.id);
  }

  /**
   * Get files that would be affected by removing a dependency
   */
  getRemovalImpact(dependencyPath: string): string[] {
    const node = this.findNodeByPath(dependencyPath);
    if (!node) {
      return [];
    }

    const affected = new Set<string>();
    this.findAllDependents(node.id, affected);
    
    return Array.from(affected);
  }

  /**
   * Suggest refactoring opportunities based on dependency analysis
   */
  suggestRefactoring(): Array<{
    type: 'extract_module' | 'merge_files' | 'break_cycle' | 'reduce_coupling';
    description: string;
    files: string[];
    benefit: string;
    effort: 'low' | 'medium' | 'high';
  }> {
    const suggestions: Array<{
      type: 'extract_module' | 'merge_files' | 'break_cycle' | 'reduce_coupling';
      description: string;
      files: string[];
      benefit: string;
      effort: 'low' | 'medium' | 'high';
    }> = [];

    // Suggest breaking circular dependencies
    for (const cycle of this.dependencyGraph.circularDependencies) {
      if (cycle.severity === 'high') {
        suggestions.push({
          type: 'break_cycle',
          description: `Break circular dependency between ${cycle.cycle.join(' → ')}`,
          files: cycle.cycle,
          benefit: 'Improves maintainability and reduces coupling',
          effort: 'medium'
        });
      }
    }

    // Suggest extracting common modules
    const highlyDepended = this.findHighlyDependedFiles();
    for (const file of highlyDepended) {
      const node = this.dependencyGraph.nodes.get(file);
      if (node && node.dependents.length > 10) {
        suggestions.push({
          type: 'extract_module',
          description: `Extract common functionality from ${path.basename(node.filePath)}`,
          files: [node.filePath],
          benefit: 'Reduces duplication and improves reusability',
          effort: 'high'
        });
      }
    }

    // Suggest merging small, tightly coupled files
    const smallFiles = this.findSmallTightlyCoupledFiles();
    for (const fileGroup of smallFiles) {
      suggestions.push({
        type: 'merge_files',
        description: `Consider merging tightly coupled files: ${fileGroup.map(f => path.basename(f)).join(', ')}`,
        files: fileGroup,
        benefit: 'Reduces file count and simplifies structure',
        effort: 'low'
      });
    }

    return suggestions;
  }

  /**
   * Private helper methods
   */
  private initializeGraph(): DependencyGraph {
    return {
      nodes: new Map(),
      edges: new Map(),
      circularDependencies: [],
      levels: new Map(),
      metrics: {
        totalNodes: 0,
        totalEdges: 0,
        averageDependencies: 0,
        maxDepth: 0,
        circularCount: 0,
        externalDependencies: 0,
        couplingScore: 0,
        cohesionScore: 0
      }
    };
  }

  private async createFileNodes(codebase: Map<string, string>): Promise<void> {
    for (const [filePath, content] of codebase) {
      const stats = await this.getFileStats(filePath);
      const nodeId = this.generateNodeId(filePath);

      const node: DependencyNode = {
        id: nodeId,
        filePath,
        name: path.basename(filePath),
        type: 'file',
        size: content.length,
        dependencies: [],
        dependents: [],
        level: 0,
        isCircular: false,
        metadata: {
          lastModified: stats.mtime,
          lineCount: content.split('\n').length,
          complexity: this.calculateFileComplexity(content),
          isExternal: this.isExternalDependency(filePath)
        }
      };

      this.dependencyGraph.nodes.set(nodeId, node);
    }
  }

  private async analyzeDependencies(codebase: Map<string, string>): Promise<void> {
    for (const [filePath, content] of codebase) {
      const dependencies = this.extractDependencies(content, filePath);
      const fromNodeId = this.generateNodeId(filePath);
      const fromNode = this.dependencyGraph.nodes.get(fromNodeId);

      if (!fromNode) continue;

      for (const dep of dependencies) {
        const toNodeId = this.generateNodeId(dep.path);
        const toNode = this.dependencyGraph.nodes.get(toNodeId);

        if (toNode) {
          // Add dependency relationship
          fromNode.dependencies.push(toNodeId);
          toNode.dependents.push(fromNodeId);

          // Create edge
          const edgeId = `${fromNodeId}->${toNodeId}`;
          const edge: DependencyEdge = {
            from: fromNodeId,
            to: toNodeId,
            type: dep.type,
            weight: dep.weight,
            isOptional: dep.isOptional
          };

          this.dependencyGraph.edges.set(edgeId, edge);
        }
      }
    }
  }

  private extractDependencies(content: string, filePath: string): Array<{
    path: string;
    type: DependencyEdge['type'];
    weight: number;
    isOptional: boolean;
  }> {
    const dependencies: Array<{
      path: string;
      type: DependencyEdge['type'];
      weight: number;
      isOptional: boolean;
    }> = [];

    const lines = content.split('\n');

    for (const line of lines) {
      // ES6 imports
      const importMatch = line.match(/import\s+.*?\s+from\s+['"]([^'"]+)['"]/);
      if (importMatch) {
        const depPath = this.resolveDependencyPath(importMatch[1], filePath);
        if (depPath) {
          dependencies.push({
            path: depPath,
            type: 'import',
            weight: 1,
            isOptional: false
          });
        }
      }

      // CommonJS requires
      const requireMatch = line.match(/require\s*\(\s*['"]([^'"]+)['"]\s*\)/);
      if (requireMatch) {
        const depPath = this.resolveDependencyPath(requireMatch[1], filePath);
        if (depPath) {
          dependencies.push({
            path: depPath,
            type: 'require',
            weight: 1,
            isOptional: false
          });
        }
      }

      // Dynamic imports
      const dynamicImportMatch = line.match(/import\s*\(\s*['"]([^'"]+)['"]\s*\)/);
      if (dynamicImportMatch) {
        const depPath = this.resolveDependencyPath(dynamicImportMatch[1], filePath);
        if (depPath) {
          dependencies.push({
            path: depPath,
            type: 'dynamic',
            weight: 0.5,
            isOptional: true
          });
        }
      }
    }

    return dependencies;
  }

  private resolveDependencyPath(importPath: string, fromFile: string): string | null {
    // Handle relative imports
    if (importPath.startsWith('./') || importPath.startsWith('../')) {
      const resolvedPath = path.resolve(path.dirname(fromFile), importPath);
      
      // Try different extensions
      const extensions = ['.ts', '.js', '.tsx', '.jsx'];
      for (const ext of extensions) {
        const fullPath = resolvedPath + ext;
        if (fs.existsSync(fullPath)) {
          return fullPath;
        }
      }
      
      // Try index files
      for (const ext of extensions) {
        const indexPath = path.join(resolvedPath, `index${ext}`);
        if (fs.existsSync(indexPath)) {
          return indexPath;
        }
      }
    }

    // Handle absolute imports (would need module resolution logic)
    return null;
  }

  private calculateDependencyLevels(): void {
    const levels = new Map<number, string[]>();
    const nodelevels = new Map<string, number>();

    // Start with nodes that have no dependencies (level 0)
    const rootNodes = Array.from(this.dependencyGraph.nodes.values())
      .filter(node => node.dependencies.length === 0);

    let currentLevel = 0;
    let currentLevelNodes = rootNodes.map(n => n.id);

    while (currentLevelNodes.length > 0) {
      levels.set(currentLevel, [...currentLevelNodes]);
      
      for (const nodeId of currentLevelNodes) {
        nodelevels.set(nodeId, currentLevel);
        const node = this.dependencyGraph.nodes.get(nodeId);
        if (node) {
          node.level = currentLevel;
        }
      }

      // Find next level nodes
      const nextLevelNodes = new Set<string>();
      
      for (const nodeId of currentLevelNodes) {
        const node = this.dependencyGraph.nodes.get(nodeId);
        if (node) {
          for (const dependentId of node.dependents) {
            const dependent = this.dependencyGraph.nodes.get(dependentId);
            if (dependent) {
              // Check if all dependencies of this node are already processed
              const allDepsProcessed = dependent.dependencies.every(depId => 
                nodelevels.has(depId)
              );
              
              if (allDepsProcessed) {
                nextLevelNodes.add(dependentId);
              }
            }
          }
        }
      }

      currentLevel++;
      currentLevelNodes = Array.from(nextLevelNodes);
    }

    this.dependencyGraph.levels = levels;
  }

  private findCyclesFromNode(
    nodeId: string,
    visited: Set<string>,
    recursionStack: Set<string>,
    currentPath: string[],
    cycles: string[][]
  ): void {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    currentPath.push(nodeId);

    const node = this.dependencyGraph.nodes.get(nodeId);
    if (node) {
      for (const depId of node.dependencies) {
        if (!visited.has(depId)) {
          this.findCyclesFromNode(depId, visited, recursionStack, currentPath, cycles);
        } else if (recursionStack.has(depId)) {
          // Found a cycle
          const cycleStart = currentPath.indexOf(depId);
          const cycle = currentPath.slice(cycleStart);
          cycles.push([...cycle, depId]);
        }
      }
    }

    recursionStack.delete(nodeId);
    currentPath.pop();
  }

  private analyzeCycle(cycle: string[]): CircularDependency {
    const severity = this.calculateCycleSeverity(cycle);
    const impact = this.calculateCycleImpact(cycle);
    const suggestions = this.generateCycleBreakingSuggestions(cycle);

    return {
      cycle: cycle.map(nodeId => {
        const node = this.dependencyGraph.nodes.get(nodeId);
        return node ? node.filePath : nodeId;
      }),
      severity,
      impact,
      suggestions
    };
  }

  private calculateCycleSeverity(cycle: string[]): 'low' | 'medium' | 'high' {
    if (cycle.length <= 2) return 'low';
    if (cycle.length <= 4) return 'medium';
    return 'high';
  }

  private calculateCycleImpact(cycle: string[]): string {
    const totalDependents = cycle.reduce((sum, nodeId) => {
      const node = this.dependencyGraph.nodes.get(nodeId);
      return sum + (node ? node.dependents.length : 0);
    }, 0);

    return `Affects ${totalDependents} dependent files`;
  }

  private generateCycleBreakingSuggestions(cycle: string[]): string[] {
    return [
      'Extract common functionality into a separate module',
      'Use dependency injection to break direct dependencies',
      'Introduce interfaces to decouple implementations',
      'Consider using event-driven architecture'
    ];
  }

  private calculateMetrics(): void {
    const nodes = Array.from(this.dependencyGraph.nodes.values());
    const edges = Array.from(this.dependencyGraph.edges.values());

    this.dependencyGraph.metrics = {
      totalNodes: nodes.length,
      totalEdges: edges.length,
      averageDependencies: nodes.reduce((sum, n) => sum + n.dependencies.length, 0) / nodes.length,
      maxDepth: Math.max(...nodes.map(n => n.level)),
      circularCount: this.dependencyGraph.circularDependencies.length,
      externalDependencies: nodes.filter(n => n.metadata.isExternal).length,
      couplingScore: this.calculateCouplingScore(nodes),
      cohesionScore: this.calculateCohesionScore(nodes)
    };
  }

  private calculateCouplingScore(nodes: DependencyNode[]): number {
    // Higher score means higher coupling (worse)
    const totalConnections = nodes.reduce((sum, n) => sum + n.dependencies.length + n.dependents.length, 0);
    return totalConnections / nodes.length;
  }

  private calculateCohesionScore(nodes: DependencyNode[]): number {
    // Higher score means higher cohesion (better)
    // This is a simplified calculation
    return 1.0; // Placeholder
  }

  private findNodeByPath(filePath: string): DependencyNode | null {
    for (const node of this.dependencyGraph.nodes.values()) {
      if (node.filePath === filePath) {
        return node;
      }
    }
    return null;
  }

  private findAllDependents(nodeId: string, result: Set<string>): void {
    const node = this.dependencyGraph.nodes.get(nodeId);
    if (!node) return;

    for (const dependentId of node.dependents) {
      const dependent = this.dependencyGraph.nodes.get(dependentId);
      if (dependent && !result.has(dependent.filePath)) {
        result.add(dependent.filePath);
        this.findAllDependents(dependentId, result);
      }
    }
  }

  private calculateRiskLevel(affectedCount: number, node: DependencyNode): 'low' | 'medium' | 'high' | 'critical' {
    if (affectedCount === 0) return 'low';
    if (affectedCount <= 5) return 'medium';
    if (affectedCount <= 15) return 'high';
    return 'critical';
  }

  private estimateChangeEffort(node: DependencyNode, affectedCount: number, changeType: string): number {
    let baseEffort = 1;
    
    if (changeType === 'delete') baseEffort = 3;
    if (changeType === 'rename') baseEffort = 2;
    
    return baseEffort + (affectedCount * 0.5) + (node.metadata.complexity * 0.1);
  }

  private generateChangeRecommendations(node: DependencyNode, changeType: string, affectedCount: number): string[] {
    const recommendations: string[] = [];
    
    if (affectedCount > 10) {
      recommendations.push('Consider making changes incrementally');
      recommendations.push('Create comprehensive tests before making changes');
    }
    
    if (changeType === 'delete') {
      recommendations.push('Ensure all dependents have alternative implementations');
    }
    
    return recommendations;
  }

  private findShortestPath(fromId: string, toId: string): string[] | null {
    // BFS to find shortest path
    const queue = [[fromId]];
    const visited = new Set([fromId]);

    while (queue.length > 0) {
      const path = queue.shift()!;
      const currentId = path[path.length - 1];

      if (currentId === toId) {
        return path.map(nodeId => {
          const node = this.dependencyGraph.nodes.get(nodeId);
          return node ? node.filePath : nodeId;
        });
      }

      const node = this.dependencyGraph.nodes.get(currentId);
      if (node) {
        for (const depId of node.dependencies) {
          if (!visited.has(depId)) {
            visited.add(depId);
            queue.push([...path, depId]);
          }
        }
      }
    }

    return null;
  }

  private findHighlyDependedFiles(): string[] {
    return Array.from(this.dependencyGraph.nodes.values())
      .filter(node => node.dependents.length > 5)
      .map(node => node.id);
  }

  private findSmallTightlyCoupledFiles(): string[][] {
    // Find groups of small files that depend heavily on each other
    const groups: string[][] = [];
    // Implementation would analyze coupling between small files
    return groups;
  }

  private generateNodeId(filePath: string): string {
    return path.relative(this.workspaceRoot, filePath).replace(/\\/g, '/');
  }

  private async getFileStats(filePath: string): Promise<fs.Stats> {
    return fs.promises.stat(filePath);
  }

  private calculateFileComplexity(content: string): number {
    // Simple complexity calculation based on various factors
    const lines = content.split('\n');
    let complexity = 0;

    for (const line of lines) {
      if (line.includes('if') || line.includes('for') || line.includes('while')) {
        complexity++;
      }
    }

    return complexity;
  }

  private isExternalDependency(filePath: string): boolean {
    return filePath.includes('node_modules') || !filePath.startsWith(this.workspaceRoot);
  }
}
