const fs = require('fs');
const path = require('path');
import type * as tsTypes from 'typescript';
const ts: typeof tsTypes = require('typescript');

interface FileDependencies {
  filePath: string;
  imports: string[];
  exports: string[];
  typeReferences: string[];
}

interface ProjectDependencies {
  [filePath: string]: FileDependencies;
}

class DependencyAnalyzer {
  private projectRoot: string;
  private tsConfigPath: string;
  private program: tsTypes.Program;
  private checker: tsTypes.TypeChecker;
  private dependencies: ProjectDependencies = {};
  private fileExtensions = ['.ts', '.tsx'];
  private excludedDirs = ['node_modules', '.git', 'dist', 'build', 'coverage'];

  constructor(projectRoot: string, tsConfigPath: string) {
    this.projectRoot = projectRoot;
    this.tsConfigPath = tsConfigPath;

    // Parse tsconfig.json
    const configFile = ts.readConfigFile(tsConfigPath, ts.sys.readFile);
    const compilerOptions = ts.parseJsonConfigFileContent(
      configFile.config,
      ts.sys,
      path.dirname(tsConfigPath)
    ).options;

    // Create program
    let rootNames: string[] = [];
    if (Array.isArray(compilerOptions.rootNames)) {
      rootNames = compilerOptions.rootNames as string[];
    } else if (typeof compilerOptions.rootNames === 'string') {
      rootNames = [compilerOptions.rootNames];
    } else {
      rootNames = [projectRoot];
    }
    this.program = ts.createProgram({
      rootNames,
      options: compilerOptions,
    });

    this.checker = this.program.getTypeChecker();
  }

  public analyze(): ProjectDependencies {
    // Get all TypeScript files in the project
    const files = this.getAllTypeScriptFiles(this.projectRoot);

    // Analyze each file
    files.forEach(filePath => {
      this.analyzeFile(filePath);
    });

    return this.dependencies;
  }

  private getAllTypeScriptFiles(dir: string): string[] {
    let results: string[] = [];
    const list = fs.readdirSync(dir);

    list.forEach((file: string) => {
      const fullPath = path.join(dir, file);
      const stat = fs.statSync(fullPath);

      if (this.excludedDirs.some((excluded: string) => fullPath.includes(excluded))) {
        return;
      }

      if (stat && stat.isDirectory()) {
        results = results.concat(this.getAllTypeScriptFiles(fullPath));
      } else if (this.fileExtensions.includes(path.extname(fullPath).toLowerCase())) {
        results.push(fullPath);
      }
    });

    return results;
  }

  private analyzeFile(filePath: string): void {
    const sourceFile = this.program.getSourceFile(filePath);
    if (!sourceFile) return;

    const fileDependencies: FileDependencies = {
      filePath: path.relative(this.projectRoot, filePath),
      imports: [],
      exports: [],
      typeReferences: []
    };

    // Find all import declarations
    ts.forEachChild(sourceFile, (node: tsTypes.Node) => {
      if (ts.isImportDeclaration(node)) {
        const moduleSpecifier = node.moduleSpecifier as tsTypes.StringLiteral;
        if (moduleSpecifier && moduleSpecifier.text) {
          fileDependencies.imports.push(moduleSpecifier.text);
        }
      } else if (ts.isExportDeclaration(node)) {
        const moduleSpecifier = node.moduleSpecifier as tsTypes.StringLiteral;
        if (moduleSpecifier && moduleSpecifier.text) {
          fileDependencies.exports.push(moduleSpecifier.text);
        }
      }
    });

    // Get type references
    const typeReferences = this.getTypeReferences(sourceFile);
    fileDependencies.typeReferences = Array.from(new Set(typeReferences));

    this.dependencies[filePath] = fileDependencies;
  }

  private getTypeReferences(sourceFile: tsTypes.SourceFile): string[] {
    const typeReferences = new Set<string>();

    const visit = (node: tsTypes.Node) => {
      // Get type of the node
      const type = this.checker.getTypeAtLocation(node);

      // Get symbol and its declarations
      const symbol = type.getSymbol() || (type as any).aliasSymbol;
      if (symbol) {
        const declarations = symbol.getDeclarations() || [];
        declarations.forEach((decl: tsTypes.Declaration) => {
          if (decl.getSourceFile()) {
            typeReferences.add(decl.getSourceFile().fileName);
          }
        });
      }

      // Get type arguments
      if ('typeArguments' in type && Array.isArray((type as any).typeArguments)) {
        (type as any).typeArguments.forEach((typeArg: tsTypes.Type) => {
          const argSymbol = typeArg.getSymbol() || (typeArg as any).aliasSymbol;
          if (argSymbol) {
            const declarations = argSymbol.getDeclarations() || [];
            declarations.forEach((decl: tsTypes.Declaration) => {
              if (decl.getSourceFile()) {
                typeReferences.add(decl.getSourceFile().fileName);
              }
            });
          }
        });
      }

      // Visit child nodes
      ts.forEachChild(node, visit);
    };

    visit(sourceFile);

    return Array.from(typeReferences);
  }

  public generateDependencyGraph(): string {
    const nodes: string[] = [];
    const edges: string[] = [];

    // Add nodes
    Object.entries(this.dependencies).forEach(([filePath, deps]) => {
      const relativePath = path.relative(this.projectRoot, filePath);
      nodes.push(`  "${relativePath}" [label="${relativePath}"];`);

      // Add edges for imports
      deps.imports.forEach(imp => {
        const target = this.resolveImportPath(filePath, imp);
        if (target) {
          edges.push(`  "${relativePath}" -> "${target}" [color="#0074D9"];`);
        }
      });

      // Add edges for type references
      deps.typeReferences.forEach(ref => {
        const relativeRef = path.relative(this.projectRoot, ref);
        if (relativeRef !== relativePath) {
          edges.push(`  "${relativePath}" -> "${relativeRef}" [color="#FF4136", style="dashed"];`);
        }
      });
    });

    // Generate DOT format
    return `digraph G {
  rankdir=LR;
  node [shape=box, style=rounded];
  
${nodes.join('\n')}
  
${edges.join('\n')}
}`;
  }

  private resolveImportPath(sourcePath: string, importPath: string): string | null {
    // Skip node modules and built-ins
    if (!importPath.startsWith('.') && !importPath.startsWith('@/') && !importPath.startsWith('~/')) {
      return null;
    }
    try {
      // Handle relative imports
      if (importPath.startsWith('.')) {
        const dir = path.dirname(sourcePath);
        const fullPath = path.resolve(dir, importPath);
        // Try with .ts extension
        if (fs.existsSync(`${fullPath}.ts`)) {
          return path.relative(this.projectRoot, `${fullPath}.ts`);
        }
        // Try with /index.ts
        if (fs.existsSync(path.join(fullPath, 'index.ts'))) {
          return path.relative(this.projectRoot, path.join(fullPath, 'index.ts'));
        }
        // Try with .tsx extension
        if (fs.existsSync(`${fullPath}.tsx`)) {
          return path.relative(this.projectRoot, `${fullPath}.tsx`);
        }
      }
      // Handle node_modules imports (simplified)
      if (!importPath.startsWith('.')) {
        return `node_modules/${importPath.split('/')[0]}`;
      }
      return null;
    } catch (error) {
      console.warn(`Failed to resolve import path: ${importPath} in ${sourcePath}`, error);
      return null;
    }
  }
}

// Main execution
const projectRoot = path.join(__dirname, '..');
const tsConfigPath = path.join(projectRoot, 'tsconfig.json');

console.log('Starting dependency analysis...');
console.log(`Project root: ${projectRoot} `);
console.log(`Using tsconfig: ${tsConfigPath} `);

try {
  const analyzer = new DependencyAnalyzer(projectRoot, tsConfigPath);
  const dependencies = analyzer.analyze();

  // Ensure output directory exists
  const outputDir = path.join(projectRoot, 'docs', 'analysis');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Save dependencies as JSON
  const jsonOutput = path.join(outputDir, 'dependencies.json');
  fs.writeFileSync(jsonOutput, JSON.stringify(dependencies, null, 2));

  console.log('Dependency analysis complete!');
  console.log(`Results saved to: ${jsonOutput} `);

  // Generate simplified report
  const reportPath = path.join(outputDir, 'dependency-report.md');
  generateMarkdownReport(dependencies, reportPath);
  console.log(`Report generated: ${reportPath} `);

} catch (error) {
  console.error('Error during dependency analysis:', error);
  process.exit(1);
}

function generateMarkdownReport(dependencies: any, outputPath: string): void {
  let report = '# Vidyadhara Dependency Analysis Report\n\n';
  report += `Generated on: ${new Date().toISOString()} \n\n`;
  report += '## File Dependencies\n\n';

  // Sort files by number of dependencies
  const sortedFiles = Object.entries(dependencies)
    .sort((a: any, b: any) => b[1].imports.length - a[1].imports.length);

  // Summary statistics
  let totalImports = 0;
  const importCounts: Record<string, number> = {};

  sortedFiles.forEach(([file, data]: [string, any]) => {
    totalImports += data.imports.length;
    data.imports.forEach((imp: string) => {
      importCounts[imp] = (importCounts[imp] || 0) + 1;
    });
  });

  report += `- Total files analyzed: ${sortedFiles.length} \n`;
  report += `- Total imports: ${totalImports} \n\n`;

  // Most imported modules
  const sortedImports = Object.entries(importCounts)
    .sort((a: any, b: any) => b[1] - a[1])
    .slice(0, 10);

  report += '## Most Imported Modules\n\n';
  report += '| Module | Import Count |\n|--------|-------------:|\n';
  sortedImports.forEach(([module, count]) => {
    report += `| ${module} | ${count} |\n`;
  });
  report += '\n';

  // Files with most dependencies
  report += '## Files with Most Dependencies\n\n';
  report += '| File | Dependencies |\n|------|-------------:|\n';
  sortedFiles.slice(0, 20).forEach(([file, data]: [string, any]) => {
    const relPath = path.relative(projectRoot, file);
    report += `| ${relPath} | ${data.imports.length} |\n`;
  });
  report += '\n';

  // Detailed dependency list
  report += '## Detailed Dependencies\n\n';
  sortedFiles.forEach(([file, data]: [string, any]) => {
    const relPath = path.relative(projectRoot, file);
    report += `### ${relPath} \n\n`;

    if (data.imports.length > 0) {
      report += '**Imports:**\n';
      data.imports.forEach((imp: string) => {
        report += `- ${imp} \n`;
      });
      report += '\n';
    }

    if (data.typeReferences.length > 0) {
      report += '**Type References:**\n';
      (Array.from(new Set(data.typeReferences)) as string[]).forEach((ref: string) => {
        report += `- ${ref}\n`;
      });
      report += '\n';
    }
  });

  fs.writeFileSync(outputPath, report);
}
