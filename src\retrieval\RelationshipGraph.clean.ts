import * as vscode from 'vscode';
import { CodeSymbol, SymbolRelationship } from './symbolAnalyzer';

interface GraphNode {
  id: string;
  symbol: CodeSymbol;
  type: 'class' | 'function' | 'interface' | 'variable' | 'module';
  metadata: {
    complexity: number;
    usageCount: number;
    lastModified: Date;
    isPublic: boolean;
  };
}

interface GraphEdge {
  id: string;
  from: string;
  to: string;
  relationship: SymbolRelationship['type'];
  weight: number;
  metadata: {
    strength: number;
    frequency: number;
    isOptional: boolean;
  };
}

interface GraphMetrics {
  totalNodes: number;
  totalEdges: number;
  density: number;
  averageDegree: number;
  clusteringCoefficient: number;
  centralityScores: Map<string, number>;
}

export class RelationshipGraph {
  private nodes: Map<string, GraphNode> = new Map();
  private edges: Map<string, GraphEdge> = new Map();
  private adjacencyList: Map<string, Set<string>> = new Map();
  private reverseAdjacencyList: Map<string, Set<string>> = new Map();
  private metrics: GraphMetrics = {
    totalNodes: 0,
    totalEdges: 0,
    density: 0,
    averageDegree: 0,
    clusteringCoefficient: 0,
    centralityScores: new Map()
  };

  constructor() {
    this.initializeGraph();
  }

  private initializeGraph(): void {
    this.nodes = new Map();
    this.edges = new Map();
    this.adjacencyList = new Map();
    this.reverseAdjacencyList = new Map();
  }

  public findStronglyConnectedComponents(): string[][] {
    const components: string[][] = [];
    const visited = new Set<string>();
    const stack: string[] = [];

    // First pass: fill the stack
    for (const nodeId of this.nodes.keys()) {
      if (!visited.has(nodeId)) {
        this.dfsForStack(nodeId, visited, stack);
      }
    }

    // Create transpose graph
    const transpose = this.createTransposeGraph();
    visited.clear();

    // Second pass: process nodes in reverse order of finishing times
    while (stack.length > 0) {
      const nodeId = stack.pop()!;
      if (nodeId && !visited.has(nodeId)) {
        const component: string[] = [];
        this.dfsOnTranspose(nodeId, visited, component, transpose);
        if (component.length > 1) {
          components.push(component);
        }
      }
    }

    return components;
  }

  private dfsForStack(nodeId: string, visited: Set<string>, stack: string[]): void {
    if (!nodeId || visited.has(nodeId)) {
      return;
    }
    
    visited.add(nodeId);
    const neighbors = this.adjacencyList.get(nodeId) || new Set<string>();
    
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        this.dfsForStack(neighbor, visited, stack);
      }
    }
    
    stack.push(nodeId);
  }

  private createTransposeGraph(): Map<string, Set<string>> {
    const transpose = new Map<string, Set<string>>();
    
    // Initialize transpose with all nodes
    for (const nodeId of this.nodes.keys()) {
      transpose.set(nodeId, new Set<string>());
    }
    
    // Add reversed edges
    for (const [from, neighbors] of this.adjacencyList.entries()) {
      if (!neighbors) continue;
      
      for (const to of neighbors) {
        const toEdges = transpose.get(to) || new Set<string>();
        toEdges.add(from);
        transpose.set(to, toEdges);
      }
    }
    
    return transpose;
  }

  private dfsOnTranspose(
    nodeId: string,
    visited: Set<string>,
    component: string[],
    transposeGraph: Map<string, Set<string>>
  ): void {
    if (!nodeId || visited.has(nodeId)) {
      return;
    }
    
    visited.add(nodeId);
    component.push(nodeId);
    
    const neighbors = transposeGraph.get(nodeId) || new Set<string>();
    
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        this.dfsOnTranspose(neighbor, visited, component, transposeGraph);
      }
    }
  }

  private generateNodeId(symbol: CodeSymbol): string {
    if (!symbol || !symbol.filePath || !symbol.name || typeof symbol.line !== 'number') {
      throw new Error('Invalid symbol provided to generateNodeId');
    }
    return `${symbol.filePath}:${symbol.name}:${symbol.line}`;
  }
}
