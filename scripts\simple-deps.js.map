{"version": 3, "file": "simple-deps.js", "sourceRoot": "", "sources": ["simple-deps.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+CAAiC;AASjC,SAAS,mBAAmB,CAAC,GAAW,EAAE,WAAqB,EAAE;IAC/D,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAElC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACtC,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACvB,mDAAmD;YACnD,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzE,OAAO;YACT,CAAC;YACD,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACzD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,WAAW,CAAC,QAAgB;IACnC,MAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACvD,MAAM,UAAU,GAAG,EAAE,CAAC,gBAAgB,CACpC,QAAQ,EACR,WAAW,EACX,EAAE,CAAC,YAAY,CAAC,MAAM,EACtB,IAAI,CACL,CAAC;IAEF,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,OAAO,GAAa,EAAE,CAAC;IAE7B,SAAS,KAAK,CAAC,IAAa;QAC1B,iBAAiB;QACjB,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,eAAe,GAAG,IAAI,CAAC,eAAmC,CAAC;YACjE,IAAI,eAAe,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;gBAC5C,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,eAAe,GAAG,IAAI,CAAC,eAAmC,CAAC;YACjE,IAAI,eAAe,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;gBAC5C,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,CAAC;IAClB,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,qBAAqB,CAAC,WAAmB;IAChD,MAAM,aAAa,GAAkB,EAAE,CAAC;IACxC,MAAM,OAAO,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IAEjD,OAAO,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,MAAM,iCAAiC,CAAC,CAAC;IAEtE,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACxG,IAAI,CAAC;YACH,aAAa,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACpC,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAS,oBAAoB,CAAC,aAA4B,EAAE,UAAkB;IAC5E,MAAM,MAAM,GAAQ;QAClB,OAAO,EAAE;YACP,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM;YAC7C,YAAY,EAAE,CAAC;YACf,aAAa,EAAE,IAAI,GAAG,EAAU;SACjC;QACD,KAAK,EAAE,EAAE;KACV,CAAC;IAEF,uBAAuB;IACvB,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;QACvD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;YACtB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;SACjC,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,OAAO,CAAC,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;IACrE,OAAO,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,gCAAgC;IAErE,iCAAiC;IACjC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC3C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAC9B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,cAAc;IACd,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,+BAA+B,UAAU,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,iBAAiB;AACjB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,wBAAwB,CAAC,CAAC;AAE5F,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;AAC/C,MAAM,aAAa,GAAG,qBAAqB,CAAC,WAAW,CAAC,CAAC;AACzD,oBAAoB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;AAEhD,qCAAqC;AACrC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;AACjF,IAAI,QAAQ,GAAG,mCAAmC,CAAC;AACnD,QAAQ,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;AAC5D,QAAQ,IAAI,yBAAyB,CAAC;AACtC,QAAQ,IAAI,6BAA6B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,IAAI,CAAC;AAE/E,gBAAgB;AAChB,IAAI,YAAY,GAAG,CAAC,CAAC;AACrB,MAAM,YAAY,GAA2B,EAAE,CAAC;AAEhD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;IAC1C,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACpC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACzB,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,IAAI,oBAAoB,YAAY,IAAI,CAAC;AACjD,QAAQ,IAAI,qBAAqB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,MAAM,CAAC;AAExE,uBAAuB;AACvB,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;KAC/C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAEhB,QAAQ,IAAI,8BAA8B,CAAC;AAC3C,QAAQ,IAAI,wDAAwD,CAAC;AACrE,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE;IACxC,QAAQ,IAAI,KAAK,MAAM,MAAM,KAAK,MAAM,CAAC;AAC3C,CAAC,CAAC,CAAC;AAEH,+BAA+B;AAC/B,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;KAC9C,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACtB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC;IACtC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;CAC3B,CAAC,CAAC;KACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;KACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAEhB,QAAQ,IAAI,uCAAuC,CAAC;AACpD,QAAQ,IAAI,oDAAoD,CAAC;AACjE,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;IACtC,QAAQ,IAAI,KAAK,IAAI,MAAM,KAAK,MAAM,CAAC;AACzC,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACxC,OAAO,CAAC,GAAG,CAAC,qBAAqB,WAAW,EAAE,CAAC,CAAC"}