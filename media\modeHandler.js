class ModeHandler {
    constructor() {
        console.log('[<PERSON><PERSON><PERSON><PERSON>] Initializing ModeHandler');
        this.currentMode = null;
        this.initialized = false;
        this.debugElement = document.getElementById('mode-debug');

        // Store a reference to the vscode API
        try {
            this.vscode = typeof acquireVsCodeApi === 'function' ? acquireVsCodeApi() : null;
            if (!this.vscode) {
                console.warn('[ModeHandler] acquireVsCodeApi is not available');
                this.logToDebug('VS Code API not available');
            } else {
                console.log('[ModeHandler] vscode API acquired successfully');
                this.logToDebug('VS Code API acquired');
            }
        } catch (error) {
            console.error('[ModeHandler] Error acquiring vscode API:', error);
            this.logToDebug('Error acquiring VS Code API: ' + (error.message || String(error)));
            this.vscode = null;
        }

        // Initialize event listeners after a short delay to ensure DOM is ready
        setTimeout(() => this.initializeEventListeners(), 100);

        // Request current mode from extension
        this.requestCurrentMode();
    }

    logToDebug(message) {
        if (this.debugElement) {
            this.debugElement.textContent += message + '\n';
        }
        console.log('[ModeHandler] ' + message);
    }

    requestCurrentMode() {
        if (this.vscode) {
            this.logToDebug('Requesting current mode from extension');
            this.vscode.postMessage({ command: 'getCurrentMode' });
        } else {
            this.logToDebug('Cannot request mode: vscode API not available');
        }
    }

    initializeEventListeners() {
        console.log('[ModeHandler] Initializing event listeners');

        // Debug mode toggle (Ctrl+Shift+D)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                const debugElement = document.getElementById('mode-debug');
                if (debugElement) {
                    debugElement.style.display = debugElement.style.display === 'none' ? 'block' : 'none';
                }
            }
        });

        // Use a single event listener on the document for better reliability
        document.addEventListener('click', (e) => {
            // Handle mode buttons
            const button = e.target.closest('.mode-button');
            if (button) {
                const modeId = button.dataset.mode;
                if (!modeId) {
                    console.error('[ModeHandler] No mode ID found on button:', button);
                    return;
                }

                this.logToDebug(`Mode button clicked: ${modeId}`);
                this.handleModeChange(modeId);
                e.preventDefault();
                e.stopPropagation();
                return;
            }

            // Handle mode select dropdown if needed
            const select = e.target.closest('#mode-select');
            if (select) {
                const modeId = select.value;
                if (modeId) {
                    this.logToDebug(`Mode select changed: ${modeId}`);
                    this.handleModeChange(modeId);
                }
            }
        });

        // Also set up change listener for mode select for better compatibility
        const modeSelect = document.getElementById('mode-select');
        if (modeSelect) {
            modeSelect.addEventListener('change', (e) => {
                const modeId = e.target.value;
                if (modeId) {
                    this.logToDebug(`Mode select changed (direct): ${modeId}`);
                    this.handleModeChange(modeId);
                }
            });
        } else {
            this.logToDebug('Mode select dropdown not found during initialization');
        }

        // Listen for mode changes from the extension
        window.addEventListener('message', (event) => {
            const message = event.data;

            // Handle different message types
            if (message.type === 'updateActiveMode' || message.command === 'updateActiveMode') {
                this.handleModeUpdateMessage(message);
            } else if (message.type === 'initialized' || message.command === 'initialized') {
                this.logToDebug('Webview initialized, setting initial mode');
                this.updateActiveMode(message.modeId);
            }
        });

        // Request current mode on initialization
        this.requestCurrentMode();
    }

    handleModeChange(modeId) {
        if (!modeId) {
            console.error('[ModeHandler] Invalid mode ID:', modeId);
            return;
        }

        console.log('[ModeHandler] Handling mode change to:', modeId);

        // Update UI immediately for better responsiveness
        this.updateActiveMode(modeId);

        // Send message to extension
        if (!this.vscode) {
            console.error('[ModeHandler] Cannot send message: vscode API not available');
            return;
        }

        const message = { command: 'changeMode', modeId: modeId };

        console.log('[ModeHandler] Sending message to extension:', message);

        try {
            this.vscode.postMessage(message);
            console.log('[ModeHandler] Message sent successfully');
        } catch (error) {
            console.error('[ModeHandler] Failed to send message:', error);
        }
    }

    handleModeUpdateMessage(message) {
        try {
            // Extract mode ID from the message (support both modeId and mode for backward compatibility)
            const modeId = message.modeId || message.mode;

            if (!modeId) {
                throw new Error('No mode ID provided in update message');
            }

            this.logToDebug(`[ModeHandler] Received mode update: ${modeId} (success: ${message.success !== false})`);

            if (message.success === false) {
                // Handle error case
                const errorMsg = message.error || 'Unknown error occurred';
                this.logToDebug(`[ModeHandler] Mode update failed: ${errorMsg}`);
                console.error(`[ModeHandler] Failed to update mode: ${errorMsg}`);
                // TODO: Show error to user if needed
                return;
            }

            // Update the active mode
            this.updateActiveMode(modeId);

        } catch (error) {
            console.error('[ModeHandler] Error handling mode update message:', error);
            this.logToDebug(`Error: ${error.message}`);
        }
    }

    /**
     * Updates the UI to reflect the active mode
     * @param {string} modeId - The ID of the mode to activate
     */
    updateActiveMode(modeId) {
        try {
            // Validate input
            if (!modeId || typeof modeId !== 'string') {
                const errorMsg = `[ModeHandler] Invalid mode ID: ${modeId}`;
                console.error(errorMsg);
                this.logToDebug(errorMsg);
                return false;
            }

            // Normalize mode ID
            modeId = modeId.trim().toLowerCase();

            this.logToDebug(`[ModeHandler] Updating active mode to: ${modeId}`);

            // Store current mode
            this.currentMode = modeId;

            // Update mode buttons
            const buttons = document.querySelectorAll('.mode-button');
            let foundActive = false;
            let buttonsUpdated = 0;

            buttons.forEach(button => {
                try {
                    if (!button || !button.dataset) {
                        this.logToDebug('Skipping invalid button element');
                        return;
                    }

                    const buttonMode = button.dataset.mode;
                    if (!buttonMode) {
                        this.logToDebug('Button missing data-mode attribute:', button);
                        return;
                    }

                    const isActive = buttonMode === modeId;

                    // Update active state
                    if (isActive) {
                        button.classList.add('active');
                        foundActive = true;
                        this.logToDebug(`Activated button for mode: ${modeId}`);
                    } else {
                        button.classList.remove('active');
                    }

                    // Update aria-pressed for accessibility
                    button.setAttribute('aria-pressed', isActive.toString());
                    buttonsUpdated++;

                } catch (buttonError) {
                    console.error('[ModeHandler] Error updating button:', buttonError);
                    this.logToDebug(`Button update error: ${buttonError.message}`);
                }
            });

            this.logToDebug(`Updated ${buttonsUpdated} mode buttons`);

            if (!foundActive) {
                const warningMsg = `No active button found for mode: ${modeId}`;
                console.warn(`[ModeHandler] ${warningMsg}`);
                this.logToDebug(warningMsg);

                // Try to find and activate the button by ID as a fallback
                const buttonById = document.getElementById(`mode-button-${modeId}`);
                if (buttonById) {
                    buttonById.classList.add('active');
                    this.logToDebug(`Activated button by ID fallback for mode: ${modeId}`);
                }
            }

            // Update mode select dropdown if it exists
            try {
                const modeSelect = document.getElementById('mode-select');
                if (modeSelect) {
                    modeSelect.value = modeId;
                    this.logToDebug(`Updated mode select to: ${modeId}`);
                }
            } catch (selectError) {
                console.error('[ModeHandler] Error updating mode select:', selectError);
            }

            // Update mode indicator if it exists
            try {
                const modeIndicator = document.getElementById('mode-indicator');
                if (modeIndicator) {
                    const modeName = modeId
                        .split('-')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                        .join(' ');
                    modeIndicator.textContent = modeName;
                    this.logToDebug(`Updated mode indicator to: ${modeName}`);
                }
                // Update current-mode-label if it exists
                const modeLabel = document.getElementById('current-mode-label');
                if (modeLabel) {
                    let modeName = '';
                    switch (modeId) {
                        case 'chat': modeName = 'Chat'; break;
                        case 'agent': modeName = 'Agent'; break;
                        case 'agent-auto': modeName = 'Agent Auto'; break;
                        default:
                            modeName = modeId.charAt(0).toUpperCase() + modeId.slice(1).replace('-', ' ');
                    }
                    modeLabel.textContent = modeName;
                    this.logToDebug(`Updated current-mode-label to: ${modeName}`);
                }
            } catch (indicatorError) {
                console.error('[ModeHandler] Error updating mode indicator:', indicatorError);
            }

            // Store mode in webview state
            try {
                if (typeof vscode !== 'undefined' && vscode && typeof vscode.setState === 'function') {
                    vscode.setState({ mode: modeId });
                    this.logToDebug(`Stored mode in webview state: ${modeId}`);
                }
            } catch (stateError) {
                console.error('[ModeHandler] Error storing mode in webview state:', stateError);
            }

            return true;

        } catch (error) {
            const errorMsg = `[ModeHandler] Error in updateActiveMode: ${error.message || String(error)}`;
            console.error(errorMsg, error);
            this.logToDebug(errorMsg);
            return false;
        }
    }
}
