export enum ChatMode {
    Chat = 'chat',
    Agent = 'agent',
    AgentAuto = 'agent-auto'
}

export interface ChatModeConfig {
    id: ChatMode;
    name: string;
    icon: string;
    description: string;
}

export const CHAT_MODE_CONFIGS: Record<ChatMode, ChatModeConfig> = {
    [ChatMode.Chat]: {
        id: ChatMode.Chat,
        name: 'Chat Mode',
        icon: '💬',
        description: 'Standard chat mode for general conversations'
    },
    [ChatMode.Agent]: {
        id: ChatMode.Agent,
        name: 'Agent Mode',
        icon: '🤖',
        description: 'AI agent mode for code-related tasks'
    },
    [ChatMode.AgentAuto]: {
        id: ChatMode.AgentAuto,
        name: 'Agent Auto Mode',
        icon: '⚡',
        description: 'Automated agent mode for continuous assistance'
    }
};
