import * as vscode from 'vscode';
import { EditOperation, StringReplaceEditor } from './stringReplaceEditor';
import { EditPreview, BatchEditPreview } from './editPreview';
import { EditHistory } from './editHistory';

export interface BatchEditOperation {
  id: string;
  operations: EditOperation[];
  description: string;
  timestamp: Date;
  status: 'pending' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
  progress: {
    total: number;
    completed: number;
    failed: number;
  };
}

export interface BatchEditResult {
  batchId: string;
  success: boolean;
  completedOperations: EditOperation[];
  failedOperations: { operation: EditOperation; error: string }[];
  totalTime: number;
}

export class BatchEditor {
  private stringEditor: StringReplaceEditor;
  private editPreview: EditPreview;
  private editHistory: EditHistory;
  private activeBatches: Map<string, BatchEditOperation> = new Map();
  private readonly maxConcurrentBatches = 3;

  constructor(stringEditor: StringReplaceEditor, editPreview: EditPreview, editHistory: EditHistory) {
    this.stringEditor = stringEditor;
    this.editPreview = editPreview;
    this.editHistory = editHistory;
  }

  /**
   * Queue multiple edit operations for batch execution
   */
  async queueBatchEdit(
    operations: EditOperation[],
    description: string = 'Batch Edit Operation'
  ): Promise<string> {
    const batchId = this.generateBatchId();
    
    const batchOperation: BatchEditOperation = {
      id: batchId,
      operations,
      description,
      timestamp: new Date(),
      status: 'pending',
      progress: {
        total: operations.length,
        completed: 0,
        failed: 0
      }
    };

    this.activeBatches.set(batchId, batchOperation);
    
    vscode.window.showInformationMessage(
      `Batch edit queued: ${operations.length} operations (ID: ${batchId})`
    );

    return batchId;
  }

  /**
   * Execute batch edit with atomic behavior (all or nothing)
   */
  async executeBatchAtomic(batchId: string): Promise<BatchEditResult> {
    const batch = this.activeBatches.get(batchId);
    if (!batch) {
      throw new Error(`Batch operation not found: ${batchId}`);
    }

    const startTime = Date.now();
    batch.status = 'in-progress';

    try {
      // First, validate all operations
      const validationResults = await this.validateAllOperations(batch.operations);
      const invalidOperations = validationResults.filter(r => !r.isValid);
      
      if (invalidOperations.length > 0) {
        batch.status = 'failed';
        throw new Error(`Validation failed for ${invalidOperations.length} operations`);
      }

      // Create preview for confirmation
      const preview = await this.editPreview.createBatchEditPreview(batch.operations);
      const confirmed = await this.editPreview.showBatchEditConfirmation(preview);
      
      if (!confirmed) {
        batch.status = 'cancelled';
        return {
          batchId,
          success: false,
          completedOperations: [],
          failedOperations: [],
          totalTime: Date.now() - startTime
        };
      }

      // Create backups before applying changes
      const backups = await this.createBackups(batch.operations);

      try {
        // Apply all operations
        const completedOperations: EditOperation[] = [];
        
        for (const operation of batch.operations) {
          await this.stringEditor.applyEdit(operation);
          completedOperations.push(operation);
          
          // Record in history
          const beforeContent = backups.get(operation.filePath) || '';
          const afterContent = await this.getFileContent(operation.filePath);
          await this.editHistory.recordEdit(
            operation,
            `Batch edit: ${batch.description}`,
            beforeContent,
            afterContent
          );

          batch.progress.completed++;
          this.updateProgress(batchId, batch.progress);
        }

        batch.status = 'completed';
        
        return {
          batchId,
          success: true,
          completedOperations,
          failedOperations: [],
          totalTime: Date.now() - startTime
        };

      } catch (error) {
        // Rollback all changes on failure
        await this.rollbackBatch(backups);
        batch.status = 'failed';
        throw error;
      }

    } catch (error) {
      batch.status = 'failed';
      return {
        batchId,
        success: false,
        completedOperations: [],
        failedOperations: batch.operations.map(op => ({
          operation: op,
          error: error instanceof Error ? error.message : String(error)
        })),
        totalTime: Date.now() - startTime
      };
    } finally {
      this.activeBatches.delete(batchId);
    }
  }

  /**
   * Execute batch edit with partial success allowed
   */
  async executeBatchPartial(batchId: string): Promise<BatchEditResult> {
    const batch = this.activeBatches.get(batchId);
    if (!batch) {
      throw new Error(`Batch operation not found: ${batchId}`);
    }

    const startTime = Date.now();
    batch.status = 'in-progress';

    const completedOperations: EditOperation[] = [];
    const failedOperations: { operation: EditOperation; error: string }[] = [];

    try {
      // Create preview for confirmation
      const preview = await this.editPreview.createBatchEditPreview(batch.operations);
      const confirmed = await this.editPreview.showBatchEditConfirmation(preview);
      
      if (!confirmed) {
        batch.status = 'cancelled';
        return {
          batchId,
          success: false,
          completedOperations: [],
          failedOperations: [],
          totalTime: Date.now() - startTime
        };
      }

      // Apply operations one by one, continuing on failure
      for (const operation of batch.operations) {
        try {
          // Get before content for history
          const beforeContent = await this.getFileContent(operation.filePath);
          
          // Apply the operation
          await this.stringEditor.applyEdit(operation);
          
          // Get after content for history
          const afterContent = await this.getFileContent(operation.filePath);
          
          // Record in history
          await this.editHistory.recordEdit(
            operation,
            `Batch edit: ${batch.description}`,
            beforeContent,
            afterContent
          );

          completedOperations.push(operation);
          batch.progress.completed++;

        } catch (error) {
          failedOperations.push({
            operation,
            error: error instanceof Error ? error.message : String(error)
          });
          batch.progress.failed++;
        }

        this.updateProgress(batchId, batch.progress);
      }

      batch.status = completedOperations.length > 0 ? 'completed' : 'failed';

      return {
        batchId,
        success: completedOperations.length > 0,
        completedOperations,
        failedOperations,
        totalTime: Date.now() - startTime
      };

    } catch (error) {
      batch.status = 'failed';
      return {
        batchId,
        success: false,
        completedOperations,
        failedOperations: batch.operations.map(op => ({
          operation: op,
          error: error instanceof Error ? error.message : String(error)
        })),
        totalTime: Date.now() - startTime
      };
    } finally {
      this.activeBatches.delete(batchId);
    }
  }

  /**
   * Cancel a batch operation
   */
  async cancelBatch(batchId: string): Promise<boolean> {
    const batch = this.activeBatches.get(batchId);
    if (!batch) {
      return false;
    }

    if (batch.status === 'in-progress') {
      batch.status = 'cancelled';
      vscode.window.showInformationMessage(`Batch operation cancelled: ${batchId}`);
    }

    this.activeBatches.delete(batchId);
    return true;
  }

  /**
   * Get status of a batch operation
   */
  getBatchStatus(batchId: string): BatchEditOperation | undefined {
    return this.activeBatches.get(batchId);
  }

  /**
   * Get all active batch operations
   */
  getActiveBatches(): BatchEditOperation[] {
    return Array.from(this.activeBatches.values());
  }

  /**
   * Validate all operations in a batch
   */
  private async validateAllOperations(operations: EditOperation[]): Promise<Array<{ isValid: boolean; errors: string[] }>> {
    const results = [];
    
    for (const operation of operations) {
      const result = await this.stringEditor.validateEdit(operation);
      results.push({
        isValid: result.isValid,
        errors: result.errors
      });
    }

    return results;
  }

  /**
   * Create backups of all files that will be modified
   */
  private async createBackups(operations: EditOperation[]): Promise<Map<string, string>> {
    const backups = new Map<string, string>();
    const uniqueFiles = new Set(operations.map(op => op.filePath));

    for (const filePath of uniqueFiles) {
      try {
        const content = await this.getFileContent(filePath);
        backups.set(filePath, content);
      } catch (error) {
        console.warn(`Failed to create backup for ${filePath}:`, error);
      }
    }

    return backups;
  }

  /**
   * Rollback batch changes using backups
   */
  private async rollbackBatch(backups: Map<string, string>): Promise<void> {
    for (const [filePath, content] of backups) {
      try {
        const document = await vscode.workspace.openTextDocument(filePath);
        const edit = new vscode.WorkspaceEdit();
        const fullRange = new vscode.Range(
          document.positionAt(0),
          document.positionAt(document.getText().length)
        );
        edit.replace(document.uri, fullRange, content);
        await vscode.workspace.applyEdit(edit);
      } catch (error) {
        console.error(`Failed to rollback ${filePath}:`, error);
      }
    }
  }

  /**
   * Get file content
   */
  private async getFileContent(filePath: string): Promise<string> {
    try {
      const document = await vscode.workspace.openTextDocument(filePath);
      return document.getText();
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update progress for a batch operation
   */
  private updateProgress(batchId: string, progress: BatchEditOperation['progress']): void {
    const percentage = Math.round((progress.completed / progress.total) * 100);
    
    // Show progress notification
    vscode.window.withProgress(
      {
        location: vscode.ProgressLocation.Notification,
        title: `Batch Edit ${batchId}`,
        cancellable: false
      },
      async (progressReporter) => {
        progressReporter.report({
          increment: percentage,
          message: `${progress.completed}/${progress.total} operations completed`
        });
      }
    );
  }

  /**
   * Generate unique batch ID
   */
  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }
}
