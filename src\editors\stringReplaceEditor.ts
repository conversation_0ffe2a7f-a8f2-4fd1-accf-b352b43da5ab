import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const exists = promisify(fs.exists);

export interface EditOperation {
  filePath: string;
  startLine: number;
  endLine: number;
  oldText: string;
  newText: string;
  timestamp: Date;
}

export interface EditValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class StringReplaceEditor {
  private workspaceRoot: string;

  constructor() {
    this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
  }

  /**
   * Read file content with line numbers
   */
  async readFileWithLines(filePath: string): Promise<{ lines: string[]; content: string }> {
    try {
      const fullPath = this.resolveFilePath(filePath);
      
      if (!(await exists(fullPath))) {
        throw new Error(`File not found: ${filePath}`);
      }

      const content = await readFile(fullPath, 'utf8');
      const lines = content.split('\n');
      
      return { lines, content };
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Replace specific text between given line numbers
   */
  async replaceTextInRange(
    filePath: string,
    startLine: number,
    endLine: number,
    newText: string,
    validate: boolean = true
  ): Promise<EditOperation> {
    try {
      const { lines, content } = await this.readFileWithLines(filePath);
      
      // Validate line ranges
      if (startLine < 1 || endLine < 1) {
        throw new Error('Line numbers must be greater than 0');
      }
      
      if (startLine > lines.length || endLine > lines.length) {
        throw new Error(`Line numbers exceed file length (${lines.length} lines)`);
      }
      
      if (startLine > endLine) {
        throw new Error('Start line must be less than or equal to end line');
      }

      // Extract old text
      const oldText = lines.slice(startLine - 1, endLine).join('\n');
      
      // Create edit operation
      const editOperation: EditOperation = {
        filePath,
        startLine,
        endLine,
        oldText,
        newText,
        timestamp: new Date()
      };

      // Validate edit if requested
      if (validate) {
        const validationResult = await this.validateEdit(editOperation);
        if (!validationResult.isValid) {
          throw new Error(`Edit validation failed: ${validationResult.errors.join(', ')}`);
        }
      }

      return editOperation;
    } catch (error) {
      throw new Error(`Failed to create edit operation: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Apply edit operation to file
   */
  async applyEdit(editOperation: EditOperation): Promise<void> {
    try {
      const { lines } = await this.readFileWithLines(editOperation.filePath);
      
      // Replace the specified lines
      const newLines = [
        ...lines.slice(0, editOperation.startLine - 1),
        ...editOperation.newText.split('\n'),
        ...lines.slice(editOperation.endLine)
      ];

      const newContent = newLines.join('\n');
      const fullPath = this.resolveFilePath(editOperation.filePath);
      
      await writeFile(fullPath, newContent, 'utf8');
      
      // Notify VS Code of the change
      const uri = vscode.Uri.file(fullPath);
      const edit = new vscode.WorkspaceEdit();
      edit.replace(uri, new vscode.Range(0, 0, lines.length, 0), newContent);
      
    } catch (error) {
      throw new Error(`Failed to apply edit: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate edit before applying
   */
  async validateEdit(editOperation: EditOperation): Promise<EditValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if file exists
      const fullPath = this.resolveFilePath(editOperation.filePath);
      if (!(await exists(fullPath))) {
        errors.push(`File does not exist: ${editOperation.filePath}`);
        return { isValid: false, errors, warnings };
      }

      // Check file permissions
      try {
        await fs.promises.access(fullPath, fs.constants.W_OK);
      } catch {
        errors.push(`File is not writable: ${editOperation.filePath}`);
      }

      // Validate line ranges
      const { lines } = await this.readFileWithLines(editOperation.filePath);
      if (editOperation.startLine > lines.length || editOperation.endLine > lines.length) {
        errors.push('Line numbers exceed file length');
      }

      // Check for potential syntax issues (basic validation)
      if (editOperation.filePath.endsWith('.json')) {
        try {
          JSON.parse(editOperation.newText);
        } catch {
          warnings.push('New text may not be valid JSON');
        }
      }

      // Check for large changes
      const oldLineCount = editOperation.endLine - editOperation.startLine + 1;
      const newLineCount = editOperation.newText.split('\n').length;
      if (Math.abs(newLineCount - oldLineCount) > 100) {
        warnings.push('Large change detected - please review carefully');
      }

    } catch (error) {
      errors.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Resolve file path relative to workspace
   */
  private resolveFilePath(filePath: string): string {
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    return path.join(this.workspaceRoot, filePath);
  }

  /**
   * Get line count of file
   */
  async getLineCount(filePath: string): Promise<number> {
    const { lines } = await this.readFileWithLines(filePath);
    return lines.length;
  }

  /**
   * Get specific line from file
   */
  async getLine(filePath: string, lineNumber: number): Promise<string> {
    const { lines } = await this.readFileWithLines(filePath);
    
    if (lineNumber < 1 || lineNumber > lines.length) {
      throw new Error(`Line number ${lineNumber} is out of range (1-${lines.length})`);
    }
    
    return lines[lineNumber - 1];
  }

  /**
   * Get range of lines from file
   */
  async getLineRange(filePath: string, startLine: number, endLine: number): Promise<string[]> {
    const { lines } = await this.readFileWithLines(filePath);

    if (startLine < 1 || endLine < 1 || startLine > lines.length || endLine > lines.length) {
      throw new Error(`Line range ${startLine}-${endLine} is out of bounds (1-${lines.length})`);
    }

    if (startLine > endLine) {
      throw new Error('Start line must be less than or equal to end line');
    }

    return lines.slice(startLine - 1, endLine);
  }

  /**
   * Replace multiple consecutive lines
   */
  async replaceMultipleLines(
    filePath: string,
    startLine: number,
    endLine: number,
    newLines: string[],
    preserveIndentation: boolean = true
  ): Promise<EditOperation> {
    try {
      const { lines } = await this.readFileWithLines(filePath);

      // Validate line ranges
      if (startLine < 1 || endLine < 1 || startLine > lines.length || endLine > lines.length) {
        throw new Error(`Line range ${startLine}-${endLine} is out of bounds (1-${lines.length})`);
      }

      let processedNewLines = newLines;

      // Preserve indentation if requested
      if (preserveIndentation && lines.length > 0) {
        const referenceIndentation = this.getIndentation(lines[startLine - 1]);
        processedNewLines = newLines.map(line => {
          if (line.trim() === '') return line; // Keep empty lines as-is
          return referenceIndentation + line.replace(/^\s*/, '');
        });
      }

      const oldText = lines.slice(startLine - 1, endLine).join('\n');
      const newText = processedNewLines.join('\n');

      return {
        filePath,
        startLine,
        endLine,
        oldText,
        newText,
        timestamp: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to replace multiple lines: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Insert new lines at specific position
   */
  async insertLinesAt(
    filePath: string,
    lineNumber: number,
    newLines: string[],
    preserveIndentation: boolean = true
  ): Promise<EditOperation> {
    try {
      const { lines } = await this.readFileWithLines(filePath);

      if (lineNumber < 1 || lineNumber > lines.length + 1) {
        throw new Error(`Line number ${lineNumber} is out of bounds (1-${lines.length + 1})`);
      }

      let processedNewLines = newLines;

      // Preserve indentation if requested and there's a reference line
      if (preserveIndentation && lineNumber <= lines.length) {
        const referenceIndentation = this.getIndentation(lines[lineNumber - 1]);
        processedNewLines = newLines.map(line => {
          if (line.trim() === '') return line;
          return referenceIndentation + line.replace(/^\s*/, '');
        });
      }

      const newText = processedNewLines.join('\n');

      return {
        filePath,
        startLine: lineNumber,
        endLine: lineNumber - 1, // Insert operation
        oldText: '',
        newText,
        timestamp: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to insert lines: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete line ranges
   */
  async deleteLineRange(
    filePath: string,
    startLine: number,
    endLine: number
  ): Promise<EditOperation> {
    try {
      const { lines } = await this.readFileWithLines(filePath);

      if (startLine < 1 || endLine < 1 || startLine > lines.length || endLine > lines.length) {
        throw new Error(`Line range ${startLine}-${endLine} is out of bounds (1-${lines.length})`);
      }

      if (startLine > endLine) {
        throw new Error('Start line must be less than or equal to end line');
      }

      const oldText = lines.slice(startLine - 1, endLine).join('\n');

      return {
        filePath,
        startLine,
        endLine,
        oldText,
        newText: '',
        timestamp: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to delete line range: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get indentation from a line
   */
  private getIndentation(line: string): string {
    const match = line.match(/^(\s*)/);
    return match ? match[1] : '';
  }

  /**
   * Apply insert operation to file
   */
  async applyInsert(editOperation: EditOperation): Promise<void> {
    try {
      const { lines } = await this.readFileWithLines(editOperation.filePath);

      const newLines = [
        ...lines.slice(0, editOperation.startLine - 1),
        ...editOperation.newText.split('\n'),
        ...lines.slice(editOperation.startLine - 1)
      ];

      const newContent = newLines.join('\n');
      const fullPath = this.resolveFilePath(editOperation.filePath);

      await writeFile(fullPath, newContent, 'utf8');
    } catch (error) {
      throw new Error(`Failed to apply insert: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Insert code at specific line number with smart indentation
   */
  async insertCodeAtLine(
    filePath: string,
    lineNumber: number,
    code: string,
    matchIndentation: boolean = true
  ): Promise<EditOperation> {
    try {
      const { lines } = await this.readFileWithLines(filePath);

      if (lineNumber < 1 || lineNumber > lines.length + 1) {
        throw new Error(`Line number ${lineNumber} is out of bounds (1-${lines.length + 1})`);
      }

      let processedCode = code;

      // Match indentation to surrounding context
      if (matchIndentation && lineNumber <= lines.length) {
        const referenceIndentation = this.getSmartIndentation(lines, lineNumber);
        processedCode = this.applyIndentation(code, referenceIndentation);
      }

      return {
        filePath,
        startLine: lineNumber,
        endLine: lineNumber - 1,
        oldText: '',
        newText: processedCode,
        timestamp: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to insert code at line: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Insert code after specific pattern
   */
  async insertCodeAfterPattern(
    filePath: string,
    pattern: string | RegExp,
    code: string,
    matchIndentation: boolean = true
  ): Promise<EditOperation> {
    try {
      const { lines } = await this.readFileWithLines(filePath);

      const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
      let insertLineNumber = -1;

      // Find the pattern
      for (let i = 0; i < lines.length; i++) {
        if (regex.test(lines[i])) {
          insertLineNumber = i + 2; // Insert after the matching line
          break;
        }
      }

      if (insertLineNumber === -1) {
        throw new Error(`Pattern not found: ${pattern}`);
      }

      return await this.insertCodeAtLine(filePath, insertLineNumber, code, matchIndentation);
    } catch (error) {
      throw new Error(`Failed to insert code after pattern: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Insert code before specific pattern
   */
  async insertCodeBeforePattern(
    filePath: string,
    pattern: string | RegExp,
    code: string,
    matchIndentation: boolean = true
  ): Promise<EditOperation> {
    try {
      const { lines } = await this.readFileWithLines(filePath);

      const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
      let insertLineNumber = -1;

      // Find the pattern
      for (let i = 0; i < lines.length; i++) {
        if (regex.test(lines[i])) {
          insertLineNumber = i + 1; // Insert before the matching line
          break;
        }
      }

      if (insertLineNumber === -1) {
        throw new Error(`Pattern not found: ${pattern}`);
      }

      return await this.insertCodeAtLine(filePath, insertLineNumber, code, matchIndentation);
    } catch (error) {
      throw new Error(`Failed to insert code before pattern: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get smart indentation based on surrounding context
   */
  private getSmartIndentation(lines: string[], lineNumber: number): string {
    // Look at surrounding lines to determine appropriate indentation
    const contextLines = [
      lineNumber > 1 ? lines[lineNumber - 2] : '',
      lineNumber <= lines.length ? lines[lineNumber - 1] : '',
      lineNumber < lines.length ? lines[lineNumber] : ''
    ].filter(line => line.trim() !== '');

    if (contextLines.length === 0) {
      return '';
    }

    // Use the most common indentation from context
    const indentations = contextLines.map(line => this.getIndentation(line));
    const indentationCounts = new Map<string, number>();

    for (const indent of indentations) {
      indentationCounts.set(indent, (indentationCounts.get(indent) || 0) + 1);
    }

    let mostCommonIndent = '';
    let maxCount = 0;

    for (const [indent, count] of indentationCounts) {
      if (count > maxCount) {
        maxCount = count;
        mostCommonIndent = indent;
      }
    }

    return mostCommonIndent;
  }

  /**
   * Apply indentation to code block
   */
  private applyIndentation(code: string, baseIndentation: string): string {
    const lines = code.split('\n');
    return lines.map((line, index) => {
      if (line.trim() === '') return line; // Keep empty lines as-is

      // For the first line, apply base indentation
      // For subsequent lines, preserve relative indentation
      if (index === 0) {
        return baseIndentation + line.replace(/^\s*/, '');
      } else {
        const originalIndent = this.getIndentation(line);
        const relativeIndent = originalIndent.length > 0 ? originalIndent : '  '; // Default 2 spaces
        return baseIndentation + relativeIndent + line.replace(/^\s*/, '');
      }
    }).join('\n');
  }

  /**
   * Validate syntax for specific file types
   */
  async validateSyntax(filePath: string, code: string): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];
    const fileExtension = path.extname(filePath).toLowerCase();

    try {
      switch (fileExtension) {
        case '.json':
          try {
            JSON.parse(code);
          } catch (e) {
            errors.push(`Invalid JSON syntax: ${e instanceof Error ? e.message : String(e)}`);
          }
          break;

        case '.js':
        case '.ts':
          // Basic JavaScript/TypeScript syntax validation
          if (code.includes('function') && !code.includes('{')) {
            errors.push('Function declaration appears incomplete');
          }
          if (code.split('{').length !== code.split('}').length) {
            errors.push('Mismatched braces detected');
          }
          break;

        case '.html':
          // Basic HTML validation
          const openTags = (code.match(/<[^/][^>]*>/g) || []).length;
          const closeTags = (code.match(/<\/[^>]*>/g) || []).length;
          if (openTags !== closeTags) {
            errors.push('Mismatched HTML tags detected');
          }
          break;

        default:
          // Generic validation for other file types
          break;
      }
    } catch (error) {
      errors.push(`Syntax validation error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
