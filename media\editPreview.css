/* Edit Preview Panel Styles */

:root {
  --vscode-editor-background: var(--vscode-editor-background, #1e1e1e);
  --vscode-editor-foreground: var(--vscode-editor-foreground, #d4d4d4);
  --vscode-diffEditor-insertedTextBackground: var(--vscode-diffEditor-insertedTextBackground, rgba(155, 185, 85, 0.2));
  --vscode-diffEditor-removedTextBackground: var(--vscode-diffEditor-removedTextBackground, rgba(255, 0, 0, 0.2));
  --vscode-diffEditor-modifiedTextBackground: var(--vscode-diffEditor-modifiedTextBackground, rgba(255, 165, 0, 0.2));
  --vscode-button-background: var(--vscode-button-background, #0e639c);
  --vscode-button-foreground: var(--vscode-button-foreground, #ffffff);
  --vscode-button-hoverBackground: var(--vscode-button-hoverBackground, #1177bb);
}

body {
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  margin: 0;
  padding: 0;
  line-height: 1.4;
}

.container {
  max-width: 100%;
  margin: 0;
  padding: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.btn-primary {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn-primary:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.btn-secondary {
  background-color: var(--vscode-button-secondaryBackground, #3c3c3c);
  color: var(--vscode-button-secondaryForeground, #cccccc);
}

.btn-secondary:hover {
  background-color: var(--vscode-button-secondaryHoverBackground, #4c4c4c);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-link {
  background: none;
  color: var(--vscode-textLink-foreground);
  text-decoration: underline;
  padding: 4px 8px;
}

.btn-link:hover {
  color: var(--vscode-textLink-activeForeground);
}

/* Batch Summary */
.batch-summary {
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-widget-border);
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;
}

.batch-summary h2 {
  margin: 0 0 12px 0;
  font-size: 18px;
}

.total-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.files-changed {
  font-weight: 600;
  color: var(--vscode-editor-foreground);
}

/* Edit Preview */
.edit-preview {
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  margin-bottom: 20px;
  overflow: hidden;
}

.file-header {
  background-color: var(--vscode-editorGroupHeader-tabsBackground);
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.file-header h2,
.file-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--vscode-editor-foreground);
  flex: 1;
}

.change-stats {
  display: flex;
  gap: 12px;
  align-items: center;
  margin: 0 16px;
}

.additions {
  color: #4ec9b0;
  font-weight: 600;
}

.deletions {
  color: #f44747;
  font-weight: 600;
}

.modifications {
  color: #ffcc02;
  font-weight: 600;
}

.file-actions {
  display: flex;
  gap: 8px;
}

/* Diff Container */
.diff-container {
  background-color: var(--vscode-editor-background);
  font-family: var(--vscode-editor-fontFamily, 'Consolas', 'Courier New', monospace);
  font-size: var(--vscode-editor-fontSize, 14px);
  line-height: 1.3;
  overflow-x: auto;
  max-height: 500px;
  overflow-y: auto;
}

.diff-line {
  display: flex;
  align-items: center;
  padding: 2px 0;
  white-space: pre;
  font-family: inherit;
  border-left: 3px solid transparent;
  transition: background-color 0.1s ease;
}

.diff-line:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.diff-line.diff-added {
  background-color: var(--vscode-diffEditor-insertedTextBackground);
  border-left-color: #4ec9b0;
}

.diff-line.diff-removed {
  background-color: var(--vscode-diffEditor-removedTextBackground);
  border-left-color: #f44747;
}

.diff-line.diff-modified {
  background-color: var(--vscode-diffEditor-modifiedTextBackground);
  border-left-color: #ffcc02;
}

.diff-line.diff-unchanged {
  background-color: transparent;
}

.line-number {
  display: inline-block;
  width: 50px;
  text-align: right;
  padding-right: 8px;
  color: var(--vscode-editorLineNumber-foreground);
  font-size: 12px;
  user-select: none;
  flex-shrink: 0;
}

.diff-prefix {
  display: inline-block;
  width: 20px;
  text-align: center;
  font-weight: bold;
  flex-shrink: 0;
}

.diff-added .diff-prefix {
  color: #4ec9b0;
}

.diff-removed .diff-prefix {
  color: #f44747;
}

.diff-modified .diff-prefix {
  color: #ffcc02;
}

.line-content {
  flex: 1;
  padding-left: 8px;
  word-break: break-all;
  white-space: pre-wrap;
}

/* Collapsible sections */
.collapsible-section {
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  margin: 8px 0;
}

.collapsible-header {
  background-color: var(--vscode-editorGroupHeader-tabsBackground);
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
}

.collapsible-header:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.collapsible-content {
  display: none;
  padding: 0;
}

.collapsible-content.expanded {
  display: block;
}

.collapse-indicator {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.collapsed .collapse-indicator {
  transform: rotate(-90deg);
}

/* Search within diffs */
.search-container {
  padding: 8px 16px;
  background-color: var(--vscode-editorWidget-background);
  border-bottom: 1px solid var(--vscode-widget-border);
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 3px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 13px;
}

.search-input:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

.search-results {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

/* Highlighted search results */
.search-highlight {
  background-color: var(--vscode-editor-findMatchBackground);
  border: 1px solid var(--vscode-editor-findMatchBorder);
  border-radius: 2px;
}

.search-highlight.current {
  background-color: var(--vscode-editor-findMatchHighlightBackground);
  border-color: var(--vscode-editor-findMatchHighlightBorder);
}

/* Word-level changes */
.word-change {
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

.word-added {
  background-color: rgba(155, 185, 85, 0.4);
  color: #4ec9b0;
}

.word-removed {
  background-color: rgba(255, 0, 0, 0.4);
  color: #f44747;
  text-decoration: line-through;
}

/* Responsive design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .file-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .change-stats {
    margin: 0;
    justify-content: center;
  }
  
  .file-actions {
    justify-content: center;
  }
  
  .line-number {
    width: 40px;
  }
}

/* Loading states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  color: var(--vscode-descriptionForeground);
}

.loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--vscode-progressBar-background);
  border-top: 2px solid var(--vscode-button-background);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.error {
  background-color: var(--vscode-inputValidation-errorBackground);
  border: 1px solid var(--vscode-inputValidation-errorBorder);
  color: var(--vscode-errorForeground);
  padding: 12px;
  border-radius: 4px;
  margin: 8px 0;
}

.warning {
  background-color: var(--vscode-inputValidation-warningBackground);
  border: 1px solid var(--vscode-inputValidation-warningBorder);
  color: var(--vscode-warningForeground);
  padding: 12px;
  border-radius: 4px;
  margin: 8px 0;
}
