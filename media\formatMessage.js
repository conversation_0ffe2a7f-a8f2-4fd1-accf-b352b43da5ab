function formatMessage(content) {
  if (!content) {
    return '';
  }

  // Replace code blocks
  content = replaceCodeBlocks(content);
  
  // Replace action blocks
  content = replaceActionBlocks(content);
  
  // Replace inline code
  content = replaceInlineCode(content);
  
  // Replace links
  content = replaceLinks(content);
  
  // Replace headers
  content = replaceHeaders(content);
  
  // Replace bold and italic
  content = replaceBoldItalic(content);
  
  // Replace lists
  content = replaceLists(content);
  
  // Replace paragraphs
  content = replaceParagraphs(content);
  
  return content;
}

function replaceCodeBlocks(content) {
  const codeBlockRegex = /```([a-zA-Z0-9]*)\n([\s\S]*?)```/g;
  
  return content.replace(codeBlockRegex, (match, language, code) => {
    const codeBlockTemplate = document.getElementById('code-block-template');
    const codeBlockElement = codeBlockTemplate.content.cloneNode(true);
    
    const languageElement = codeBlockElement.querySelector('.code-language');
    const codeElement = codeBlockElement.querySelector('code');
    const copyButton = codeBlockElement.querySelector('.copy-code-button');
    
    languageElement.textContent = language || 'plaintext';
    codeElement.textContent = code;
    codeElement.className = language ? `language-${language}` : 'language-plaintext';
    
    // Add event listener to copy button
    copyButton.addEventListener('click', () => {
      navigator.clipboard.writeText(code);
      showToast('Code copied to clipboard');
    });
    
    const tempDiv = document.createElement('div');
    tempDiv.appendChild(codeBlockElement);
    return tempDiv.innerHTML;
  });
}

function replaceActionBlocks(content) {
  const actionBlockRegex = /<action-block[^>]*>([\s\S]*?)<\/action-block>/g;
  
  return content.replace(actionBlockRegex, (match, actionContent) => {
    // Extract title if present
    const titleMatch = match.match(/title="([^"]*)"/);
    const title = titleMatch ? titleMatch[1] : 'Action';
    
    // Create action block
    const actionBlockTemplate = document.getElementById('action-block-template');
    const actionBlockElement = actionBlockTemplate.content.cloneNode(true);
    
    const titleElement = actionBlockElement.querySelector('.action-title');
    const contentElement = actionBlockElement.querySelector('.action-content');
    const buttonsElement = actionBlockElement.querySelector('.action-buttons');
    
    titleElement.textContent = title;
    
    // Parse action content
    const actionLines = actionContent.trim().split('\n');
    let formattedContent = '';
    
    for (const line of actionLines) {
      if (line.startsWith('button:')) {
        // Add button
        const buttonMatch = line.match(/button:([^|]*)\|([^|]*)\|?(.*)?/);
        
        if (buttonMatch) {
          const buttonText = buttonMatch[1].trim();
          const buttonAction = buttonMatch[2].trim();
          const buttonType = buttonMatch[3] ? buttonMatch[3].trim() : 'primary';
          
          const button = document.createElement('button');
          button.className = `action-button ${buttonType}`;
          button.textContent = buttonText;
          
          button.addEventListener('click', () => {
            // Handle button action
            handleActionButtonClick(buttonAction);
          });
          
          buttonsElement.appendChild(button);
        }
      } else {
        // Add content
        formattedContent += line + '\n';
      }
    }
    
    contentElement.innerHTML = formatMessage(formattedContent);
    
    const tempDiv = document.createElement('div');
    tempDiv.appendChild(actionBlockElement);
    return tempDiv.innerHTML;
  });
}

function handleActionButtonClick(action) {
  // Parse action
  if (action.startsWith('command:')) {
    const command = action.substring(8);
    
    // Send command to extension
    vscode.postMessage({
      type: 'executeCommand',
      command,
      args: []
    });
  } else if (action.startsWith('openFile:')) {
    const filePath = action.substring(9);
    
    // Send open file message to extension
    vscode.postMessage({
      type: 'openFile',
      filePath
    });
  }
}

function replaceInlineCode(content) {
  return content.replace(/`([^`]+)`/g, '<code>$1</code>');
}

function replaceLinks(content) {
  return content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
}

function replaceHeaders(content) {
  return content
    .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
    .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
    .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
    .replace(/^#### (.*?)$/gm, '<h4>$1</h4>')
    .replace(/^##### (.*?)$/gm, '<h5>$1</h5>')
    .replace(/^###### (.*?)$/gm, '<h6>$1</h6>');
}

function replaceBoldItalic(content) {
  return content
    .replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/~~(.*?)~~/g, '<del>$1</del>');
}

function replaceLists(content) {
  // Unordered lists
  content = content.replace(/^[*-] (.*?)$/gm, '<li>$1</li>');
  // Wrap consecutive <li>...</li> blocks in <ul>
  content = content.replace(/(<li>.*?<\/li>\s*)+/g, match => `<ul>${match}</ul>`);
  
  // Ordered lists
  content = content.replace(/^\d+\. (.*?)$/gm, '<li>$1</li>');
  // Wrap consecutive <li>...</li> blocks in <ol>
  content = content.replace(/(<li>.*?<\/li>\s*)+/g, match => `<ol>${match}</ol>`);
  
  return content;
}

function replaceParagraphs(content) {
  // Replace double newlines with paragraph breaks
  content = content.replace(/\n\n/g, '</p><p>');
  
  // Wrap in paragraph tags if not already wrapped
  if (!content.startsWith('<')) {
    content = `<p>${content}</p>`;
  }
  
  return content;
}

function showToast(message) {
  const toast = document.createElement('div');
  toast.className = 'toast';
  toast.textContent = message;
  
  document.body.appendChild(toast);
  
  setTimeout(() => {
    toast.classList.add('show');
  }, 10);
  
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 3000);
}
