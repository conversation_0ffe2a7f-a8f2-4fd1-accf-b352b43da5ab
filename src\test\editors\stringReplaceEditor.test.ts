import * as assert from 'assert';
import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { StringReplaceEditor, EditOperation } from '../../editors/stringReplaceEditor';

suite('StringReplaceEditor Tests', () => {
  let editor: StringReplaceEditor;
  let testFilePath: string;
  let testContent: string;

  setup(async () => {
    editor = new StringReplaceEditor();
    
    // Create a temporary test file
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    testFilePath = path.join(workspaceRoot, 'test-file.txt');
    testContent = 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5';
    
    await fs.promises.writeFile(testFilePath, testContent, 'utf8');
  });

  teardown(async () => {
    // Clean up test file
    try {
      await fs.promises.unlink(testFilePath);
    } catch (error) {
      // Ignore if file doesn't exist
    }
  });

  test('should read file with lines correctly', async () => {
    const result = await editor.readFileWithLines(testFilePath);
    
    assert.strictEqual(result.content, testContent);
    assert.strictEqual(result.lines.length, 5);
    assert.strictEqual(result.lines[0], 'Line 1');
    assert.strictEqual(result.lines[4], 'Line 5');
  });

  test('should create edit operation for valid line range', async () => {
    const operation = await editor.replaceTextInRange(
      testFilePath,
      2,
      3,
      'Modified Line 2\nModified Line 3'
    );

    assert.strictEqual(operation.filePath, testFilePath);
    assert.strictEqual(operation.startLine, 2);
    assert.strictEqual(operation.endLine, 3);
    assert.strictEqual(operation.oldText, 'Line 2\nLine 3');
    assert.strictEqual(operation.newText, 'Modified Line 2\nModified Line 3');
  });

  test('should throw error for invalid line range', async () => {
    await assert.rejects(
      () => editor.replaceTextInRange(testFilePath, 10, 15, 'New text'),
      /Line numbers exceed file length/
    );
  });

  test('should throw error for start line greater than end line', async () => {
    await assert.rejects(
      () => editor.replaceTextInRange(testFilePath, 3, 2, 'New text'),
      /Start line must be less than or equal to end line/
    );
  });

  test('should validate edit operation correctly', async () => {
    const operation: EditOperation = {
      filePath: testFilePath,
      startLine: 2,
      endLine: 3,
      oldText: 'Line 2\nLine 3',
      newText: 'Modified text',
      timestamp: new Date()
    };

    const result = await editor.validateEdit(operation);
    assert.strictEqual(result.isValid, true);
    assert.strictEqual(result.errors.length, 0);
  });

  test('should detect invalid file in validation', async () => {
    const operation: EditOperation = {
      filePath: 'non-existent-file.txt',
      startLine: 1,
      endLine: 1,
      oldText: 'old',
      newText: 'new',
      timestamp: new Date()
    };

    const result = await editor.validateEdit(operation);
    assert.strictEqual(result.isValid, false);
    assert.ok(result.errors.some(error => error.includes('does not exist')));
  });

  test('should get specific line correctly', async () => {
    const line = await editor.getLine(testFilePath, 3);
    assert.strictEqual(line, 'Line 3');
  });

  test('should throw error for out of range line number', async () => {
    await assert.rejects(
      () => editor.getLine(testFilePath, 10),
      /Line number 10 is out of range/
    );
  });

  test('should get line range correctly', async () => {
    const lines = await editor.getLineRange(testFilePath, 2, 4);
    assert.strictEqual(lines.length, 3);
    assert.strictEqual(lines[0], 'Line 2');
    assert.strictEqual(lines[1], 'Line 3');
    assert.strictEqual(lines[2], 'Line 4');
  });

  test('should handle multi-line replacement with indentation', async () => {
    // Create a test file with indented content
    const indentedContent = '  function test() {\n    console.log("hello");\n  }';
    const indentedFilePath = path.join(path.dirname(testFilePath), 'indented-test.js');
    await fs.promises.writeFile(indentedFilePath, indentedContent, 'utf8');

    try {
      const operation = await editor.replaceMultipleLines(
        indentedFilePath,
        2,
        2,
        ['console.log("modified");'],
        true // preserve indentation
      );

      assert.ok(operation.newText.includes('    console.log("modified");'));
    } finally {
      await fs.promises.unlink(indentedFilePath);
    }
  });

  test('should create insert operation correctly', async () => {
    const operation = await editor.insertLinesAt(
      testFilePath,
      3,
      ['Inserted Line 1', 'Inserted Line 2']
    );

    assert.strictEqual(operation.startLine, 3);
    assert.strictEqual(operation.endLine, 2); // Insert operation
    assert.strictEqual(operation.oldText, '');
    assert.ok(operation.newText.includes('Inserted Line 1'));
  });

  test('should create delete operation correctly', async () => {
    const operation = await editor.deleteLineRange(testFilePath, 2, 3);

    assert.strictEqual(operation.startLine, 2);
    assert.strictEqual(operation.endLine, 3);
    assert.strictEqual(operation.oldText, 'Line 2\nLine 3');
    assert.strictEqual(operation.newText, '');
  });

  test('should insert code at specific line with smart indentation', async () => {
    const jsContent = 'function test() {\n  let x = 1;\n  return x;\n}';
    const jsFilePath = path.join(path.dirname(testFilePath), 'test.js');
    await fs.promises.writeFile(jsFilePath, jsContent, 'utf8');

    try {
      const operation = await editor.insertCodeAtLine(
        jsFilePath,
        3,
        'console.log(x);',
        true // match indentation
      );

      assert.ok(operation.newText.includes('  console.log(x);'));
    } finally {
      await fs.promises.unlink(jsFilePath);
    }
  });

  test('should insert code after pattern', async () => {
    const operation = await editor.insertCodeAfterPattern(
      testFilePath,
      /Line 2/,
      'New line after Line 2'
    );

    assert.strictEqual(operation.startLine, 4); // After Line 2 (which is line 2)
  });

  test('should insert code before pattern', async () => {
    const operation = await editor.insertCodeBeforePattern(
      testFilePath,
      /Line 3/,
      'New line before Line 3'
    );

    assert.strictEqual(operation.startLine, 3); // Before Line 3
  });

  test('should throw error when pattern not found', async () => {
    await assert.rejects(
      () => editor.insertCodeAfterPattern(testFilePath, /NonExistent/, 'New line'),
      /Pattern not found/
    );
  });

  test('should validate JSON syntax correctly', async () => {
    const validJson = '{"key": "value"}';
    const invalidJson = '{"key": "value"';

    const validResult = await editor.validateSyntax('test.json', validJson);
    assert.strictEqual(validResult.isValid, true);

    const invalidResult = await editor.validateSyntax('test.json', invalidJson);
    assert.strictEqual(invalidResult.isValid, false);
    assert.ok(invalidResult.errors.some(error => error.includes('Invalid JSON syntax')));
  });

  test('should validate JavaScript syntax correctly', async () => {
    const validJs = 'function test() { return true; }';
    const invalidJs = 'function test() { return true;';

    const validResult = await editor.validateSyntax('test.js', validJs);
    assert.strictEqual(validResult.isValid, true);

    const invalidResult = await editor.validateSyntax('test.js', invalidJs);
    assert.strictEqual(invalidResult.isValid, false);
    assert.ok(invalidResult.errors.some(error => error.includes('Mismatched braces')));
  });

  test('should handle file not found error gracefully', async () => {
    await assert.rejects(
      () => editor.readFileWithLines('non-existent-file.txt'),
      /File not found/
    );
  });

  test('should get line count correctly', async () => {
    const lineCount = await editor.getLineCount(testFilePath);
    assert.strictEqual(lineCount, 5);
  });

  test('should apply edit operation correctly', async () => {
    const operation: EditOperation = {
      filePath: testFilePath,
      startLine: 2,
      endLine: 2,
      oldText: 'Line 2',
      newText: 'Modified Line 2',
      timestamp: new Date()
    };

    await editor.applyEdit(operation);

    const { content } = await editor.readFileWithLines(testFilePath);
    assert.ok(content.includes('Modified Line 2'));
    assert.ok(!content.includes('Line 2\nLine 3')); // Original Line 2 should be replaced
  });

  test('should apply insert operation correctly', async () => {
    const operation: EditOperation = {
      filePath: testFilePath,
      startLine: 3,
      endLine: 2, // Insert operation
      oldText: '',
      newText: 'Inserted Line',
      timestamp: new Date()
    };

    await editor.applyInsert(operation);

    const { lines } = await editor.readFileWithLines(testFilePath);
    assert.strictEqual(lines[2], 'Inserted Line');
    assert.strictEqual(lines[3], 'Line 3'); // Original Line 3 should be shifted
  });
});
