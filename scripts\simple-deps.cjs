const fs = require('fs');
const path = require('path');

// Configuration
const PROJECT_ROOT = path.join(__dirname, '..');
const OUTPUT_DIR = path.join(PROJECT_ROOT, 'docs', 'dependencies');
const IGNORE_DIRS = ['node_modules', '.git', 'dist', 'build', 'coverage'];

// Results storage
const dependencyMap = {};

// Find all TypeScript files
function findTypeScriptFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);

  list.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!IGNORE_DIRS.includes(file)) {
        results = results.concat(findTypeScriptFiles(fullPath));
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      results.push(fullPath);
    }
  });

  return results;
}

// Analyze a single file for imports
function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  const imports = [];
  const exports = [];
  
  // Simple regex to find imports (this is a basic implementation)
  const importRegex = /from\s+['"]([^'"]+)['"]|import\s+[^'"\n]+['"]([^'"]+)['"]/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    const importPath = match[1] || match[2];
    if (importPath && !importPath.startsWith('.')) {
      imports.push(importPath.split('/')[0]); // Only take the package name
    }
  }
  
  return { imports, exports };
}

// Generate markdown report
function generateReport(dependencyMap) {
  let report = '# Dependency Analysis Report\n\n';
  report += `Generated on: ${new Date().toISOString()}\n\n`;
  
  // Summary
  report += '## Summary\n\n';
  report += `- Total files analyzed: ${Object.keys(dependencyMap).length}\n`;
  
  // Count imports
  const importCounts = {};
  Object.values(dependencyMap).forEach(({ imports }) => {
    imports.forEach(imp => {
      importCounts[imp] = (importCounts[imp] || 0) + 1;
    });
  });
  
  report += `- Total unique imports: ${Object.keys(importCounts).length}\n\n`;
  
  // Most used packages
  const sortedImports = Object.entries(importCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20);
    
  report += '## Most Used Packages\n\n';
  report += '| Package | Usage Count |\n|---------|-------------:|\n';
  sortedImports.forEach(([pkg, count]) => {
    report += `| ${pkg} | ${count} |\n`;
  });
  
  return report;
}

// Main function
function main() {
  console.log('Starting dependency analysis...');
  
  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  
  // Find and analyze all TypeScript files
  const tsFiles = findTypeScriptFiles(PROJECT_ROOT);
  console.log(`Found ${tsFiles.length} TypeScript files to analyze...`);
  
  tsFiles.forEach((file, index) => {
    const relativePath = path.relative(PROJECT_ROOT, file);
    process.stdout.write(`\rAnalyzing ${index + 1}/${tsFiles.length}: ${relativePath}`);
    
    try {
      dependencyMap[relativePath] = analyzeFile(file);
    } catch (error) {
      console.error(`\nError analyzing ${relativePath}:`, error);
    }
  });
  
  // Generate and save reports
  const report = generateReport(dependencyMap);
  const reportPath = path.join(OUTPUT_DIR, 'dependency-report.md');
  fs.writeFileSync(reportPath, report);
  
  console.log(`\n\nAnalysis complete! Report saved to: ${reportPath}`);
  
  // Save raw data as JSON
  const jsonPath = path.join(OUTPUT_DIR, 'dependencies.json');
  fs.writeFileSync(jsonPath, JSON.stringify(dependencyMap, null, 2));
  console.log(`Raw data saved to: ${jsonPath}`);
}

// Run the analysis
main();
