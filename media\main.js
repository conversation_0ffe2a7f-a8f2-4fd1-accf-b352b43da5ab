(function () {
  console.log('*** Vidyadhara main.js script loaded ***');
  // Get VS Code API
  const vscode = acquireVsCodeApi();
  console.log('vscode:', vscode);

  // DOM Elements
  // Debug: Log DOM elements and VS Code API
  // These will be undefined/null if script is loaded before DOM
  // or if acquireVs<PERSON>ode<PERSON><PERSON> is not available
  // They will help us debug why event listeners or messaging is not working

  const chatMessages = document.getElementById('chat-messages');
  const messageInput = document.getElementById('message-input');
  const sendButton = document.getElementById('send-button');
  console.log('sendButton:', sendButton);
  console.log('messageInput:', messageInput);
  const clearButton = document.getElementById('clear-button');
  const stopButton = document.getElementById('stop-button');
  const newChatButton = document.getElementById('new-chat-button');
  const settingsButton = document.getElementById('settings-button');
  const closeSettingsButton = document.getElementById('close-settings-button');
  const settingsPanel = document.getElementById('settings-panel');
  const saveSettingsButton = document.getElementById('save-settings-button');
  const modelSelector = document.getElementById('model-selector');

  // Templates
  const userMessageTemplate = document.getElementById('user-message-template');
  const assistantMessageTemplate = document.getElementById('assistant-message-template');

  // State
  let currentConversation = null;
  let isWaitingForResponse = false;
  let currentResponseMessageId = null;
  let queuedMessage = null; // <-- Add a queue for the first message
  let backendReady = false;
  let backendReadyTimeout = null;

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      sendWebviewReady();
      init();
    });
  } else {
    sendWebviewReady();
    init();
  }

  function init() {
    // Set up event listeners for main controls
    if (messageInput) messageInput.addEventListener('keydown', handleMessageInputKeydown);
    if (sendButton) sendButton.addEventListener('click', sendMessage);
    if (clearButton) clearButton.addEventListener('click', clearConversation);
    if (stopButton) stopButton.addEventListener('click', stopResponse);
    if (newChatButton) newChatButton.addEventListener('click', createNewConversation);
    if (settingsButton) settingsButton.addEventListener('click', toggleSettingsPanel);
    if (closeSettingsButton) closeSettingsButton.addEventListener('click', toggleSettingsPanel);
    if (saveSettingsButton) saveSettingsButton.addEventListener('click', saveSettings);
    if (modelSelector) modelSelector.addEventListener('change', changeModel);
    if (messageInput) messageInput.addEventListener('input', autoResizeTextarea);

    // Handle messages from the extension
    window.addEventListener('message', handleExtensionMessage);

    // Initialize the extension
    initExtension();
  }

  function initExtension() {
    // Request conversations list
    logAndSendMessage({ command: 'getConversations' });

    // Load settings
    loadSettings();

    // Initialize ModeHandler
    initializeModeHandler();

    // Load mode controls
    loadModeControls();

    // Request current mode from extension
    logAndSendMessage({ command: 'getCurrentMode' });
  }

  function initializeModeHandler() {
    try {
      console.log('[main] Initializing ModeManager...');

      // Request current mode from the extension
      logAndSendMessage({ command: 'getCurrentMode' });
      
      console.log('[main] ModeManager initialization requested');
      return true;
    } catch (error) {
      console.error('[main] Error initializing ModeManager:', error);
      return false;
    }
  }

  function loadModeControls() {
    const modeControls = document.getElementById('mode-controls');
    if (!modeControls) {
      console.error('Mode controls container not found');
      return;
    }

    // Request mode controls from the extension
    logAndSendMessage({ command: 'getModeControls' });

    // Set up event delegation for mode buttons
    modeControls.addEventListener('click', (e) => {
      const button = e.target.closest('.mode-button');
      if (button) {
        e.preventDefault();
        const mode = button.getAttribute('data-mode');
        if (mode) {
          logAndSendMessage({ command: 'setMode', mode });
        }
      }
    });

    // Set up a check for mode initialization
    const retryInterval = 500; // ms
    let retryCount = 0;
    const maxRetries = 5;

    const checkModeInitialization = setInterval(() => {
      const activeButton = document.querySelector('.mode-button.active');

      if (activeButton) {
        console.log('Mode buttons initialized successfully');
        clearInterval(checkModeInitialization);
        return;
      }

      retryCount++;
      if (retryCount >= maxRetries) {
        console.warn('Failed to initialize mode buttons after', maxRetries, 'attempts');
        clearInterval(checkModeInitialization);
        return;
      }

      console.log('No active mode button found, requesting current mode again');
      logAndSendMessage({ command: 'getCurrentMode' });
    }, retryInterval);
  }

  function handleMessageInputKeydown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  }

  function autoResizeTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = (messageInput.scrollHeight) + 'px';
  }

  function sendMessage() {
    const message = messageInput.value.trim();

    if (!message || isWaitingForResponse) {
      return;
    }

    // Disable input and button immediately
    messageInput.disabled = true;
    sendButton.disabled = true;
    isWaitingForResponse = true;

    // If no conversation, queue the message and request a new conversation
    if (!currentConversation) {
      queuedMessage = message;
      logAndSendMessage({ command: 'newConversation' });
      return;
    }

    // Clear input before sending
    messageInput.value = '';
    messageInput.style.height = 'auto';

    // Generate a message ID
    const messageId = Date.now().toString();

    // Send message to extension
    logAndSendMessage({ command: 'sendMessage', text: message, messageId });

    // Update state
    currentResponseMessageId = messageId;

    // Show stop button
    stopButton.style.display = 'block';
    sendButton.style.display = 'none';
  }

  function addUserMessage(content) {
    const messageElement = userMessageTemplate.content.cloneNode(true);
    const messageContent = messageElement.querySelector('.message-content');
    const messageDiv = messageElement.querySelector('.message');

    messageContent.textContent = content;

    // Add timestamp
    const timestamp = document.createElement('div');
    timestamp.className = 'message-timestamp';
    timestamp.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    messageDiv.appendChild(timestamp);

    chatMessages.appendChild(messageElement);
    scrollToBottom();
  }

  function addAssistantMessage(content, messageId) {
    const messageElement = assistantMessageTemplate.content.cloneNode(true);
    const messageContent = messageElement.querySelector('.message-content');
    const copyButton = messageElement.querySelector('.copy-button');
    const messageDiv = messageElement.querySelector('.message');

    // Set message ID as data attribute
    messageDiv.dataset.messageId = messageId;

    // Format message content
    messageContent.innerHTML = formatMessage(content);

    // Add timestamp
    const timestamp = document.createElement('div');
    timestamp.className = 'message-timestamp';
    timestamp.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    messageDiv.appendChild(timestamp);

    // Add event listener to copy button
    copyButton.addEventListener('click', () => {
      navigator.clipboard.writeText(content);
      showToast('Copied to clipboard');
    });

    chatMessages.appendChild(messageElement);
    scrollToBottom();
  }

  function updateAssistantMessage(content, messageId) {
    const messageElement = document.querySelector(`.message[data-message-id="${messageId}"]`);

    if (messageElement) {
      const messageContent = messageElement.querySelector('.message-content');
      messageContent.innerHTML = formatMessage(content);
      scrollToBottom();
    }
  }

  function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function clearConversation() {
    // Clear UI
    chatMessages.innerHTML = '';

    // Create new conversation
    logAndSendMessage({ command: 'newConversation' });
  }

  function stopResponse() {
    // Send stop message to extension
    logAndSendMessage({ command: 'stopResponse' });

    // Update state
    isWaitingForResponse = false;
    currentResponseMessageId = null;

    // Hide stop button
    stopButton.classList.remove('visible');
  }

  function createNewConversation() {
    // Clear UI
    chatMessages.innerHTML = '';

    // Create new conversation
    logAndSendMessage({ command: 'newConversation' });
  }

  function toggleSettingsPanel() {
    const settingsPanel = document.getElementById('settings-panel');
    if (settingsPanel) {
      settingsPanel.classList.toggle('visible');
      // Toggle body scroll when settings panel is open
      document.body.style.overflow = settingsPanel.classList.contains('visible') ? 'hidden' : '';
    }
  }

  function loadSettings() {
    // Request settings from extension
    logAndSendMessage({ command: 'getSettings' });
    
    // Set up event listeners for settings panel
    const saveSettingsBtn = document.getElementById('save-settings-button');
    const closeSettingsBtn = document.getElementById('close-settings-button');
    const settingsBtn = document.getElementById('settings-button');

    if (saveSettingsBtn) saveSettingsBtn.addEventListener('click', saveSettings);
    if (closeSettingsBtn) closeSettingsBtn.addEventListener('click', toggleSettingsPanel);
    if (settingsBtn) settingsBtn.addEventListener('click', toggleSettingsPanel);
    
    // Set up GitHub token visibility toggle
    const toggleTokenBtn = document.getElementById('toggle-token-visibility');
    const githubTokenInput = document.getElementById('github-token');
    
    if (toggleTokenBtn && githubTokenInput) {
      toggleTokenBtn.addEventListener('click', () => {
        const isPassword = githubTokenInput.type === 'password';
        githubTokenInput.type = isPassword ? 'text' : 'password';
        const icon = toggleTokenBtn.querySelector('svg');
        if (icon) {
          // Toggle between eye and eye-slash icons
          if (isPassword) {
            icon.innerHTML = '<path d="M12 19c.94 0 1.92-.19 2.84-.52l-.74-.74c-.49.15-1 .26-1.53.26-5 0-9.27-3.11-11-7.5.25-.6.75-1.5 1.5-2.5l1.8 1.4c-.5.8-.8 1.6-1 2.1 1.6 2.3 4.1 3.5 7.7 3.5.52 0 1.03-.05 1.53-.15l.74.74c-.92.33-1.9.51-2.84.51z"/>';
            toggleTokenBtn.setAttribute('aria-label', 'Hide token');
          } else {
            icon.innerHTML = '<path d="M12 9a3 3 0 0 1 3 3 3 3 0 0 1-3 3 3 3 0 0 1-3-3 3 3 0 0 1 3-3m0-4.5c5 0 9.27 3.11 11 7.5-1.73 4.39-6 7.5-11 7.5S2.73 16.39 1 12c1.73-4.39 6-7.5 11-7.5M3.18 12a9.82 9.82 0 0 0 17.64 0 9.82 9.82 0 0 0-17.64 0z"/>';
            toggleTokenBtn.setAttribute('aria-label', 'Show token');
          }
        }
      });
    }
    
    // Auto-check GitHub connection when settings are loaded
    const checkConnection = () => {
      const token = document.getElementById('github-token').value;
      const owner = document.getElementById('github-owner').value;
      const repo = document.getElementById('github-repo').value;
      
      if (token && owner && repo) {
        checkGitHubConnection({ githubToken: token, githubOwner: owner, githubRepo: repo });
      }
    };
    
    // Check connection when GitHub settings change
    const githubInputs = ['github-token', 'github-owner', 'github-repo'];
    githubInputs.forEach(id => {
      const input = document.getElementById(id);
      if (input) {
        input.addEventListener('change', checkConnection);
        input.addEventListener('blur', checkConnection);
      }
    });
    
    // Add input validation for max context length
    const maxContextInput = document.getElementById('max-context-length');
    if (maxContextInput) {
      maxContextInput.addEventListener('change', () => {
        let value = parseInt(maxContextInput.value, 10);
        if (isNaN(value) || value < 1000) {
          value = 1000;
        } else if (value > 1000000) {
          value = 1000000;
        }
        maxContextInput.value = value;
      });
    }
  }

  function saveSettings() {
    // Get all settings
    const settings = {
      openRouterApiKey: document.getElementById('openrouter-api-key').value,
      githubToken: document.getElementById('github-token').value,
      githubOwner: document.getElementById('github-owner').value,
      githubRepo: document.getElementById('github-repo').value,
      githubBranch: document.getElementById('github-branch').value || 'main',
      defaultModel: document.getElementById('default-model').value,
      maxContextLength: parseInt(document.getElementById('max-context-length').value, 10) || 100000,
      theme: document.getElementById('theme-selector').value
    };
    
    logAndSendMessage({ command: 'saveSettings', settings: settings });
    
    // Check GitHub connection if credentials are provided
    if (settings.githubToken && settings.githubOwner && settings.githubRepo) {
      checkGitHubConnection(settings);
    }
    
    toggleSettingsPanel();
    showToast('Settings saved');
  }
  
  function checkGitHubConnection(settings) {
    const statusEl = document.getElementById('github-connection-status');
    if (!statusEl) return;
    
    statusEl.textContent = 'Checking connection...';
    statusEl.className = 'github-status';
    
    // In a real implementation, this would call the extension's backend
    // For now, we'll simulate a successful connection
    setTimeout(() => {
      if (settings.githubToken && settings.githubOwner && settings.githubRepo) {
        statusEl.textContent = `Connected to ${settings.githubOwner}/${settings.githubRepo}`;
        statusEl.className = 'github-status connected';
      } else {
        statusEl.textContent = 'Please fill in all GitHub details';
        statusEl.className = 'github-status disconnected';
      }
    }, 1000);
  }

  function changeModel() {
    const model = modelSelector.value;

    logAndSendMessage({ command: 'changeModel', model });

    showToast(`Model changed to ${model}`);
  }

  function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  }

  function formatModeName(modeId) {
    if (!modeId) return 'Unknown';

    switch (modeId) {
      case 'chat': return 'Chat';
      case 'agent': return 'Agent';
      case 'agent-auto': return 'Agent Auto';
      default: return modeId.charAt(0).toUpperCase() + modeId.slice(1).replace('-', ' ');
    }
  }

  function showTypingIndicator() {
    const typingIndicator = document.createElement('div');
    typingIndicator.className = 'typing-indicator';
    typingIndicator.id = 'typing-indicator';

    for (let i = 0; i < 3; i++) {
      const dot = document.createElement('span');
      typingIndicator.appendChild(dot);
    }

    chatMessages.appendChild(typingIndicator);
    scrollToBottom();
  }

  function removeTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
      typingIndicator.remove();
    }
  }

  // Direct event handler for mode buttons (as a backup)
  document.addEventListener('click', (event) => {
    const button = event.target.closest('.mode-button');
    if (button) {
      const modeId = button.dataset.mode;
      console.log('[main.js] Mode button clicked directly:', modeId);
      logAndSendMessage({ command: 'changeMode', modeId });
    }
  });

  function showBackendError() {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'backend-error';
    errorDiv.textContent = 'Error: Extension backend is not responding. Please reload the window or check the extension host.';
    document.body.appendChild(errorDiv);
  }

  function sendWebviewReady() {
    console.log('[main.js] Sending webviewReady ping to backend');
    logAndSendMessage({ command: 'webviewReady' });
    backendReadyTimeout = setTimeout(() => {
      if (!backendReady) {
        showBackendError();
      }
    }, 2000);
  }

  function handleExtensionMessage(event) {
    const message = event.data;
    const messageType = message.type || message.command;
    console.log('[main.js] Received message from backend:', message);
    switch (messageType) {
      case 'ready':
        backendReady = true;
        if (backendReadyTimeout) clearTimeout(backendReadyTimeout);
        console.log('[main.js] Backend responded to ping (ready)');
        break;
      case 'setConversation':
        currentConversation = message.conversation;
        renderConversation(currentConversation);
        // If a message was queued, send it now
        if (queuedMessage) {
          const msg = queuedMessage;
          queuedMessage = null;
          messageInput.value = msg;
          sendMessage();
        }
        break;

      case 'startResponse':
        // Show typing indicator
        showTypingIndicator();
        // Add empty assistant message
        addAssistantMessage('', message.messageId);
        break;

      case 'updateResponse':
        // Remove typing indicator
        removeTypingIndicator();
        updateAssistantMessage(message.content, message.messageId);
        break;

      case 'completeResponse':
        // Remove typing indicator
        removeTypingIndicator();
        updateAssistantMessage(message.content, message.messageId);

        // Update state
        isWaitingForResponse = false;
        currentResponseMessageId = null;

        // Hide stop button
        stopButton.classList.remove('visible');
        break;

      case 'error':
        // Remove typing indicator
        removeTypingIndicator();
        showToast(`Error: ${message.message}`);

        // Update state
        isWaitingForResponse = false;
        currentResponseMessageId = null;

        // Hide stop button
        stopButton.classList.remove('visible');
        break;

      case 'conversationsList':
        // Handle conversations list
        break;

      case 'updateActiveMode':
        // Update the active mode button
        const modeButtons = document.querySelectorAll('.mode-button');
        modeButtons.forEach(button => {
          const buttonMode = button.getAttribute('data-mode');
          if (buttonMode === message.modeId) {
            button.classList.add('active');
            button.setAttribute('aria-pressed', 'true');
            button.title = `Currently in ${message.modeName} mode`;
          } else {
            button.classList.remove('active');
            button.setAttribute('aria-pressed', 'false');
            button.removeAttribute('title');
          }
        });
        
        // Update the mode display if it exists
        const modeDisplay = document.getElementById('current-mode-display');
        if (modeDisplay) {
          modeDisplay.textContent = message.modeName;
          modeDisplay.title = `Current mode: ${message.modeName}`;
        }
        
        // Show a toast notification if this was a mode change
        if (message.timestamp) {
          showToast(`Switched to ${message.modeName} mode`);
        }
        break;
        
      case 'modeControls':
        // Update the mode controls with the available modes
        const modeControls = document.getElementById('mode-controls');
        if (modeControls) {
          modeControls.innerHTML = message.availableModes.map(mode => `
            <button 
              class="mode-button ${mode.isActive ? 'active' : ''}" 
              data-mode="${mode.id}"
              aria-pressed="${mode.isActive ? 'true' : 'false'}"
              title="${mode.description}"
            >
              <span class="mode-icon">${mode.icon}</span>
              <span class="mode-name">${mode.name}</span>
            </button>
          `).join('');
          
          // Set up event listeners for the new buttons
          modeControls.querySelectorAll('.mode-button').forEach(button => {
            button.addEventListener('click', (e) => {
              const mode = button.getAttribute('data-mode');
              if (mode) {
                logAndSendMessage({ command: 'setMode', mode });
              }
            });
          });
        }
        break;
        
      case 'settings':
        // Update API Keys
        const openrouterApiKeyInput = document.getElementById('openrouter-api-key');
        if (openrouterApiKeyInput) {
          openrouterApiKeyInput.value = message.settings.openRouterApiKey || '';
        }
        
        // Update GitHub Integration
        const githubToken = message.settings.githubToken || '';
        const githubOwner = message.settings.githubOwner || '';
        const githubRepo = message.settings.githubRepo || '';
        const githubBranch = message.settings.githubBranch || 'main';
        
        const githubTokenInput = document.getElementById('github-token');
        const githubOwnerInput = document.getElementById('github-owner');
        const githubRepoInput = document.getElementById('github-repo');
        const githubBranchInput = document.getElementById('github-branch');

        if (githubTokenInput) githubTokenInput.value = githubToken;
        if (githubOwnerInput) githubOwnerInput.value = githubOwner;
        if (githubRepoInput) githubRepoInput.value = githubRepo;
        if (githubBranchInput) githubBranchInput.value = githubBranch;
        
        // Check GitHub connection if we have all required fields
        if (githubToken && githubOwner && githubRepo) {
          checkGitHubConnection({
            githubToken,
            githubOwner,
            githubRepo,
            githubBranch
          });
        }
        
        // Update Model Settings
        const defaultModel = message.settings.defaultModel || 'anthropic/claude-3-opus-20240229';
        const defaultModelInput = document.getElementById('default-model');
        const maxContextInput = document.getElementById('max-context-length');

        if (defaultModelInput) defaultModelInput.value = defaultModel;
        if (maxContextInput) maxContextInput.value = message.settings.maxContextLength || 100000;
        
        // Update Appearance
        document.getElementById('theme-selector').value = message.settings.theme || 'system';
        
        // Update model selector
        modelSelector.value = defaultModel;
        
        console.log('Settings loaded successfully');
        break;
        
      case 'githubConnectionStatus':
        const statusEl = document.getElementById('github-connection-status');
        if (!statusEl) break;
        
        if (message.connected) {
          statusEl.textContent = message.message || 'Connected to GitHub';
          statusEl.className = 'github-status connected';
        } else {
          statusEl.textContent = message.message || 'Failed to connect to GitHub';
          statusEl.className = 'github-status disconnected';
        }
        break;
        
      case 'settingsSaved':
        showToast('Settings saved successfully');
        break;

      case 'updateActiveMode':
        console.log('Received updateActiveMode message:', message);

        // Get the mode ID from the message (support both modeId and mode for backward compatibility)
        const modeId = message.modeId || message.mode;
        if (!modeId) {
          console.error('No mode ID provided in updateActiveMode message');
          return;
        }

        console.log(`Updating active mode to: ${modeId}`);

        // Try to use ModeHandler first
        const modeHandler = window.modeHandler;
        if (modeHandler && typeof modeHandler.updateActiveMode === 'function') {
          console.log('Using ModeHandler to update active mode');
          modeHandler.updateActiveMode(modeId);
        } else {
          console.warn('ModeHandler not available or missing updateActiveMethod, using fallback');
          // Fallback direct update if ModeHandler isn't available
          document.querySelectorAll('.mode-button').forEach(btn => {
            if (btn && btn.classList) {
              btn.classList.remove('active');
            }
          });

          const activeBtn = document.querySelector(`.mode-button[data-mode="${modeId}"]`);
          if (activeBtn) {
            activeBtn.classList.add('active');
          } else {
            console.warn(`Could not find mode button for mode: ${modeId}`);
          }
        }

        // Store mode in webview state
        if (vscode && typeof vscode.setState === 'function') {
          vscode.setState({ mode: modeId });
        }

        // Show toast notification
        const modeName = formatModeName(modeId);
        console.log(`Mode changed to: ${modeName}`);
        showToast(`Switched to ${modeName} mode`);

        // If ModeHandler wasn't available, try to initialize it
        if (!modeHandler) {
          console.log('Attempting to initialize ModeHandler...');
          initializeModeHandler();
        }
        break;
    }
  }

  function formatModeName(modeId) {
    if (!modeId) return 'Unknown';

    switch (modeId) {
      case 'chat': return 'Chat';
      case 'agent': return 'Agent';
      case 'agent-auto': return 'Agent Auto';
      default: return modeId.charAt(0).toUpperCase() + modeId.slice(1).replace('-', ' ');
    }
  }

  function formatMessage(content) {
    if (!content) return '';
    
    // Initialize marked with options if not already done
    if (window.marked) {
      marked.setOptions({
        gfm: true,
        breaks: true,
        sanitize: true
      });
    }

    // Use marked to parse markdown or return plain text if marked is not available
    let html;
    try {
      html = window.marked ? window.marked.parse(content) : content;
    } catch (e) {
      console.error('Error parsing markdown:', e);
      html = content;
    }

    // Process code blocks for syntax highlighting
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Add syntax highlighting to code blocks if hljs is available
    if (window.hljs) {
      tempDiv.querySelectorAll('pre code').forEach((block) => {
        try {
          hljs.highlightElement(block);
        } catch (e) {
          console.error('Error highlighting code block:', e);
        }
      });
    }

    // Add copy buttons to code blocks
    tempDiv.querySelectorAll('pre').forEach((pre) => {
      const codeBlock = document.createElement('div');
      codeBlock.className = 'code-block';

      const codeHeader = document.createElement('div');
      codeHeader.className = 'code-header';

      const codeLanguage = document.createElement('span');
      codeLanguage.className = 'code-language';
      const code = pre.querySelector('code');
      const language = code.className.replace('language-', '');
      codeLanguage.textContent = language || 'Code';

      const copyButton = document.createElement('button');
      copyButton.className = 'copy-code-button';
      copyButton.textContent = 'Copy';
      copyButton.addEventListener('click', () => {
        const codeText = code.textContent;
        navigator.clipboard.writeText(codeText);
        copyButton.textContent = 'Copied!';
        setTimeout(() => {
          copyButton.textContent = 'Copy';
        }, 2000);
      });

      codeHeader.appendChild(codeLanguage);
      codeHeader.appendChild(copyButton);

      codeBlock.appendChild(codeHeader);
      codeBlock.appendChild(pre.cloneNode(true));

      pre.replaceWith(codeBlock);
    });

    return tempDiv.innerHTML;
  }

  function renderConversation(conversation) {
    // Clear UI
    chatMessages.innerHTML = '';

    // Render messages
    for (const message of conversation.messages) {
      if (message.role === 'user') {
        addUserMessage(message.content);
      } else if (message.role === 'assistant') {
        addAssistantMessage(message.content, message.id);
      }
    }
  }

  function logAndSendMessage(msg) {
    console.log('[main.js] Sending message to backend:', msg);
    vscode.postMessage(msg);
  }
})();
