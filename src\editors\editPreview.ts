import * as vscode from 'vscode';
import { EditOperation } from './stringReplaceEditor';

export interface DiffLine {
  type: 'unchanged' | 'added' | 'removed' | 'modified';
  lineNumber: number;
  content: string;
  originalLineNumber?: number;
}

export interface EditPreviewData {
  filePath: string;
  originalContent: string;
  modifiedContent: string;
  diffLines: DiffLine[];
  changeStats: {
    additions: number;
    deletions: number;
    modifications: number;
  };
}

export interface BatchEditPreview {
  edits: EditPreviewData[];
  totalStats: {
    filesChanged: number;
    totalAdditions: number;
    totalDeletions: number;
    totalModifications: number;
  };
}

export class EditPreview {
  /**
   * Generate diff between original and modified content
   */
  generateDiff(originalContent: string, modifiedContent: string): DiffLine[] {
    const originalLines = originalContent.split('\n');
    const modifiedLines = modifiedContent.split('\n');
    const diffLines: DiffLine[] = [];

    // Simple diff algorithm - can be enhanced with more sophisticated algorithms
    const maxLines = Math.max(originalLines.length, modifiedLines.length);
    let originalIndex = 0;
    let modifiedIndex = 0;

    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[originalIndex];
      const modifiedLine = modifiedLines[modifiedIndex];

      if (originalLine === modifiedLine) {
        // Unchanged line
        diffLines.push({
          type: 'unchanged',
          lineNumber: modifiedIndex + 1,
          content: modifiedLine || '',
          originalLineNumber: originalIndex + 1
        });
        originalIndex++;
        modifiedIndex++;
      } else if (originalIndex >= originalLines.length) {
        // Added line
        diffLines.push({
          type: 'added',
          lineNumber: modifiedIndex + 1,
          content: modifiedLine
        });
        modifiedIndex++;
      } else if (modifiedIndex >= modifiedLines.length) {
        // Removed line
        diffLines.push({
          type: 'removed',
          lineNumber: -1,
          content: originalLine,
          originalLineNumber: originalIndex + 1
        });
        originalIndex++;
      } else {
        // Modified line
        diffLines.push({
          type: 'removed',
          lineNumber: -1,
          content: originalLine,
          originalLineNumber: originalIndex + 1
        });
        diffLines.push({
          type: 'added',
          lineNumber: modifiedIndex + 1,
          content: modifiedLine
        });
        originalIndex++;
        modifiedIndex++;
      }
    }

    return diffLines;
  }

  /**
   * Create preview data for a single edit operation
   */
  async createEditPreview(editOperation: EditOperation): Promise<EditPreviewData> {
    try {
      // Read current file content
      const document = await vscode.workspace.openTextDocument(editOperation.filePath);
      const originalContent = document.getText();
      
      // Apply the edit to create modified content
      const lines = originalContent.split('\n');
      const modifiedLines = [
        ...lines.slice(0, editOperation.startLine - 1),
        ...editOperation.newText.split('\n'),
        ...lines.slice(editOperation.endLine)
      ];
      const modifiedContent = modifiedLines.join('\n');

      // Generate diff
      const diffLines = this.generateDiff(originalContent, modifiedContent);

      // Calculate change statistics
      const changeStats = this.calculateChangeStats(diffLines);

      return {
        filePath: editOperation.filePath,
        originalContent,
        modifiedContent,
        diffLines,
        changeStats
      };
    } catch (error) {
      throw new Error(`Failed to create edit preview: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create batch edit preview for multiple operations
   */
  async createBatchEditPreview(editOperations: EditOperation[]): Promise<BatchEditPreview> {
    const edits: EditPreviewData[] = [];
    let totalAdditions = 0;
    let totalDeletions = 0;
    let totalModifications = 0;

    for (const operation of editOperations) {
      const preview = await this.createEditPreview(operation);
      edits.push(preview);
      
      totalAdditions += preview.changeStats.additions;
      totalDeletions += preview.changeStats.deletions;
      totalModifications += preview.changeStats.modifications;
    }

    return {
      edits,
      totalStats: {
        filesChanged: editOperations.length,
        totalAdditions,
        totalDeletions,
        totalModifications
      }
    };
  }

  /**
   * Calculate change statistics from diff lines
   */
  private calculateChangeStats(diffLines: DiffLine[]): { additions: number; deletions: number; modifications: number } {
    let additions = 0;
    let deletions = 0;
    let modifications = 0;

    for (const line of diffLines) {
      switch (line.type) {
        case 'added':
          additions++;
          break;
        case 'removed':
          deletions++;
          break;
        case 'modified':
          modifications++;
          break;
      }
    }

    return { additions, deletions, modifications };
  }

  /**
   * Show confirmation dialog for edit operation
   */
  async showEditConfirmation(preview: EditPreviewData): Promise<boolean> {
    const stats = preview.changeStats;
    const message = `Apply changes to ${preview.filePath}?\n\n` +
      `+${stats.additions} additions, -${stats.deletions} deletions, ~${stats.modifications} modifications`;

    const result = await vscode.window.showWarningMessage(
      message,
      { modal: true },
      'Apply Changes',
      'Cancel'
    );

    return result === 'Apply Changes';
  }

  /**
   * Show confirmation dialog for batch edit operations
   */
  async showBatchEditConfirmation(batchPreview: BatchEditPreview): Promise<boolean> {
    const stats = batchPreview.totalStats;
    const message = `Apply changes to ${stats.filesChanged} files?\n\n` +
      `Total: +${stats.totalAdditions} additions, -${stats.totalDeletions} deletions, ~${stats.totalModifications} modifications`;

    const result = await vscode.window.showWarningMessage(
      message,
      { modal: true },
      'Apply All Changes',
      'Review Individual Files',
      'Cancel'
    );

    if (result === 'Apply All Changes') {
      return true;
    } else if (result === 'Review Individual Files') {
      return await this.showIndividualFileConfirmations(batchPreview);
    }

    return false;
  }

  /**
   * Show individual file confirmations for batch operations
   */
  private async showIndividualFileConfirmations(batchPreview: BatchEditPreview): Promise<boolean> {
    const confirmedEdits: EditPreviewData[] = [];

    for (const edit of batchPreview.edits) {
      const confirmed = await this.showEditConfirmation(edit);
      if (confirmed) {
        confirmedEdits.push(edit);
      }
    }

    if (confirmedEdits.length === 0) {
      vscode.window.showInformationMessage('No changes were applied.');
      return false;
    }

    if (confirmedEdits.length < batchPreview.edits.length) {
      const message = `Apply ${confirmedEdits.length} of ${batchPreview.edits.length} changes?`;
      const result = await vscode.window.showInformationMessage(
        message,
        'Apply Selected',
        'Cancel'
      );
      return result === 'Apply Selected';
    }

    return true;
  }

  /**
   * Format diff for display
   */
  formatDiffForDisplay(diffLines: DiffLine[]): string {
    let result = '';
    
    for (const line of diffLines) {
      const prefix = this.getDiffPrefix(line.type);
      const lineNumber = line.lineNumber > 0 ? line.lineNumber.toString().padStart(4) : '    ';
      result += `${lineNumber} ${prefix} ${line.content}\n`;
    }

    return result;
  }

  /**
   * Get prefix for diff line type
   */
  private getDiffPrefix(type: DiffLine['type']): string {
    switch (type) {
      case 'added': return '+';
      case 'removed': return '-';
      case 'modified': return '~';
      case 'unchanged': return ' ';
      default: return ' ';
    }
  }

  /**
   * Generate HTML preview for webview
   */
  generateHtmlPreview(preview: EditPreviewData): string {
    const diffHtml = preview.diffLines.map(line => {
      const cssClass = `diff-${line.type}`;
      const lineNumber = line.lineNumber > 0 ? line.lineNumber : '';
      const prefix = this.getDiffPrefix(line.type);
      
      return `<div class="${cssClass}">
        <span class="line-number">${lineNumber}</span>
        <span class="diff-prefix">${prefix}</span>
        <span class="line-content">${this.escapeHtml(line.content)}</span>
      </div>`;
    }).join('');

    return `
      <div class="edit-preview">
        <h3>Changes to ${preview.filePath}</h3>
        <div class="change-stats">
          <span class="additions">+${preview.changeStats.additions}</span>
          <span class="deletions">-${preview.changeStats.deletions}</span>
          <span class="modifications">~${preview.changeStats.modifications}</span>
        </div>
        <div class="diff-content">
          ${diffHtml}
        </div>
      </div>
    `;
  }

  /**
   * Escape HTML characters
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}
