<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'none'; img-src ${webview.cspSource} https:; script-src 'nonce-${nonce}' 'unsafe-inline'; style-src ${webview.cspSource} 'unsafe-inline'; connect-src ${webview.cspSource} https:; worker-src ${webview.cspSource} blob:;">
  <title>Vidyadhara Chat</title>
  <link rel="stylesheet" href="${styleUri}">
  <link rel="stylesheet" href="${jsonFormatStylesUri}">
  <script nonce="${nonce}" src="${configUri}"></script>
  <script nonce="${nonce}" src="${modeHandlerUri}"></script>
  <script nonce="${nonce}" src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script nonce="${nonce}" src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
</head>

<body>
  <div id="webview-marker" style="color:red;font-weight:bold;">Vidyadhara Webview Loaded</div>
  <div class="chat-container">
    <div class="toolbar">
      <div class="toolbar-left">
        <button id="new-chat-button" class="toolbar-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="12" y1="18" x2="12" y2="12"></line>
            <line x1="9" y1="15" x2="15" y2="15"></line>
          </svg>
          <span class="label">New Chat</span>
        </button>
        <button id="clear-button" class="toolbar-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
          </svg>
          <span class="label">Clear</span>
        </button>
      </div>
      <div class="toolbar-center">
        <div class="mode-selector">
          <div id="mode-controls" class="mode-controls">
            <!-- Mode buttons will be inserted here by JavaScript -->
            <div class="mode-loading">Loading modes...</div>
          </div>
          <div id="current-mode-display" class="current-mode" title="Current mode"></div>
        </div>
        <select id="model-selector" class="model-selector">
          <option value="anthropic/claude-3-opus-20240229">Claude 3 Opus</option>
          <option value="anthropic/claude-3-sonnet-20240229">Claude 3 Sonnet</option>
          <option value="anthropic/claude-3-haiku-20240307">Claude 3 Haiku</option>
          <option value="openai/gpt-4o">GPT-4o</option>
          <option value="openai/gpt-4-turbo">GPT-4 Turbo</option>
          <option value="google/gemini-1.5-pro">Gemini 1.5 Pro</option>
        </select>
      </div>
      <div class="toolbar-right">
        <button id="settings-button" class="toolbar-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path
              d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z">
            </path>
          </svg>
          <span class="label">Settings</span>
        </button>
      </div>
    </div>

    <div class="header">
      <span class="title">Vidyadhara AI Assistant</span>
      <span id="current-mode-label" class="mode-label">Chat</span>
    </div>

    <div class="chat-messages" id="chat-messages"></div>

    <div class="bottom-controls">
      <div class="input-container">
        <textarea id="message-input" placeholder="Ask Vidyadhara something..." rows="1"></textarea>
        <button id="send-button" class="send-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="22" y1="2" x2="11" y2="13"></line>
            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
          </svg>
        </button>
      </div>
      <div class="input-controls">
        <button id="stop-button" class="control-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          </svg>
          Stop Generating
        </button>
      </div>
    </div>
  </div>

  <div id="settings-panel" class="settings-panel hidden">
    <div class="settings-header">
      <h2>Settings</h2>
      <button id="close-settings-button" class="close-button">×</button>
    </div>
    <div class="settings-content">
      <div class="settings-group">
        <h3>API Keys</h3>
        <div class="settings-item">
          <label for="openrouter-api-key">OpenRouter API Key</label>
          <input type="password" id="openrouter-api-key" placeholder="Enter your OpenRouter API key">
          <p class="settings-description">Get your API key from <a href="https://openrouter.ai/keys"
              target="_blank">OpenRouter</a></p>
        </div>
      </div>
      
      <div class="settings-group">
        <h3>GitHub Integration</h3>
        <div class="settings-item">
          <label for="github-token">GitHub Personal Access Token</label>
          <div class="input-with-icon">
            <input type="password" id="github-token" placeholder="Enter your GitHub token">
            <button type="button" id="toggle-token-visibility" class="toggle-visibility" aria-label="Show token">
              <svg viewBox="0 0 24 24" width="16" height="16">
                <path d="M12 9a3 3 0 0 1 3 3 3 3 0 0 1-3 3 3 3 0 0 1-3-3 3 3 0 0 1 3-3m0-4.5c5 0 9.27 3.11 11 7.5-1.73 4.39-6 7.5-11 7.5S2.73 16.39 1 12c1.73-4.39 6-7.5 11-7.5M3.18 12a9.82 9.82 0 0 0 17.64 0 9.82 9.82 0 0 0-17.64 0z"/>
              </svg>
            </button>
          </div>
          <div id="github-connection-status" class="github-status">Not connected</div>
          <p class="settings-description">
            <a href="https://github.com/settings/tokens/new?scopes=repo&description=Vidyadhara%20Extension" target="_blank">
              Create a token with repo scope
            </a>
          </p>
        </div>
        <div class="settings-item">
          <label for="github-owner">GitHub Username/Organization</label>
          <input type="text" id="github-owner" placeholder="Your GitHub username or org name">
        </div>
        <div class="settings-item">
          <label for="github-repo">Repository Name</label>
          <input type="text" id="github-repo" placeholder="repository-name">
        </div>
        <div class="settings-item">
          <label for="github-branch">Branch (optional)</label>
          <input type="text" id="github-branch" placeholder="main" value="main">
          <p class="settings-description">Leave as 'main' if unsure</p>
        </div>
      </div>
      <div class="settings-group">
        <h3>Model Settings</h3>
        <div class="settings-item">
          <label for="default-model">Default Model</label>
          <select id="default-model">
            <option value="anthropic/claude-3-opus-20240229">Claude 3 Opus</option>
            <option value="anthropic/claude-3-sonnet-20240229">Claude 3 Sonnet</option>
            <option value="anthropic/claude-3-haiku-20240307">Claude 3 Haiku</option>
            <option value="openai/gpt-4o">GPT-4o</option>
            <option value="openai/gpt-4-turbo">GPT-4 Turbo</option>
            <option value="google/gemini-1.5-pro">Gemini 1.5 Pro</option>
          </select>
          <p class="settings-description">Select your preferred AI model</p>
        </div>
        <div class="settings-item">
          <label for="max-context-length">Max Context Length</label>
          <input type="number" id="max-context-length" value="100000" min="1000" max="200000">
          <p class="settings-description">Maximum number of tokens to include in context</p>
        </div>
      </div>
      <div class="settings-group">
        <h3>Appearance</h3>
        <div class="settings-item">
          <label for="theme-selector">Theme</label>
          <select id="theme-selector">
            <option value="system">System Default</option>
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
          <p class="settings-description">Choose your preferred theme</p>
        </div>
      </div>
      <div class="settings-actions">
        <button id="save-settings-button" class="settings-button primary">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
            <polyline points="17 21 17 13 7 13 7 21"></polyline>
            <polyline points="7 3 7 8 15 8"></polyline>
          </svg>
          Save All Settings
        </button>
      </div>
    </div>
  </div>

  <template id="user-message-template">
    <div class="message user-message">
      <div class="message-content"></div>
    </div>
  </template>

  <template id="assistant-message-template">
    <div class="message assistant-message">
      <div class="message-content"></div>
      <div class="message-actions">
        <button class="copy-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
          Copy
        </button>
      </div>
    </div>
  </template>

  <template id="code-block-template">
    <div class="code-block">
      <div class="code-header">
        <span class="code-language"></span>
        <button class="copy-code-button">Copy</button>
      </div>
      <pre><code></code></pre>
    </div>
  </template>

  <template id="action-block-template">
    <div class="action-block">
      <div class="action-header">
        <span class="action-title"></span>
      </div>
      <div class="action-content"></div>
      <div class="action-buttons"></div>
    </div>
  </template>

  <script nonce="${nonce}">
    window.modeControlsUri = "${modeControlsUri}";
  </script>
  <script nonce="${nonce}" src="${formatMessageUri}"></script>
  <script nonce="${nonce}" src="${formatJsonResponseUri}"></script>
  <script nonce="${nonce}" src="${actionBlockFormatterUri}"></script>
  <script nonce="${nonce}" src="${scriptUri}"></script>
</body>

</html>
