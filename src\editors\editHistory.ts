import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { EditOperation } from './stringReplaceEditor';

const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);

export interface HistoryEntry {
  id: string;
  operation: EditOperation;
  timestamp: Date;
  description: string;
  beforeContent: string;
  afterContent: string;
  canUndo: boolean;
}

export interface UndoRedoStack {
  undoStack: HistoryEntry[];
  redoStack: HistoryEntry[];
  maxStackSize: number;
}

export class EditHistory {
  private history: Map<string, HistoryEntry[]> = new Map(); // filePath -> history entries
  private undoRedoStacks: Map<string, UndoRedoStack> = new Map(); // filePath -> undo/redo stacks
  private readonly maxHistorySize = 100;
  private readonly maxStackSize = 50;
  private context: vscode.ExtensionContext;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
    this.loadPersistedHistory();
  }

  /**
   * Record an edit operation in history
   */
  async recordEdit(
    operation: EditOperation,
    description: string,
    beforeContent: string,
    afterContent: string
  ): Promise<string> {
    const id = this.generateId();
    const entry: HistoryEntry = {
      id,
      operation,
      timestamp: new Date(),
      description,
      beforeContent,
      afterContent,
      canUndo: true
    };

    // Add to file history
    const fileHistory = this.history.get(operation.filePath) || [];
    fileHistory.push(entry);
    
    // Limit history size
    if (fileHistory.length > this.maxHistorySize) {
      fileHistory.shift();
    }
    
    this.history.set(operation.filePath, fileHistory);

    // Add to undo stack
    this.addToUndoStack(operation.filePath, entry);

    // Persist history
    await this.persistHistory();

    return id;
  }

  /**
   * Undo the last edit operation for a file
   */
  async undoLastEdit(filePath: string): Promise<boolean> {
    const stack = this.undoRedoStacks.get(filePath);
    if (!stack || stack.undoStack.length === 0) {
      vscode.window.showInformationMessage('No operations to undo');
      return false;
    }

    const entry = stack.undoStack.pop()!;
    
    try {
      // Restore the before content
      const fullPath = this.resolveFilePath(filePath);
      await writeFile(fullPath, entry.beforeContent, 'utf8');

      // Move to redo stack
      stack.redoStack.push(entry);
      if (stack.redoStack.length > this.maxStackSize) {
        stack.redoStack.shift();
      }

      // Update VS Code editor
      const document = await vscode.workspace.openTextDocument(fullPath);
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        document.positionAt(0),
        document.positionAt(document.getText().length)
      );
      edit.replace(document.uri, fullRange, entry.beforeContent);
      await vscode.workspace.applyEdit(edit);

      vscode.window.showInformationMessage(`Undone: ${entry.description}`);
      return true;
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to undo: ${error instanceof Error ? error.message : String(error)}`);
      // Put the entry back on the undo stack
      stack.undoStack.push(entry);
      return false;
    }
  }

  /**
   * Redo the last undone edit operation for a file
   */
  async redoLastEdit(filePath: string): Promise<boolean> {
    const stack = this.undoRedoStacks.get(filePath);
    if (!stack || stack.redoStack.length === 0) {
      vscode.window.showInformationMessage('No operations to redo');
      return false;
    }

    const entry = stack.redoStack.pop()!;
    
    try {
      // Restore the after content
      const fullPath = this.resolveFilePath(filePath);
      await writeFile(fullPath, entry.afterContent, 'utf8');

      // Move back to undo stack
      stack.undoStack.push(entry);

      // Update VS Code editor
      const document = await vscode.workspace.openTextDocument(fullPath);
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        document.positionAt(0),
        document.positionAt(document.getText().length)
      );
      edit.replace(document.uri, fullRange, entry.afterContent);
      await vscode.workspace.applyEdit(edit);

      vscode.window.showInformationMessage(`Redone: ${entry.description}`);
      return true;
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to redo: ${error instanceof Error ? error.message : String(error)}`);
      // Put the entry back on the redo stack
      stack.redoStack.push(entry);
      return false;
    }
  }

  /**
   * Rollback to a specific history entry
   */
  async rollbackToEntry(filePath: string, entryId: string): Promise<boolean> {
    const fileHistory = this.history.get(filePath);
    if (!fileHistory) {
      vscode.window.showErrorMessage('No history found for file');
      return false;
    }

    const entry = fileHistory.find(e => e.id === entryId);
    if (!entry) {
      vscode.window.showErrorMessage('History entry not found');
      return false;
    }

    try {
      // Restore to the before content of the specified entry
      const fullPath = this.resolveFilePath(filePath);
      await writeFile(fullPath, entry.beforeContent, 'utf8');

      // Update VS Code editor
      const document = await vscode.workspace.openTextDocument(fullPath);
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        document.positionAt(0),
        document.positionAt(document.getText().length)
      );
      edit.replace(document.uri, fullRange, entry.beforeContent);
      await vscode.workspace.applyEdit(edit);

      vscode.window.showInformationMessage(`Rolled back to: ${entry.description}`);
      return true;
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to rollback: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Get history for a specific file
   */
  getFileHistory(filePath: string): HistoryEntry[] {
    return this.history.get(filePath) || [];
  }

  /**
   * Get all files with history
   */
  getFilesWithHistory(): string[] {
    return Array.from(this.history.keys());
  }

  /**
   * Clear history for a specific file
   */
  clearFileHistory(filePath: string): void {
    this.history.delete(filePath);
    this.undoRedoStacks.delete(filePath);
    this.persistHistory();
  }

  /**
   * Clear all history
   */
  clearAllHistory(): void {
    this.history.clear();
    this.undoRedoStacks.clear();
    this.persistHistory();
  }

  /**
   * Check if undo is available for a file
   */
  canUndo(filePath: string): boolean {
    const stack = this.undoRedoStacks.get(filePath);
    return stack ? stack.undoStack.length > 0 : false;
  }

  /**
   * Check if redo is available for a file
   */
  canRedo(filePath: string): boolean {
    const stack = this.undoRedoStacks.get(filePath);
    return stack ? stack.redoStack.length > 0 : false;
  }

  /**
   * Get undo/redo stack info for a file
   */
  getStackInfo(filePath: string): { undoCount: number; redoCount: number } {
    const stack = this.undoRedoStacks.get(filePath);
    return {
      undoCount: stack ? stack.undoStack.length : 0,
      redoCount: stack ? stack.redoStack.length : 0
    };
  }

  /**
   * Add entry to undo stack
   */
  private addToUndoStack(filePath: string, entry: HistoryEntry): void {
    let stack = this.undoRedoStacks.get(filePath);
    if (!stack) {
      stack = {
        undoStack: [],
        redoStack: [],
        maxStackSize: this.maxStackSize
      };
      this.undoRedoStacks.set(filePath, stack);
    }

    stack.undoStack.push(entry);
    
    // Clear redo stack when new operation is performed
    stack.redoStack = [];

    // Limit stack size
    if (stack.undoStack.length > this.maxStackSize) {
      stack.undoStack.shift();
    }
  }

  /**
   * Generate unique ID for history entry
   */
  private generateId(): string {
    return `edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Resolve file path
   */
  private resolveFilePath(filePath: string): string {
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    return path.join(workspaceRoot, filePath);
  }

  /**
   * Persist history to VS Code storage
   */
  private async persistHistory(): Promise<void> {
    try {
      const historyData = {
        history: Array.from(this.history.entries()),
        undoRedoStacks: Array.from(this.undoRedoStacks.entries())
      };
      
      await this.context.globalState.update('editHistory', JSON.stringify(historyData));
    } catch (error) {
      console.error('Failed to persist edit history:', error);
    }
  }

  /**
   * Load persisted history from VS Code storage
   */
  private async loadPersistedHistory(): Promise<void> {
    try {
      const historyJson = this.context.globalState.get<string>('editHistory');
      if (historyJson) {
        const historyData = JSON.parse(historyJson);
        
        // Restore history
        this.history = new Map(historyData.history || []);
        
        // Restore undo/redo stacks
        this.undoRedoStacks = new Map(historyData.undoRedoStacks || []);
        
        // Convert timestamp strings back to Date objects
        for (const [, entries] of this.history) {
          for (const entry of entries) {
            entry.timestamp = new Date(entry.timestamp);
            entry.operation.timestamp = new Date(entry.operation.timestamp);
          }
        }
        
        for (const [, stack] of this.undoRedoStacks) {
          for (const entry of [...stack.undoStack, ...stack.redoStack]) {
            entry.timestamp = new Date(entry.timestamp);
            entry.operation.timestamp = new Date(entry.operation.timestamp);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load persisted edit history:', error);
      // Reset to empty state on error
      this.history.clear();
      this.undoRedoStacks.clear();
    }
  }
}
