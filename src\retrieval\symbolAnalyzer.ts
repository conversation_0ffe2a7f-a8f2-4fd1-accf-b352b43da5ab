import * as vscode from 'vscode';
import * as path from 'path';
import { CodeSymbol } from './codebaseRetrieval';

// Re-export CodeSymbol for other modules
export type { CodeSymbol };

export interface SymbolRelationship {
  fromSymbol: CodeSymbol;
  toSymbol: CodeSymbol;
  type: 'extends' | 'implements' | 'calls' | 'uses' | 'imports' | 'references';
  filePath: string;
  line: number;
  context: string;
}

export interface SymbolUsage {
  symbol: CodeSymbol;
  usageType: 'definition' | 'call' | 'reference' | 'assignment' | 'parameter';
  filePath: string;
  line: number;
  column: number;
  context: string;
}

export interface SymbolAnalysis {
  symbol: CodeSymbol;
  usages: SymbolUsage[];
  relationships: SymbolRelationship[];
  dependencies: CodeSymbol[];
  dependents: CodeSymbol[];
  complexity: number;
  documentation: string;
  examples: string[];
}

export class SymbolAnalyzer {
  private symbolCache: Map<string, SymbolAnalysis> = new Map();
  private relationshipCache: Map<string, SymbolRelationship[]> = new Map();

  /**
   * Analyze a symbol and its relationships
   */
  async analyzeSymbol(symbol: CodeSymbol, codebase: Map<string, string>): Promise<SymbolAnalysis> {
    const cacheKey = `${symbol.filePath}:${symbol.name}:${symbol.line}`;
    
    if (this.symbolCache.has(cacheKey)) {
      return this.symbolCache.get(cacheKey)!;
    }

    const analysis: SymbolAnalysis = {
      symbol,
      usages: await this.findSymbolUsages(symbol, codebase),
      relationships: await this.findSymbolRelationships(symbol, codebase),
      dependencies: [],
      dependents: [],
      complexity: this.calculateComplexity(symbol, codebase),
      documentation: this.extractDocumentation(symbol, codebase),
      examples: await this.findUsageExamples(symbol, codebase)
    };

    // Build dependency graph
    analysis.dependencies = this.extractDependencies(analysis.relationships);
    analysis.dependents = await this.findDependents(symbol, codebase);

    this.symbolCache.set(cacheKey, analysis);
    return analysis;
  }

  /**
   * Find all usages of a symbol across the codebase
   */
  async findSymbolUsages(symbol: CodeSymbol, codebase: Map<string, string>): Promise<SymbolUsage[]> {
    const usages: SymbolUsage[] = [];
    const symbolName = symbol.name;

    for (const [filePath, content] of codebase) {
      const lines = content.split('\n');
      
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex];
        const usageMatches = this.findSymbolInLine(line, symbolName, lineIndex + 1);
        
        for (const match of usageMatches) {
          const usage: SymbolUsage = {
            symbol,
            usageType: this.determineUsageType(line, match.column, symbol),
            filePath,
            line: lineIndex + 1,
            column: match.column,
            context: this.extractUsageContext(lines, lineIndex)
          };
          
          usages.push(usage);
        }
      }
    }

    return usages;
  }

  /**
   * Find relationships between symbols
   */
  async findSymbolRelationships(symbol: CodeSymbol, codebase: Map<string, string>): Promise<SymbolRelationship[]> {
    const relationships: SymbolRelationship[] = [];
    const content = codebase.get(symbol.filePath);
    
    if (!content) {
      return relationships;
    }

    const lines = content.split('\n');
    const symbolLine = lines[symbol.line - 1];

    // Find inheritance relationships
    relationships.push(...this.findInheritanceRelationships(symbol, symbolLine, codebase));
    
    // Find method calls and references
    relationships.push(...this.findCallRelationships(symbol, lines, codebase));
    
    // Find import relationships
    relationships.push(...this.findImportRelationships(symbol, lines, codebase));

    return relationships;
  }

  /**
   * Find inheritance relationships (extends, implements)
   */
  private findInheritanceRelationships(
    symbol: CodeSymbol,
    symbolLine: string,
    codebase: Map<string, string>
  ): SymbolRelationship[] {
    const relationships: SymbolRelationship[] = [];

    // Find extends relationships
    const extendsMatch = symbolLine.match(/extends\s+(\w+)/);
    if (extendsMatch) {
      const parentClass = this.findSymbolByName(extendsMatch[1], codebase);
      if (parentClass) {
        relationships.push({
          fromSymbol: symbol,
          toSymbol: parentClass,
          type: 'extends',
          filePath: symbol.filePath,
          line: symbol.line,
          context: symbolLine.trim()
        });
      }
    }

    // Find implements relationships
    const implementsMatch = symbolLine.match(/implements\s+([\w,\s]+)/);
    if (implementsMatch) {
      const interfaces = implementsMatch[1].split(',').map(i => i.trim());
      
      for (const interfaceName of interfaces) {
        const interfaceSymbol = this.findSymbolByName(interfaceName, codebase);
        if (interfaceSymbol) {
          relationships.push({
            fromSymbol: symbol,
            toSymbol: interfaceSymbol,
            type: 'implements',
            filePath: symbol.filePath,
            line: symbol.line,
            context: symbolLine.trim()
          });
        }
      }
    }

    return relationships;
  }

  /**
   * Find method call and reference relationships
   */
  private findCallRelationships(
    symbol: CodeSymbol,
    lines: string[],
    codebase: Map<string, string>
  ): SymbolRelationship[] {
    const relationships: SymbolRelationship[] = [];

    // For functions, find what they call
    if (symbol.type === 'function') {
      const functionBody = this.extractFunctionBody(lines, symbol.line - 1);
      const calledFunctions = this.extractFunctionCalls(functionBody);
      
      for (const calledFunction of calledFunctions) {
        const targetSymbol = this.findSymbolByName(calledFunction.name, codebase);
        if (targetSymbol) {
          relationships.push({
            fromSymbol: symbol,
            toSymbol: targetSymbol,
            type: 'calls',
            filePath: symbol.filePath,
            line: calledFunction.line,
            context: calledFunction.context
          });
        }
      }
    }

    return relationships;
  }

  /**
   * Find import relationships
   */
  private findImportRelationships(
    symbol: CodeSymbol,
    lines: string[],
    codebase: Map<string, string>
  ): SymbolRelationship[] {
    const relationships: SymbolRelationship[] = [];

    // Find import statements that might relate to this symbol
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.trim().startsWith('import')) {
        const importedSymbols = this.extractImportedSymbols(line);
        
        for (const importedSymbol of importedSymbols) {
          if (importedSymbol === symbol.name) {
            // This symbol is imported, find its definition
            const definitionSymbol = this.findSymbolDefinition(importedSymbol, line, codebase);
            if (definitionSymbol) {
              relationships.push({
                fromSymbol: symbol,
                toSymbol: definitionSymbol,
                type: 'imports',
                filePath: symbol.filePath,
                line: i + 1,
                context: line.trim()
              });
            }
          }
        }
      }
    }

    return relationships;
  }

  /**
   * Calculate symbol complexity
   */
  private calculateComplexity(symbol: CodeSymbol, codebase: Map<string, string>): number {
    const content = codebase.get(symbol.filePath);
    if (!content) return 0;

    const lines = content.split('\n');
    let complexity = 1; // Base complexity

    if (symbol.type === 'function') {
      const functionBody = this.extractFunctionBody(lines, symbol.line - 1);
      
      // Count decision points
      const decisionPoints = [
        /if\s*\(/g,
        /else\s+if\s*\(/g,
        /while\s*\(/g,
        /for\s*\(/g,
        /switch\s*\(/g,
        /case\s+/g,
        /catch\s*\(/g,
        /&&/g,
        /\|\|/g
      ];

      for (const pattern of decisionPoints) {
        const matches = functionBody.match(pattern);
        if (matches) {
          complexity += matches.length;
        }
      }
    } else if (symbol.type === 'class') {
      // For classes, complexity is based on number of methods and properties
      const classBody = this.extractClassBody(lines, symbol.line - 1);
      const methods = classBody.match(/\w+\s*\(/g) || [];
      const properties = classBody.match(/\w+\s*[:=]/g) || [];
      
      complexity = methods.length + properties.length;
    }

    return complexity;
  }

  /**
   * Extract comprehensive documentation for a symbol
   */
  private extractDocumentation(symbol: CodeSymbol, codebase: Map<string, string>): string {
    const content = codebase.get(symbol.filePath);
    if (!content) return '';

    const lines = content.split('\n');
    const docLines: string[] = [];

    // Look for documentation above the symbol
    for (let i = symbol.line - 2; i >= 0; i--) {
      const line = lines[i].trim();
      
      if (line.startsWith('/**') || line.startsWith('/*') || line.startsWith('*') || line.startsWith('//')) {
        docLines.unshift(line);
      } else if (line === '') {
        continue;
      } else {
        break;
      }
    }

    // Clean up documentation
    return docLines
      .map(line => line.replace(/^\/\*\*?|\*\/|^\s*\*\s?|^\/\/\s?/g, ''))
      .filter(line => line.trim() !== '')
      .join('\n');
  }

  /**
   * Find usage examples for a symbol
   */
  private async findUsageExamples(symbol: CodeSymbol, codebase: Map<string, string>): Promise<string[]> {
    const examples: string[] = [];
    const usages = await this.findSymbolUsages(symbol, codebase);

    // Get diverse usage examples
    const exampleUsages = usages
      .filter(usage => usage.usageType === 'call' || usage.usageType === 'reference')
      .slice(0, 5); // Limit to 5 examples

    for (const usage of exampleUsages) {
      const content = codebase.get(usage.filePath);
      if (content) {
        const lines = content.split('\n');
        const context = this.extractUsageContext(lines, usage.line - 1, 3);
        examples.push(context);
      }
    }

    return examples;
  }

  /**
   * Find symbols that depend on this symbol
   */
  private async findDependents(symbol: CodeSymbol, codebase: Map<string, string>): Promise<CodeSymbol[]> {
    const dependents: CodeSymbol[] = [];
    
    // This would be implemented by searching for all symbols that reference this symbol
    // For now, return empty array as placeholder
    
    return dependents;
  }

  /**
   * Utility methods
   */
  private findSymbolInLine(line: string, symbolName: string, lineNumber: number): Array<{ column: number }> {
    const matches: Array<{ column: number }> = [];
    let index = 0;

    while (true) {
      index = line.indexOf(symbolName, index);
      if (index === -1) break;

      // Check if it's a whole word match
      const before = index > 0 ? line[index - 1] : ' ';
      const after = index + symbolName.length < line.length ? line[index + symbolName.length] : ' ';
      
      if (!/\w/.test(before) && !/\w/.test(after)) {
        matches.push({ column: index });
      }
      
      index++;
    }

    return matches;
  }

  private determineUsageType(line: string, column: number, symbol: CodeSymbol): SymbolUsage['usageType'] {
    const beforeSymbol = line.substring(0, column).trim();
    const afterSymbol = line.substring(column + symbol.name.length).trim();

    // Check if it's a function call
    if (afterSymbol.startsWith('(')) {
      return 'call';
    }

    // Check if it's an assignment
    if (beforeSymbol.endsWith('=') || beforeSymbol.includes('let ') || beforeSymbol.includes('const ') || beforeSymbol.includes('var ')) {
      return 'assignment';
    }

    // Check if it's a parameter
    if (line.includes('function') && line.includes('(') && line.includes(')')) {
      const funcStart = line.indexOf('(');
      const funcEnd = line.indexOf(')', funcStart);
      if (column > funcStart && column < funcEnd) {
        return 'parameter';
      }
    }

    // Check if it's a definition
    if (line.includes('class ') || line.includes('function ') || line.includes('interface ')) {
      return 'definition';
    }

    return 'reference';
  }

  private extractUsageContext(lines: string[], lineIndex: number, contextSize: number = 2): string {
    const start = Math.max(0, lineIndex - contextSize);
    const end = Math.min(lines.length, lineIndex + contextSize + 1);
    
    return lines.slice(start, end).join('\n');
  }

  private findSymbolByName(name: string, codebase: Map<string, string>): CodeSymbol | null {
    // This would search through all symbols to find one with the given name
    // For now, return null as placeholder
    return null;
  }

  private extractFunctionBody(lines: string[], startLine: number): string {
    let braceCount = 0;
    let inFunction = false;
    const bodyLines: string[] = [];

    for (let i = startLine; i < lines.length; i++) {
      const line = lines[i];
      
      if (!inFunction && line.includes('{')) {
        inFunction = true;
        braceCount = 1;
        bodyLines.push(line);
        continue;
      }

      if (inFunction) {
        bodyLines.push(line);
        
        for (const char of line) {
          if (char === '{') braceCount++;
          if (char === '}') braceCount--;
        }

        if (braceCount === 0) {
          break;
        }
      }
    }

    return bodyLines.join('\n');
  }

  private extractClassBody(lines: string[], startLine: number): string {
    // Similar to extractFunctionBody but for classes
    return this.extractFunctionBody(lines, startLine);
  }

  private extractFunctionCalls(code: string): Array<{ name: string; line: number; context: string }> {
    const calls: Array<{ name: string; line: number; context: string }> = [];
    const lines = code.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const callMatches = line.match(/(\w+)\s*\(/g);
      
      if (callMatches) {
        for (const match of callMatches) {
          const functionName = match.replace(/\s*\($/, '');
          calls.push({
            name: functionName,
            line: i + 1,
            context: line.trim()
          });
        }
      }
    }

    return calls;
  }

  private extractImportedSymbols(importLine: string): string[] {
    const symbols: string[] = [];
    
    // Handle different import patterns
    const patterns = [
      /import\s+(\w+)/g,                    // import Symbol
      /import\s*{\s*([^}]+)\s*}/g,          // import { Symbol1, Symbol2 }
      /import\s+\*\s+as\s+(\w+)/g,          // import * as Symbol
    ];

    for (const pattern of patterns) {
      const matches = importLine.matchAll(pattern);
      for (const match of matches) {
        if (match[1].includes(',')) {
          // Multiple imports
          symbols.push(...match[1].split(',').map(s => s.trim()));
        } else {
          symbols.push(match[1]);
        }
      }
    }

    return symbols;
  }

  private findSymbolDefinition(symbolName: string, importLine: string, codebase: Map<string, string>): CodeSymbol | null {
    // Extract the module path from import statement
    const moduleMatch = importLine.match(/from\s+['"]([^'"]+)['"]/);
    if (!moduleMatch) return null;

    const modulePath = moduleMatch[1];
    
    // This would resolve the module path and find the symbol definition
    // For now, return null as placeholder
    return null;
  }

  private extractDependencies(relationships: SymbolRelationship[]): CodeSymbol[] {
    return relationships
      .filter(rel => rel.type === 'calls' || rel.type === 'uses' || rel.type === 'extends' || rel.type === 'implements')
      .map(rel => rel.toSymbol);
  }

  /**
   * Get symbol hierarchy (inheritance chain)
   */
  async getSymbolHierarchy(symbol: CodeSymbol, codebase: Map<string, string>): Promise<CodeSymbol[]> {
    const hierarchy: CodeSymbol[] = [symbol];
    const analysis = await this.analyzeSymbol(symbol, codebase);

    // Find parent classes/interfaces
    const parentRelationships = analysis.relationships.filter(
      rel => rel.type === 'extends' || rel.type === 'implements'
    );

    for (const rel of parentRelationships) {
      const parentHierarchy = await this.getSymbolHierarchy(rel.toSymbol, codebase);
      hierarchy.push(...parentHierarchy);
    }

    return hierarchy;
  }

  /**
   * Clear caches
   */
  clearCache(): void {
    this.symbolCache.clear();
    this.relationshipCache.clear();
  }
}
