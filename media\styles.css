:root {
  /* Base colors */
  --background-color: #ffffff;
  --foreground-color: #333333;
  --primary-color: #0078d4;
  --secondary-color: #005a9e;
  --accent-color: #0078d4;

  /* UI element colors */
  --border-color: #e0e0e0;
  --hover-color: #f5f5f5;
  --active-color: #e0f0ff;
  --message-bg-user: #e9f2ff;
  --message-bg-assistant: #ffffff;
  --message-border-user: #c7e0ff;
  --message-border-assistant: #e0e0e0;

  /* Code colors */
  --code-bg-color: #f8f8f8;
  --code-border-color: #e0e0e0;

  /* Action block colors */
  --action-block-bg: #f0f7ff;
  --action-block-border: #cce5ff;

  /* Status colors */
  --error-color: #d83b01;
  --success-color: #107c10;
  --warning-color: #f2c811;
  --info-color: #0078d4;

  /* Scrollbar colors */
  --scrollbar-thumb: #c1c1c1;
  --scrollbar-track: #f1f1f1;

  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --code-font-family: 'Cascadia Code', 'Menlo', 'Monaco', 'Courier New', monospace;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Borders */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05);

  /* Transitions */
  --transition-fast: 0.1s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
}

.vscode-dark {
  --background-color: #1e1e1e;
  --foreground-color: #e0e0e0;
  --primary-color: #0078d4;
  --secondary-color: #2b88d8;
  --accent-color: #0078d4;

  --border-color: #3c3c3c;
  --hover-color: #2a2a2a;
  --active-color: #094771;
  --message-bg-user: #094771;
  --message-bg-assistant: #1e1e1e;
  --message-border-user: #0078d4;
  --message-border-assistant: #3c3c3c;

  --code-bg-color: #252526;
  --code-border-color: #3c3c3c;

  --action-block-bg: #063b59;
  --action-block-border: #0e639c;

  --scrollbar-thumb: #5a5a5a;
  --scrollbar-track: #383838;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--foreground-color);
  line-height: 1.5;
  font-size: 14px;
  overflow: hidden;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: #a0a0a0;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
}

/* Toolbar */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-color);
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: var(--foreground-color);
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  transition: background-color var(--transition-fast);
}

.toolbar-button:hover {
  background-color: var(--hover-color);
}

.toolbar-button:active {
  background-color: var(--active-color);
}

.toolbar-button .icon {
  margin-right: var(--spacing-xs);
  font-size: 14px;
}

.model-selector {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  background-color: var(--background-color);
  color: var(--foreground-color);
  font-size: 13px;
  min-width: 200px;
  transition: border-color var(--transition-fast);
  outline: none;
}

.model-selector:focus {
  border-color: var(--primary-color);
}

/* Chat messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.message {
  position: relative;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  max-width: 85%;
  overflow-wrap: break-word;
  box-shadow: var(--shadow-sm);
  animation: message-fade-in var(--transition-normal);
}

@keyframes message-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  background-color: var(--message-bg-user);
  border: 1px solid var(--message-border-user);
  align-self: flex-end;
  margin-left: auto;
  border-bottom-right-radius: var(--spacing-xs);
}

.assistant-message {
  background-color: var(--message-bg-assistant);
  border: 1px solid var(--message-border-assistant);
  align-self: flex-start;
  margin-right: auto;
  border-bottom-left-radius: var(--spacing-xs);
}

.message-timestamp {
  position: absolute;
  bottom: -18px;
  font-size: 10px;
  color: #888;
  white-space: nowrap;
}

.user-message .message-timestamp {
  right: 0;
}

.assistant-message .message-timestamp {
  left: 0;
}

.message-content {
  white-space: pre-wrap;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-sm);
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.message:hover .message-actions {
  opacity: 1;
}

.copy-button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: 12px;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-fast);
}

.copy-button:hover {
  background-color: var(--hover-color);
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  margin-top: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: inline-block;
  opacity: 0.6;
}

.typing-indicator span:nth-child(1) {
  animation: typing 1s infinite 0s;
}

.typing-indicator span:nth-child(2) {
  animation: typing 1s infinite 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation: typing 1s infinite 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-5px);
  }

  100% {
    transform: translateY(0);
  }
}

/* Bottom controls */
.bottom-controls {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background-color: var(--background-color);
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.input-container {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.input-container:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.input-container textarea {
  flex: 1;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  resize: none;
  background-color: var(--background-color);
  color: var(--foreground-color);
  font-family: var(--font-family);
  font-size: 14px;
  max-height: 200px;
  outline: none;
}

.send-button {
  background-color: var(--primary-color);
  border: none;
  color: white;
  cursor: pointer;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--transition-fast);
}

.send-button:hover {
  background-color: var(--secondary-color);
}

.send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.input-controls {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-sm);
}

.control-button {
  background: none;
  border: none;
  color: var(--foreground-color);
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-fast);
}

.control-button:hover {
  background-color: var(--hover-color);
}

#stop-button {
  display: none;
  color: var(--error-color);
}

#stop-button.visible {
  display: block;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

/* Code blocks */
.code-block {
  margin: var(--spacing-sm) 0;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  border: 1px solid var(--code-border-color);
  box-shadow: var(--shadow-sm);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--code-border-color);
  font-size: 12px;
}

.code-language {
  font-weight: bold;
}

.copy-code-button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: 12px;
  opacity: 0.8;
  transition: opacity var(--transition-fast);
}

.copy-code-button:hover {
  opacity: 1;
}

.code-block pre {
  margin: 0;
  padding: var(--spacing-sm);
  overflow-x: auto;
  background-color: var(--code-bg-color);
}

.code-block code {
  font-family: var(--code-font-family);
  font-size: 13px;
  white-space: pre;
}

/* Action blocks */
.action-block {
  margin: var(--spacing-sm) 0;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  border: 1px solid var(--action-block-border);
  background-color: var(--action-block-bg);
  box-shadow: var(--shadow-sm);
}

.action-header {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--action-block-border);
  font-weight: bold;
}

.action-content {
  padding: var(--spacing-sm);
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-top: 1px solid var(--action-block-border);
  gap: var(--spacing-xs);
}

.action-button {
  background-color: var(--primary-color);
  border: none;
  color: white;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  margin-left: var(--spacing-xs);
  font-size: 12px;
  transition: background-color var(--transition-fast);
}

.action-button:hover {
  background-color: var(--secondary-color);
}

.action-button.secondary {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.action-button.secondary:hover {
  background-color: rgba(0, 120, 212, 0.1);
}

/* Settings panel */
.settings-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 400px;
  background-color: var(--background-color);
  border-left: 1px solid var(--border-color);
  z-index: 1000;
  overflow-y: auto;
  transition: transform var(--transition-slow);
  box-shadow: var(--shadow-lg);
}

.settings-panel.hidden {
  transform: translateX(100%);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  font-size: 18px;
  font-weight: 500;
}

.close-button {
  background: none;
  border: none;
  color: var(--foreground-color);
  cursor: pointer;
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color var(--transition-fast);
}

.close-button:hover {
  background-color: var(--hover-color);
}

.settings-content {
  padding: var(--spacing-md);
}

.settings-group {
  margin-bottom: var(--spacing-lg);
}

.settings-group h3 {
  margin-bottom: var(--spacing-sm);
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-group h3 svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

/* GitHub integration specific styles */
.github-token-container {
  position: relative;
  display: flex;
  gap: 8px;
}

.toggle-password {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--foreground-color);
  opacity: 0.6;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.toggle-password:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

.toggle-password:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.github-status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  background-color: #e8f5e9;
  color: #2e7d32;
  margin-top: 4px;
}

.github-status.connected::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #2e7d32;
}

.github-status.disconnected {
  background-color: #ffebee;
  color: #c62828;
}

.github-status.disconnected::before {
  background-color: #c62828;
}

.repository-info {
  margin-top: 8px;
  padding: 8px;
  background-color: var(--code-bg-color);
  border-radius: 4px;
  font-size: 12px;
  border-left: 2px solid var(--primary-color);
}

@media (max-width: 480px) {
  .github-token-container {
    flex-direction: column;
  }
  
  .github-token-container input {
    width: 100%;
  }
}

.settings-item {
  margin-bottom: var(--spacing-md);
  position: relative;
}

.settings-item label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: 13px;
  font-weight: 500;
}

.settings-description {
  margin-top: var(--spacing-xs);
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.vscode-dark .settings-description {
  color: #aaa;
}

.settings-description a {
  color: var(--primary-color);
  text-decoration: none;
}

.settings-description a:hover {
  text-decoration: underline;
}

.settings-item input,
.settings-item select,
.settings-item textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-primary);
}

.settings-item input:focus,
.settings-item select:focus,
.settings-item textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.settings-button {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  transition: background-color var(--transition-fast);
}

.settings-button:hover {
  background-color: rgba(0, 120, 212, 0.1);
}

.settings-button.primary {
  background-color: var(--primary-color);
  color: white;
}

.settings-button.primary:hover {
  background-color: var(--secondary-color);
}

.settings-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
}

/* Settings Panel */
.settings-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 400px;
  max-width: 100%;
  background-color: var(--background-color);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  border-left: 1px solid var(--border-color);
}

.settings-panel.visible {
  transform: translateX(0);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
}

.settings-group {
  margin-bottom: var(--spacing-lg);
}

.settings-group h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--foreground-color);
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon input {
  padding-right: 40px !important;
}

.toggle-visibility {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: var(--foreground-color);
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.toggle-visibility:hover {
  opacity: 1;
}

.github-status {
  margin-top: 4px;
  font-size: 12px;
  font-style: italic;
}

.github-status.connected {
  color: var(--success-color);
}

.github-status.error {
  color: var(--error-color);
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--foreground-color);
  opacity: 0.7;
  transition: opacity 0.2s;
  padding: 0;
  line-height: 1;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-button:hover {
  opacity: 1;
  background-color: var(--hover-color);
}

.hidden {
  display: none !important;
}

/* Toast notifications */
.toast {
  position: fixed;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  background-color: var(--foreground-color);
  color: var(--background-color);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  opacity: 0;
  transition: transform var(--transition-slow), opacity var(--transition-slow);
  z-index: 2000;
  box-shadow: var(--shadow-md);
}

.toast.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

/* Mode controls */
/* Mode Controls Container */
.mode-controls {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 10;
  margin-bottom: 8px;
}

/* Mode Buttons */
.mode-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  margin: 0 2px;
  border: 1px solid var(--border-color);
  background-color: var(--background-color);
  color: var(--foreground-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.4;
  outline: none;
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
}

.mode-button:hover {
  background-color: var(--hover-color);
  border-color: var(--primary-color);
}

.mode-button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.mode-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  font-weight: 500;
  box-shadow: 0 0 0 1px rgba(0, 120, 212, 0.2);
}

.mode-button.active .icon {
  color: white;
}

.mode-button .icon {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.mode-button .label {
  font-size: 13px;
}

.mode-button:hover .icon {
  transform: scale(1.1);
}

.mode-button .tooltip {
  visibility: hidden;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--foreground-color);
  color: var(--background-color);
  text-align: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 100;
  pointer-events: none;
}

.mode-button:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

.mode-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
}

/* Mode-specific styles */
.mode-button[data-mode="chat"].active {
  background-color: rgba(0, 120, 212, 0.1);
}

.mode-button[data-mode="agent"].active {
  background-color: rgba(16, 124, 16, 0.1);
}

/* Mode Debug */
.mode-debug {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 11px;
  color: #666;
  max-height: 100px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mode-controls {
    justify-content: space-between;
    padding: var(--spacing-xs);
  }
  
  .mode-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 13px;
    flex: 1;
    justify-content: center;
  }
  
  .mode-button .icon {
    font-size: 1em;
  }
  
  .mode-button .label {
    display: none;
  }
  
  .mode-button .tooltip {
    display: none;
  }
}

.mode-button[data-mode="agent"].active {
  background-color: rgba(16, 124, 16, 0.1);
  border-color: var(--success-color);
  color: var(--success-color);
}

.mode-button[data-mode="agent"].active::after {
  background-color: var(--success-color);
}

.mode-button[data-mode="agent-auto"].active {
  background-color: rgba(216, 59, 1, 0.1);
  border-color: var(--error-color);
  color: var(--error-color);
}

.mode-button[data-mode="agent-auto"].active::after {
  background-color: var(--error-color);
}

/* Mode Selector */
.mode-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
}

.mode-controls {
  display: flex;
  gap: 4px;
  background: var(--background-color);
  border-radius: var(--border-radius-md);
  padding: 2px;
  border: 1px solid var(--border-color);
}

.mode-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border: none;
  background: transparent;
  color: var(--foreground-color);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.mode-button:hover {
  background: var(--hover-color);
}

.mode-button.active {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.mode-button .mode-icon {
  font-size: 14px;
}

.mode-button .mode-name {
  display: none;
}

.current-mode {
  font-size: 13px;
  font-weight: 500;
  color: var(--foreground-color);
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.mode-loading {
  padding: 4px 12px;
  font-size: 13px;
  color: var(--foreground-color);
  opacity: 0.7;
}

@media (min-width: 768px) {
  .mode-button {
    padding: 4px 12px;
  }
  
  .mode-button .mode-name {
    display: inline;
  }
}

/* Mode Selector */
.mode-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
}

/* Tooltip styles */
.mode-button .tooltip {
  visibility: hidden;
  width: 140px;
  background-color: var(--foreground-color);
  color: var(--background-color);
  text-align: center;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  position: absolute;
  z-index: 100;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 12px;
  line-height: 1.4;
  pointer-events: none;
  white-space: normal;
}

.mode-button:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

/* Focus state */
.mode-button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--primary-color);
}

/* Disabled state */
.mode-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--border-color);
}

/* Debug info */
#mode-debug {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.85);
  color: #fff;
  padding: 8px 12px;
  font-family: 'Menlo', 'Consolas', monospace;
  font-size: 11px;
  line-height: 1.5;
  max-height: 150px;
  overflow-y: auto;
  z-index: 1000;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: none; /* Hidden by default, can be toggled for debugging */
  box-sizing: border-box;
}

/* Show debug when it has content */
#mode-debug:not(:empty) {
  display: block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .message {
    max-width: 90%;
  }

  .settings-panel {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .toolbar-button .label {
    display: none;
  }

  .toolbar-button .icon {
    margin-right: 0;
  }

  .model-selector {
    min-width: 150px;
  }
}