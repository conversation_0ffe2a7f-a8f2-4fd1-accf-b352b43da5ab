"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const ts = __importStar(require("typescript"));
function findTypeScriptFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            // Skip node_modules and other excluded directories
            if (['node_modules', '.git', 'dist', 'build', 'coverage'].includes(file)) {
                return;
            }
            findTypeScriptFiles(filePath, fileList);
        }
        else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
            fileList.push(filePath);
        }
    });
    return fileList;
}
function analyzeFile(filePath) {
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const sourceFile = ts.createSourceFile(filePath, fileContent, ts.ScriptTarget.Latest, true);
    const imports = [];
    const exports = [];
    function visit(node) {
        // Handle imports
        if (ts.isImportDeclaration(node)) {
            const moduleSpecifier = node.moduleSpecifier;
            if (moduleSpecifier && moduleSpecifier.text) {
                imports.push(moduleSpecifier.text);
            }
        }
        // Handle exports
        if (ts.isExportDeclaration(node)) {
            const moduleSpecifier = node.moduleSpecifier;
            if (moduleSpecifier && moduleSpecifier.text) {
                exports.push(moduleSpecifier.text);
            }
        }
        ts.forEachChild(node, visit);
    }
    visit(sourceFile);
    return { imports, exports };
}
function generateDependencyMap(projectRoot) {
    const dependencyMap = {};
    const tsFiles = findTypeScriptFiles(projectRoot);
    console.log(`Found ${tsFiles.length} TypeScript files to analyze...`);
    tsFiles.forEach((file, index) => {
        process.stdout.write(`\rAnalyzing ${index + 1}/${tsFiles.length}: ${path.relative(projectRoot, file)}`);
        try {
            dependencyMap[file] = analyzeFile(file);
        }
        catch (error) {
            console.error(`\nError analyzing ${file}:`, error);
        }
    });
    console.log('\nAnalysis complete!');
    return dependencyMap;
}
function saveDependencyReport(dependencyMap, outputPath) {
    const report = {
        summary: {
            totalFiles: Object.keys(dependencyMap).length,
            totalImports: 0,
            uniqueImports: new Set(),
        },
        files: {},
    };
    // Process dependencies
    Object.entries(dependencyMap).forEach(([filePath, deps]) => {
        const relPath = path.relative(process.cwd(), filePath);
        report.files[relPath] = {
            imports: deps.imports,
            exports: deps.exports,
            importCount: deps.imports.length,
        };
        report.summary.totalImports += deps.imports.length;
        deps.imports.forEach(imp => report.summary.uniqueImports.add(imp));
    });
    report.summary.uniqueImportCount = report.summary.uniqueImports.size;
    delete report.summary.uniqueImports; // Convert Set to count for JSON
    // Ensure output directory exists
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    // Save report
    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
    console.log(`Dependency report saved to: ${outputPath}`);
}
// Main execution
const projectRoot = path.join(__dirname, '..');
const outputPath = path.join(projectRoot, 'docs', 'dependencies', 'dependency-report.json');
console.log('Starting dependency analysis...');
const dependencyMap = generateDependencyMap(projectRoot);
saveDependencyReport(dependencyMap, outputPath);
// Generate a simple markdown summary
const summaryPath = path.join(projectRoot, 'docs', 'dependencies', 'SUMMARY.md');
let markdown = '# Dependency Analysis Summary\n\n';
markdown += `Generated on: ${new Date().toISOString()}\n\n`;
markdown += '## Project Overview\n\n';
markdown += `- Total TypeScript files: ${Object.keys(dependencyMap).length}\n`;
// Count imports
let totalImports = 0;
const importCounts = {};
Object.values(dependencyMap).forEach(deps => {
    totalImports += deps.imports.length;
    deps.imports.forEach(imp => {
        importCounts[imp] = (importCounts[imp] || 0) + 1;
    });
});
markdown += `- Total imports: ${totalImports}\n`;
markdown += `- Unique imports: ${Object.keys(importCounts).length}\n\n`;
// Top imported modules
const sortedImports = Object.entries(importCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20);
markdown += '## Most Imported Modules\n\n';
markdown += '| Module | Import Count |\n|--------|-------------:|\n';
sortedImports.forEach(([module, count]) => {
    markdown += `| ${module} | ${count} |\n`;
});
// Files with most dependencies
const filesByDeps = Object.entries(dependencyMap)
    .map(([file, deps]) => ({
    file: path.relative(projectRoot, file),
    count: deps.imports.length
}))
    .sort((a, b) => b.count - a.count)
    .slice(0, 20);
markdown += '\n## Files with Most Dependencies\n\n';
markdown += '| File | Dependencies |\n|------|-------------:|\n';
filesByDeps.forEach(({ file, count }) => {
    markdown += `| ${file} | ${count} |\n`;
});
fs.writeFileSync(summaryPath, markdown);
console.log(`Summary saved to: ${summaryPath}`);
//# sourceMappingURL=simple-deps.js.map