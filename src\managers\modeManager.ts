import * as vscode from 'vscode';
import { ChatMode, CHAT_MODE_CONFIGS, ChatModeConfig } from '../models/chatMode';

export { ChatMode } from '../models/chatMode';

export interface ModeInfo {
  id: string;
  name: string;
  icon: string;
  description: string;
  isActive: boolean;
}

/**
 * Manages the current chat mode and its configuration
 */
export class ModeManager {
    private _currentMode: ChatMode;
    private _config: vscode.WorkspaceConfiguration;
    private _initialized: boolean = false;
    private _isSettingMode: boolean = false;
    private readonly _modeChangeListeners: Array<(mode: ChatMode) => void> = [];

    constructor() {
        this._config = vscode.workspace.getConfiguration('vidyadhara');
        this._currentMode = this._config.get('chatMode') || ChatMode.Chat;
        this._initialized = true;
    }

    /**
     * Sets the current chat mode
     * @param mode The mode to set
     * @returns Promise that resolves to true if the mode was set successfully
     */
    public async setMode(mode: ChatMode): Promise<boolean> {
        // Prevent reentrancy
        if (this._isSettingMode) {
            console.log(`[ModeManager] Already setting mode, ignoring request to set to ${mode}`);
            return false;
        }

        this._isSettingMode = true;
        
        try {
            if (!this._initialized) {
                console.warn('[ModeManager] setMode called before initialization');
                // Try to initialize
                this._config = vscode.workspace.getConfiguration('vidyadhara');
                this._initialized = true;
            }

            // Validate the mode
            if (!Object.values(ChatMode).includes(mode)) {
                const errorMsg = `Invalid mode: ${mode}. Valid modes are: ${Object.values(ChatMode).join(', ')}`;
                console.error(`[ModeManager] ${errorMsg}`);
                vscode.window.showErrorMessage(errorMsg);
                return false;
            }

            // Don't update if it's the same mode
            if (this._currentMode === mode) {
                console.log(`[ModeManager] Already in mode: ${mode}, no change needed`);
                return true;
            }

            console.log(`[ModeManager] Setting mode to: ${mode}`);
            
            // Update the configuration
            await this._config.update('chatMode', mode, vscode.ConfigurationTarget.Global);
            
            // Update the current mode
            const previousMode = this._currentMode;
            this._currentMode = mode;
            
            // Notify listeners of mode change
            this._notifyModeChange(mode, previousMode);
            
            console.log(`[ModeManager] Successfully set mode to: ${mode}`);
            return true;
            
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.error(`[ModeManager] Error setting mode to ${mode}:`, errorMsg);
            vscode.window.showErrorMessage(`Failed to set mode to ${mode}: ${errorMsg}`);
            return false;
        } finally {
            this._isSettingMode = false;
        }
    }

    /**
     * Gets the current chat mode
     * @returns The current chat mode
     */
    public getCurrentMode(): ChatMode {
        if (!this._initialized) {
            console.warn('[ModeManager] getCurrentMode called before initialization, initializing...');
            try {
                this._config = vscode.workspace.getConfiguration('vidyadhara');
                this._currentMode = this._config.get('chatMode') || ChatMode.Chat;
                this._initialized = true;
                console.log(`[ModeManager] Initialized with mode: ${this._currentMode}`);
            } catch (error) {
                console.error('[ModeManager] Error initializing from config:', error);
                this._currentMode = ChatMode.Chat; // Fallback to default
            }
        }
        return this._currentMode;
    }

    /**
     * Gets the configuration for the current mode
     * @returns The configuration for the current mode
     */
    public getModeConfig(): ChatModeConfig | undefined {
        const mode = this.getCurrentMode();
        return CHAT_MODE_CONFIGS[mode];
    }

    /**
     * Refreshes the configuration from VS Code settings
     */
    public async refreshConfig(): Promise<void> {
        try {
            this._config = vscode.workspace.getConfiguration('vidyadhara');
            this._currentMode = this._config.get('chatMode') || ChatMode.Chat;
            this._initialized = true;
            console.log('[ModeManager] Configuration refreshed');
        } catch (error) {
            console.error('[ModeManager] Error refreshing configuration:', error);
        }
    }

    /**
     * Gets all available chat modes with their configurations
     * @returns Object mapping mode IDs to their configurations
     */
    public getAvailableModes(): Record<string, ChatModeConfig> {
        return { ...CHAT_MODE_CONFIGS };
    }



    /**
     * Changes the current mode
     * @param modeId The mode ID to change to
     * @returns Promise that resolves when the mode is changed
     */
    public async changeMode(modeId: string): Promise<{ modeId: string; modeName: string; modeIcon: string }> {
        if (!(modeId in CHAT_MODE_CONFIGS)) {
            throw new Error(`Invalid mode: ${modeId}`);
        }

        const success = await this.setMode(modeId as ChatMode);
        if (!success) {
            throw new Error(`Failed to change to mode: ${modeId}`);
        }

        const config = this.getModeConfig();
        return {
            modeId,
            modeName: config?.name || modeId,
            modeIcon: config?.icon || '💬'
        };
    }

    /**
     * Add a listener for mode changes
     * @param listener Callback function that receives the new mode
     * @returns Disposable to remove the listener
     */
    public onModeChange(listener: (mode: ChatMode) => void): vscode.Disposable {
        this._modeChangeListeners.push(listener);
        return {
            dispose: () => {
                const index = this._modeChangeListeners.indexOf(listener);
                if (index !== -1) {
                    this._modeChangeListeners.splice(index, 1);
                }
            }
        };
    }

    /**
     * Notify all listeners of a mode change
     * @param newMode The new mode that was set
     * @param previousMode The previous mode
     */
    private _notifyModeChange(newMode: ChatMode, previousMode: ChatMode): void {
        console.log(`[ModeManager] Notifying ${this._modeChangeListeners.length} listeners of mode change from ${previousMode} to ${newMode}`);
        // Create a copy of the listeners array to avoid issues if listeners are removed during iteration
        const listeners = [...this._modeChangeListeners];
        for (const listener of listeners) {
            try {
                listener(newMode);
            } catch (error) {
                console.error('[ModeManager] Error in mode change listener:', error);
            }
        }
    }
}
