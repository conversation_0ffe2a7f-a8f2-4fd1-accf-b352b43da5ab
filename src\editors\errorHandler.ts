import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { EditOperation } from './stringReplaceEditor';

const access = promisify(fs.access);
const stat = promisify(fs.stat);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const mkdir = promisify(fs.mkdir);

export interface ErrorRecoveryOptions {
  createBackup: boolean;
  validateSyntax: boolean;
  checkPermissions: boolean;
  maxRetries: number;
  retryDelay: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  canProceed: boolean;
}

export interface BackupInfo {
  filePath: string;
  backupPath: string;
  timestamp: Date;
  originalSize: number;
}

export class ErrorHandler {
  private backupDir: string;
  private readonly defaultOptions: ErrorRecoveryOptions = {
    createBackup: true,
    validateSyntax: true,
    checkPermissions: true,
    maxRetries: 3,
    retryDelay: 1000
  };

  constructor() {
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    this.backupDir = path.join(workspaceRoot, '.vidyadhara', 'backups');
    this.ensureBackupDirectory();
  }

  /**
   * Comprehensive validation before applying edits
   */
  async validateEdit(
    operation: EditOperation,
    options: Partial<ErrorRecoveryOptions> = {}
  ): Promise<ValidationResult> {
    const opts = { ...this.defaultOptions, ...options };
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check file permissions
      if (opts.checkPermissions) {
        const permissionResult = await this.checkFilePermissions(operation.filePath);
        if (!permissionResult.canWrite) {
          errors.push(`File is not writable: ${operation.filePath}`);
        }
        if (!permissionResult.exists) {
          errors.push(`File does not exist: ${operation.filePath}`);
        }
      }

      // Validate file content and syntax
      if (opts.validateSyntax) {
        const syntaxResult = await this.validateSyntax(operation);
        errors.push(...syntaxResult.errors);
        warnings.push(...syntaxResult.warnings);
      }

      // Check for potentially dangerous operations
      const riskAssessment = this.assessEditRisk(operation);
      warnings.push(...riskAssessment.warnings);
      if (riskAssessment.isHighRisk) {
        warnings.push('High-risk operation detected - please review carefully');
      }

      // Validate line ranges
      const rangeValidation = await this.validateLineRanges(operation);
      errors.push(...rangeValidation.errors);

    } catch (error) {
      errors.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      canProceed: errors.length === 0
    };
  }

  /**
   * Create backup before applying changes
   */
  async createBackup(filePath: string): Promise<BackupInfo> {
    try {
      const fullPath = this.resolveFilePath(filePath);
      const stats = await stat(fullPath);
      const content = await readFile(fullPath, 'utf8');
      
      const timestamp = new Date();
      const backupFileName = `${path.basename(filePath)}.${timestamp.getTime()}.backup`;
      const backupPath = path.join(this.backupDir, backupFileName);
      
      await writeFile(backupPath, content, 'utf8');

      const backupInfo: BackupInfo = {
        filePath,
        backupPath,
        timestamp,
        originalSize: stats.size
      };

      vscode.window.showInformationMessage(`Backup created: ${backupFileName}`);
      return backupInfo;

    } catch (error) {
      throw new Error(`Failed to create backup: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupInfo: BackupInfo): Promise<void> {
    try {
      const backupContent = await readFile(backupInfo.backupPath, 'utf8');
      const fullPath = this.resolveFilePath(backupInfo.filePath);
      
      await writeFile(fullPath, backupContent, 'utf8');
      
      // Update VS Code editor
      const document = await vscode.workspace.openTextDocument(fullPath);
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        document.positionAt(0),
        document.positionAt(document.getText().length)
      );
      edit.replace(document.uri, fullRange, backupContent);
      await vscode.workspace.applyEdit(edit);

      vscode.window.showInformationMessage(`Restored from backup: ${backupInfo.filePath}`);

    } catch (error) {
      throw new Error(`Failed to restore from backup: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Execute operation with error recovery
   */
  async executeWithRecovery<T>(
    operation: () => Promise<T>,
    options: Partial<ErrorRecoveryOptions> = {}
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= opts.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < opts.maxRetries) {
          vscode.window.showWarningMessage(
            `Operation failed (attempt ${attempt}/${opts.maxRetries}): ${lastError.message}. Retrying...`
          );
          await this.delay(opts.retryDelay);
        }
      }
    }

    // All retries failed
    const errorMessage = `Operation failed after ${opts.maxRetries} attempts: ${lastError?.message}`;
    vscode.window.showErrorMessage(errorMessage);
    throw new Error(errorMessage);
  }

  /**
   * Check file permissions
   */
  private async checkFilePermissions(filePath: string): Promise<{ exists: boolean; canRead: boolean; canWrite: boolean }> {
    const fullPath = this.resolveFilePath(filePath);
    
    try {
      await access(fullPath, fs.constants.F_OK);
      const canRead = await this.canAccess(fullPath, fs.constants.R_OK);
      const canWrite = await this.canAccess(fullPath, fs.constants.W_OK);
      
      return { exists: true, canRead, canWrite };
    } catch {
      return { exists: false, canRead: false, canWrite: false };
    }
  }

  /**
   * Validate syntax for specific file types
   */
  private async validateSyntax(operation: EditOperation): Promise<{ errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const fileExtension = path.extname(operation.filePath).toLowerCase();

    try {
      switch (fileExtension) {
        case '.json':
          try {
            JSON.parse(operation.newText);
          } catch (e) {
            errors.push(`Invalid JSON syntax: ${e instanceof Error ? e.message : String(e)}`);
          }
          break;

        case '.js':
        case '.ts':
          // Basic JavaScript/TypeScript validation
          const jsErrors = this.validateJavaScript(operation.newText);
          errors.push(...jsErrors);
          break;

        case '.html':
          // Basic HTML validation
          const htmlErrors = this.validateHtml(operation.newText);
          errors.push(...htmlErrors);
          break;

        case '.css':
          // Basic CSS validation
          const cssErrors = this.validateCss(operation.newText);
          warnings.push(...cssErrors); // CSS errors are often non-critical
          break;

        default:
          // Generic validation
          break;
      }
    } catch (error) {
      errors.push(`Syntax validation error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return { errors, warnings };
  }

  /**
   * Assess risk level of edit operation
   */
  private assessEditRisk(operation: EditOperation): { isHighRisk: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let isHighRisk = false;

    // Check for large changes
    const oldLineCount = operation.oldText.split('\n').length;
    const newLineCount = operation.newText.split('\n').length;
    const changeSize = Math.abs(newLineCount - oldLineCount);

    if (changeSize > 100) {
      warnings.push(`Large change detected: ${changeSize} lines affected`);
      isHighRisk = true;
    }

    // Check for critical file modifications
    const criticalFiles = ['.gitignore', 'package.json', 'tsconfig.json', '.env'];
    if (criticalFiles.some(file => operation.filePath.endsWith(file))) {
      warnings.push('Modifying critical configuration file');
      isHighRisk = true;
    }

    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      /rm\s+-rf/,
      /sudo\s+/,
      /eval\s*\(/,
      /exec\s*\(/,
      /system\s*\(/
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(operation.newText)) {
        warnings.push('Potentially dangerous code pattern detected');
        isHighRisk = true;
        break;
      }
    }

    return { isHighRisk, warnings };
  }

  /**
   * Validate line ranges
   */
  private async validateLineRanges(operation: EditOperation): Promise<{ errors: string[] }> {
    const errors: string[] = [];

    try {
      const document = await vscode.workspace.openTextDocument(operation.filePath);
      const lineCount = document.lineCount;

      if (operation.startLine < 1 || operation.endLine < 1) {
        errors.push('Line numbers must be greater than 0');
      }

      if (operation.startLine > lineCount || operation.endLine > lineCount) {
        errors.push(`Line numbers exceed file length (${lineCount} lines)`);
      }

      if (operation.startLine > operation.endLine) {
        errors.push('Start line must be less than or equal to end line');
      }

    } catch (error) {
      errors.push(`Failed to validate line ranges: ${error instanceof Error ? error.message : String(error)}`);
    }

    return { errors };
  }

  /**
   * Basic JavaScript validation
   */
  private validateJavaScript(code: string): string[] {
    const errors: string[] = [];

    // Check for basic syntax issues
    const openBraces = (code.match(/\{/g) || []).length;
    const closeBraces = (code.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
      errors.push('Mismatched braces in JavaScript code');
    }

    const openParens = (code.match(/\(/g) || []).length;
    const closeParens = (code.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push('Mismatched parentheses in JavaScript code');
    }

    return errors;
  }

  /**
   * Basic HTML validation
   */
  private validateHtml(html: string): string[] {
    const errors: string[] = [];

    // Check for basic tag matching
    const openTags = (html.match(/<[^/][^>]*>/g) || []).length;
    const closeTags = (html.match(/<\/[^>]*>/g) || []).length;
    const selfClosingTags = (html.match(/<[^>]*\/>/g) || []).length;

    if (openTags !== closeTags + selfClosingTags) {
      errors.push('Mismatched HTML tags detected');
    }

    return errors;
  }

  /**
   * Basic CSS validation
   */
  private validateCss(css: string): string[] {
    const warnings: string[] = [];

    // Check for basic CSS syntax
    const openBraces = (css.match(/\{/g) || []).length;
    const closeBraces = (css.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
      warnings.push('Mismatched braces in CSS code');
    }

    return warnings;
  }

  /**
   * Check if file can be accessed with specific permissions
   */
  private async canAccess(filePath: string, mode: number): Promise<boolean> {
    try {
      await access(filePath, mode);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Ensure backup directory exists
   */
  private async ensureBackupDirectory(): Promise<void> {
    try {
      await mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      console.warn('Failed to create backup directory:', error);
    }
  }

  /**
   * Resolve file path
   */
  private resolveFilePath(filePath: string): string {
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    return path.join(workspaceRoot, filePath);
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
