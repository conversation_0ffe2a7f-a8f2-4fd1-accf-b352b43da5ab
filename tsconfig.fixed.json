{"extends": "./tsconfig.json", "compilerOptions": {"target": "es2018", "module": "esnext", "moduleResolution": "node", "esModuleInterop": true, "downlevelIteration": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/retrieval/RelationshipGraph.clean.ts"], "exclude": ["node_modules"]}