import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AgentTools } from '../../agentTools';
import { TaskManager, TaskState, TaskPriority } from '../../tasks/taskManager';
import { TaskScheduler } from '../../tasks/taskScheduler';
import { StringReplaceEditor } from '../../editors/stringReplaceEditor';
import { CodebaseRetrieval } from '../../retrieval/codebaseRetrieval';

suite('Comprehensive Integration Tests', () => {
  let agentTools: AgentTools;
  let taskManager: TaskManager;
  let taskScheduler: TaskScheduler;
  let testWorkspaceRoot: string;

  suiteSetup(async () => {
    // Setup test workspace
    testWorkspaceRoot = path.join(__dirname, '..', '..', '..', 'test-workspace');
    
    // Create test files
    await createTestFiles();
    
    // Initialize components
    const context = {
      globalState: {
        get: () => undefined,
        update: () => Promise.resolve()
      }
    } as any;
    
    taskManager = new TaskManager(context);
    taskScheduler = new TaskScheduler();
  });

  suiteTeardown(async () => {
    // Cleanup test files
    await cleanupTestFiles();
  });

  suite('File Editing Integration', () => {
    test('should perform complex multi-file editing workflow', async () => {
      const editor = new StringReplaceEditor();
      
      // Create test file
      const testFile = path.join(testWorkspaceRoot, 'edit-test.ts');
      const initialContent = `class TestClass {
  private value: number = 0;
  
  getValue(): number {
    return this.value;
  }
  
  setValue(newValue: number): void {
    this.value = newValue;
  }
}`;
      
      await fs.promises.writeFile(testFile, initialContent);
      
      // Perform edit operation
      const operation = await editor.replaceTextInRange(
        testFile,
        4, 6,
        `  getValue(): number {
    return this.value * 2;
  }`
      );
      
      assert.strictEqual(operation.startLine, 4);
      assert.strictEqual(operation.endLine, 6);
      assert.ok(operation.newText.includes('this.value * 2'));
      
      // Apply the edit
      await editor.applyEdit(operation);
      
      // Verify the change
      const updatedContent = await fs.promises.readFile(testFile, 'utf8');
      assert.ok(updatedContent.includes('this.value * 2'));
    });

    test('should handle edit validation and error recovery', async () => {
      const editor = new StringReplaceEditor();
      
      // Test invalid line range
      await assert.rejects(
        () => editor.replaceTextInRange('nonexistent.ts', 1, 5, 'new content'),
        /File not found/
      );
      
      // Test invalid line numbers
      const testFile = path.join(testWorkspaceRoot, 'small-file.ts');
      await fs.promises.writeFile(testFile, 'line 1\nline 2\n');
      
      await assert.rejects(
        () => editor.replaceTextInRange(testFile, 10, 15, 'new content'),
        /Line numbers exceed file length/
      );
    });
  });

  suite('Codebase Retrieval Integration', () => {
    test('should index and search codebase effectively', async () => {
      const retrieval = new CodebaseRetrieval();
      
      // Create test files with different content
      await createCodebaseTestFiles();
      
      // Index the codebase
      await retrieval.indexCodebase();
      
      // Search for functions
      const functionResults = await retrieval.searchCodebase({
        text: 'function calculate',
        intent: 'find_function',
        maxResults: 5
      });
      
      assert.ok(functionResults.length > 0);
      assert.ok(functionResults.some(result => 
        result.symbols.some(symbol => symbol.type === 'function')
      ));
      
      // Search for classes
      const classResults = await retrieval.searchCodebase({
        text: 'class Calculator',
        intent: 'find_class',
        maxResults: 5
      });
      
      assert.ok(classResults.length > 0);
      assert.ok(classResults.some(result => 
        result.symbols.some(symbol => symbol.type === 'class')
      ));
    });

    test('should provide contextual suggestions', async () => {
      const retrieval = new CodebaseRetrieval();
      const testFile = path.join(testWorkspaceRoot, 'context-test.ts');
      
      const content = `import { Calculator } from './calculator';

class MathService {
  private calc = new Calculator();
  
  performCalculation(a: number, b: number): number {
    // Need to call calculator method here
    return 0;
  }
}`;
      
      await fs.promises.writeFile(testFile, content);
      
      const suggestions = await retrieval.getContextualSuggestions(
        testFile,
        new vscode.Position(6, 10),
        'calculator method'
      );
      
      assert.ok(suggestions.length >= 0); // May be empty if no calculator methods found
    });
  });

  suite('Task Management Integration', () => {
    test('should create and manage task hierarchy', async () => {
      // Create parent task
      const parentTask = await taskManager.createTask({
        name: 'Implement Authentication System',
        description: 'Complete user authentication functionality',
        priority: TaskPriority.HIGH,
        estimatedHours: 20
      });
      
      assert.strictEqual(parentTask.name, 'Implement Authentication System');
      assert.strictEqual(parentTask.state, TaskState.NOT_STARTED);
      assert.strictEqual(parentTask.priority, TaskPriority.HIGH);
      
      // Create subtasks
      const subtask1 = await taskManager.createTask({
        name: 'Create User Model',
        description: 'Define user data structure',
        parentId: parentTask.id,
        estimatedHours: 4
      });
      
      const subtask2 = await taskManager.createTask({
        name: 'Implement Login API',
        description: 'Create login endpoint',
        parentId: parentTask.id,
        estimatedHours: 8,
        dependencies: [subtask1.id]
      });
      
      // Verify hierarchy
      const updatedParent = taskManager.getTask(parentTask.id);
      assert.ok(updatedParent);
      assert.strictEqual(updatedParent.childIds.length, 2);
      assert.ok(updatedParent.childIds.includes(subtask1.id));
      assert.ok(updatedParent.childIds.includes(subtask2.id));
      
      // Verify dependency
      assert.ok(subtask2.dependencies.includes(subtask1.id));
    });

    test('should handle task state transitions correctly', async () => {
      const task = await taskManager.createTask({
        name: 'Test Task',
        description: 'Task for testing state transitions'
      });
      
      // Test valid transition
      await taskManager.updateTask(task.id, { state: TaskState.IN_PROGRESS });
      const updatedTask = taskManager.getTask(task.id);
      assert.strictEqual(updatedTask?.state, TaskState.IN_PROGRESS);
      
      // Test completion
      await taskManager.updateTask(task.id, { state: TaskState.COMPLETE });
      const completedTask = taskManager.getTask(task.id);
      assert.strictEqual(completedTask?.state, TaskState.COMPLETE);
      assert.ok(completedTask?.completedAt);
    });

    test('should generate project schedule', async () => {
      // Create tasks with dependencies
      const task1 = await taskManager.createTask({
        name: 'Design Database Schema',
        estimatedHours: 8
      });
      
      const task2 = await taskManager.createTask({
        name: 'Implement Database Layer',
        estimatedHours: 16,
        dependencies: [task1.id]
      });
      
      const task3 = await taskManager.createTask({
        name: 'Create API Endpoints',
        estimatedHours: 12,
        dependencies: [task2.id]
      });
      
      // Generate schedule
      const tasks = taskManager.getTasks();
      const schedule = taskScheduler.generateSchedule(tasks);
      
      assert.ok(schedule.tasks.length >= 3);
      assert.ok(schedule.projectEnd > schedule.projectStart);
      assert.ok(schedule.totalDuration > 0);
      assert.ok(schedule.criticalPath.length > 0);
    });
  });

  suite('End-to-End Workflow', () => {
    test('should complete full development workflow', async () => {
      // 1. Create a development task
      const task = await taskManager.createTask({
        name: 'Add User Profile Feature',
        description: 'Implement user profile management',
        priority: TaskPriority.MEDIUM,
        estimatedHours: 12
      });
      
      // 2. Start time tracking
      await taskManager.startTimeTracking(task.id);
      
      // 3. Create and edit files
      const editor = new StringReplaceEditor();
      const profileFile = path.join(testWorkspaceRoot, 'userProfile.ts');
      
      const profileCode = `export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

export class UserProfileService {
  async getProfile(userId: string): Promise<UserProfile> {
    // TODO: Implement profile retrieval
    throw new Error('Not implemented');
  }
  
  async updateProfile(userId: string, updates: Partial<UserProfile>): Promise<void> {
    // TODO: Implement profile update
    throw new Error('Not implemented');
  }
}`;
      
      await fs.promises.writeFile(profileFile, profileCode);
      
      // 4. Implement the methods
      await editor.replaceTextInRange(
        profileFile,
        9, 11,
        `  async getProfile(userId: string): Promise<UserProfile> {
    const response = await fetch(\`/api/users/\${userId}/profile\`);
    return response.json();
  }`
      );
      
      // 5. Search for related code
      const retrieval = new CodebaseRetrieval();
      const searchResults = await retrieval.searchCodebase({
        text: 'user profile',
        intent: 'general',
        maxResults: 5
      });
      
      // 6. Complete the task
      await taskManager.updateTask(task.id, { state: TaskState.COMPLETE });
      
      // 7. Stop time tracking
      const timeSpent = await taskManager.stopTimeTracking(task.id);
      
      // Verify workflow completion
      const completedTask = taskManager.getTask(task.id);
      assert.strictEqual(completedTask?.state, TaskState.COMPLETE);
      assert.ok(completedTask?.completedAt);
      assert.ok(timeSpent >= 0);
      
      // Verify file was created and modified
      const fileContent = await fs.promises.readFile(profileFile, 'utf8');
      assert.ok(fileContent.includes('fetch(`/api/users/${userId}/profile`)'));
    });
  });

  // Helper functions
  async function createTestFiles(): Promise<void> {
    await fs.promises.mkdir(testWorkspaceRoot, { recursive: true });
  }

  async function cleanupTestFiles(): Promise<void> {
    try {
      await fs.promises.rmdir(testWorkspaceRoot, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  }

  async function createCodebaseTestFiles(): Promise<void> {
    const calculatorFile = path.join(testWorkspaceRoot, 'calculator.ts');
    const calculatorCode = `export class Calculator {
  add(a: number, b: number): number {
    return a + b;
  }
  
  subtract(a: number, b: number): number {
    return a - b;
  }
  
  multiply(a: number, b: number): number {
    return a * b;
  }
}

export function calculateTax(amount: number, rate: number): number {
  return amount * rate;
}`;
    
    const utilsFile = path.join(testWorkspaceRoot, 'utils.ts');
    const utilsCode = `export function formatCurrency(amount: number): string {
  return \`$\${amount.toFixed(2)}\`;
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  return emailRegex.test(email);
}`;
    
    await fs.promises.writeFile(calculatorFile, calculatorCode);
    await fs.promises.writeFile(utilsFile, utilsCode);
  }
});

// Performance and stress tests
suite('Performance Tests', () => {
  test('should handle large file editing efficiently', async () => {
    const editor = new StringReplaceEditor();
    const largeFile = path.join(__dirname, 'large-test-file.ts');
    
    // Create a large file
    const lines = Array.from({ length: 10000 }, (_, i) => `// Line ${i + 1}`);
    const largeContent = lines.join('\n');
    await fs.promises.writeFile(largeFile, largeContent);
    
    const startTime = Date.now();
    
    // Perform edit operation
    await editor.replaceTextInRange(
      largeFile,
      5000, 5010,
      '// Modified section\n'.repeat(10)
    );
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (less than 1 second)
    assert.ok(duration < 1000, `Edit took too long: ${duration}ms`);
    
    // Cleanup
    await fs.promises.unlink(largeFile);
  });

  test('should handle many concurrent tasks efficiently', async () => {
    const context = {
      globalState: {
        get: () => undefined,
        update: () => Promise.resolve()
      }
    } as any;
    
    const taskManager = new TaskManager(context);
    const startTime = Date.now();
    
    // Create many tasks concurrently
    const taskPromises = Array.from({ length: 100 }, (_, i) =>
      taskManager.createTask({
        name: `Performance Test Task ${i}`,
        description: `Task ${i} for performance testing`
      })
    );
    
    const tasks = await Promise.all(taskPromises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    assert.strictEqual(tasks.length, 100);
    assert.ok(duration < 5000, `Task creation took too long: ${duration}ms`);
    
    // Verify all tasks were created correctly
    tasks.forEach((task, i) => {
      assert.strictEqual(task.name, `Performance Test Task ${i}`);
      assert.strictEqual(task.state, TaskState.NOT_STARTED);
    });
  });
});
