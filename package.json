{"name": "<PERSON><PERSON><PERSON><PERSON>", "publisher": "shiva<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": "AI coding assistant", "version": "0.1.0", "repository": {"type": "git", "url": "https://github.com/shivam29feb/vidyadhara"}, "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished", "onView:vidyadhara.chatView"], "main": "./dist/extension.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "node ./test/runTest.js", "compile": "tsc -p .", "watch": "tsc -watch -p ."}, "contributes": {"commands": [{"command": "vidyadhara.setChatMode", "title": "Vidyadhara: <PERSON>"}, {"command": "vidyadhara.helloWorld", "title": "Vidyadhara: Hello World"}, {"command": "vidyadhara.setOpenRouterApiKey", "title": "Vidyadhara: Set OpenRouter API Key"}, {"command": "vidyadhara.setGitHubToken", "title": "Vidyadhara: <PERSON>"}, {"command": "vidyadhara.openChatPanel", "title": "Vidyadhara: Open Chat Panel"}, {"command": "vidyadhara.clearChatHistory", "title": "Vidyadhara: Clear Chat History"}, {"command": "vidyadhara.indexCodebase", "title": "Vidyadhara: Index Codebase"}, {"command": "vidyadhara.showFileChanges", "title": "V<PERSON>adhara: Show Pending File Changes"}, {"command": "vidyadhara.setChatMode", "title": "Vidyadhara: <PERSON>", "category": "<PERSON><PERSON><PERSON><PERSON>"}], "viewsContainers": {"activitybar": [{"id": "vidy<PERSON>hara-sidebar", "title": "<PERSON><PERSON><PERSON><PERSON>", "icon": "media/icon.png"}]}, "views": {"vidyadhara-sidebar": [{"type": "webview", "id": "vidyadhara.chatView", "name": "Cha<PERSON>"}]}, "configuration": {"title": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"vidyadhara.openRouterApiKey": {"type": "string", "default": "", "description": "API key for OpenRouter"}, "vidyadhara.defaultModel": {"type": "string", "default": "anthropic/claude-3-5-sonnet-20241022", "description": "Default model to use for AI responses"}, "vidyadhara.maxContextLength": {"type": "number", "default": 100000, "description": "Maximum context length for AI requests"}, "vidyadhara.embeddingModel": {"type": "string", "default": "openai/text-embedding-ada-002", "description": "Model to use for generating embeddings"}, "vidyadhara.github.token": {"type": "string", "default": "", "description": "GitHub personal access token"}, "vidyadhara.github.owner": {"type": "string", "default": "", "description": "GitHub repository owner"}, "vidyadhara.github.repo": {"type": "string", "default": "", "description": "GitHub repository name"}, "vidyadhara.github.defaultBranch": {"type": "string", "default": "main", "description": "Default branch of the GitHub repository"}, "vidyadhara.web.searchApiKey": {"type": "string", "default": "", "description": "Google Custom Search API key"}, "vidyadhara.web.searchEngineId": {"type": "string", "default": "", "description": "Google Custom Search Engine ID"}, "vidyadhara.chatMode": {"type": "string", "default": "chat", "enum": ["chat", "agent", "agent-auto"], "enumDescriptions": ["Standard chat mode for general conversations", "AI agent mode for code-related tasks", "Automated agent mode for continuous assistance"], "description": "The active chat mode"}}}}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.80.0", "eslint": "^8.39.0", "glob": "^8.1.0", "mocha": "^10.8.2", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.100.1", "webpack-cli": "^6.0.1"}, "dependencies": {"@types/highlight.js": "^9.12.4", "@types/marked": "^5.0.2", "@types/uuid": "^10.0.0", "@vscode/test-electron": "^2.5.2", "@xenova/transformers": "^2.17.2", "axios": "^1.8.4", "highlight.js": "^11.11.1", "marked": "^15.0.12", "simple-git": "^3.27.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}}