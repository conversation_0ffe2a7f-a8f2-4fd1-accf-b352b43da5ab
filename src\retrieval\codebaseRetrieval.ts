import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const readFile = promisify(fs.readFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

export interface CodeSearchResult {
  filePath: string;
  content: string;
  relevanceScore: number;
  matchedLines: number[];
  context: string;
  symbols: CodeSymbol[];
  explanation: string;
}

export interface CodeSymbol {
  name: string;
  type: 'class' | 'function' | 'variable' | 'interface' | 'enum' | 'type';
  filePath: string;
  line: number;
  column: number;
  documentation?: string;
  signature?: string;
  scope: string;
}

export interface SearchQuery {
  text: string;
  intent: 'find_function' | 'find_class' | 'find_usage' | 'find_pattern' | 'general';
  fileTypes?: string[];
  excludePaths?: string[];
  maxResults?: number;
  includeTests?: boolean;
}

export interface CodeEmbedding {
  filePath: string;
  content: string;
  embedding: number[];
  symbols: CodeSymbol[];
  lastModified: Date;
}

export class CodebaseRetrieval {
  private workspaceRoot: string;
  private embeddings: Map<string, CodeEmbedding> = new Map();
  private symbolIndex: Map<string, CodeSymbol[]> = new Map();
  private fileIndex: Map<string, string> = new Map();
  private isIndexing: boolean = false;

  constructor() {
    this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    this.initializeWatchers();
  }

  /**
   * Search codebase with natural language query
   */
  async searchCodebase(query: SearchQuery): Promise<CodeSearchResult[]> {
    try {
      // Ensure index is up to date
      if (!this.isIndexed()) {
        await this.indexCodebase();
      }

      // Determine search intent
      const intent = this.determineSearchIntent(query.text);
      query.intent = intent;

      // Perform different types of searches based on intent
      const results = await this.performSearch(query);

      // Rank and filter results
      const rankedResults = this.rankResults(results, query);

      // Limit results
      const maxResults = query.maxResults || 10;
      return rankedResults.slice(0, maxResults);

    } catch (error) {
      console.error('Codebase search failed:', error);
      throw new Error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Find symbols by name or pattern
   */
  async findSymbols(pattern: string, type?: CodeSymbol['type']): Promise<CodeSymbol[]> {
    const results: CodeSymbol[] = [];
    const regex = new RegExp(pattern, 'i');

    for (const symbols of this.symbolIndex.values()) {
      for (const symbol of symbols) {
        if (regex.test(symbol.name) && (!type || symbol.type === type)) {
          results.push(symbol);
        }
      }
    }

    return results.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Get context-aware suggestions based on current file
   */
  async getContextualSuggestions(
    currentFilePath: string,
    cursorPosition: vscode.Position,
    query: string
  ): Promise<CodeSearchResult[]> {
    try {
      // Get current file context
      const currentFileContent = await this.getFileContent(currentFilePath);
      const currentLine = currentFileContent.split('\n')[cursorPosition.line];
      
      // Extract context around cursor
      const contextLines = this.extractContext(currentFileContent, cursorPosition, 5);
      
      // Enhance query with context
      const enhancedQuery: SearchQuery = {
        text: query,
        intent: 'general',
        maxResults: 5
      };

      // Weight results based on current file context
      const results = await this.searchCodebase(enhancedQuery);
      
      return this.reRankWithContext(results, currentFilePath, contextLines);

    } catch (error) {
      console.error('Contextual suggestions failed:', error);
      return [];
    }
  }

  /**
   * Index the entire codebase
   */
  async indexCodebase(progressCallback?: (progress: number) => void): Promise<void> {
    if (this.isIndexing) {
      return;
    }

    this.isIndexing = true;
    
    try {
      console.log('Starting codebase indexing...');
      
      // Clear existing indices
      this.embeddings.clear();
      this.symbolIndex.clear();
      this.fileIndex.clear();

      // Get all code files
      const codeFiles = await this.getCodeFiles();
      console.log(`Found ${codeFiles.length} code files to index`);

      // Index files
      for (let i = 0; i < codeFiles.length; i++) {
        const filePath = codeFiles[i];
        
        try {
          await this.indexFile(filePath);
          
          if (progressCallback) {
            progressCallback((i + 1) / codeFiles.length * 100);
          }
        } catch (error) {
          console.warn(`Failed to index file ${filePath}:`, error);
        }
      }

      console.log('Codebase indexing completed');
      
    } finally {
      this.isIndexing = false;
    }
  }

  /**
   * Index a single file
   */
  private async indexFile(filePath: string): Promise<void> {
    try {
      const content = await this.getFileContent(filePath);
      const stats = await stat(filePath);
      
      // Extract symbols from file
      const symbols = await this.extractSymbols(filePath, content);
      
      // Generate embedding (simplified - in real implementation would use ML model)
      const embedding = this.generateSimpleEmbedding(content);
      
      // Store in indices
      const codeEmbedding: CodeEmbedding = {
        filePath,
        content,
        embedding,
        symbols,
        lastModified: stats.mtime
      };
      
      this.embeddings.set(filePath, codeEmbedding);
      this.symbolIndex.set(filePath, symbols);
      this.fileIndex.set(filePath, content);

    } catch (error) {
      console.error(`Failed to index file ${filePath}:`, error);
    }
  }

  /**
   * Extract symbols from code content
   */
  private async extractSymbols(filePath: string, content: string): Promise<CodeSymbol[]> {
    const symbols: CodeSymbol[] = [];
    const lines = content.split('\n');
    const fileExtension = path.extname(filePath);

    // Simple regex-based symbol extraction (can be enhanced with AST parsing)
    const patterns = this.getSymbolPatterns(fileExtension);

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      
      for (const pattern of patterns) {
        const matches = line.matchAll(pattern.regex);
        
        for (const match of matches) {
          if (match.groups) {
            const symbol: CodeSymbol = {
              name: match.groups.name,
              type: pattern.type,
              filePath,
              line: lineIndex + 1,
              column: match.index || 0,
              signature: match[0],
              scope: this.determineScope(lines, lineIndex)
            };
            
            // Extract documentation if available
            symbol.documentation = this.extractDocumentation(lines, lineIndex);
            
            symbols.push(symbol);
          }
        }
      }
    }

    return symbols;
  }

  /**
   * Get symbol extraction patterns for different file types
   */
  private getSymbolPatterns(fileExtension: string): Array<{ regex: RegExp; type: CodeSymbol['type'] }> {
    const patterns: Array<{ regex: RegExp; type: CodeSymbol['type'] }> = [];

    switch (fileExtension) {
      case '.ts':
      case '.js':
        patterns.push(
          { regex: /class\s+(?<name>\w+)/g, type: 'class' },
          { regex: /interface\s+(?<name>\w+)/g, type: 'interface' },
          { regex: /function\s+(?<name>\w+)/g, type: 'function' },
          { regex: /(?:const|let|var)\s+(?<name>\w+)\s*=/g, type: 'variable' },
          { regex: /enum\s+(?<name>\w+)/g, type: 'enum' },
          { regex: /type\s+(?<name>\w+)/g, type: 'type' }
        );
        break;
      
      case '.py':
        patterns.push(
          { regex: /class\s+(?<name>\w+)/g, type: 'class' },
          { regex: /def\s+(?<name>\w+)/g, type: 'function' },
          { regex: /(?<name>\w+)\s*=/g, type: 'variable' }
        );
        break;
      
      case '.java':
        patterns.push(
          { regex: /class\s+(?<name>\w+)/g, type: 'class' },
          { regex: /interface\s+(?<name>\w+)/g, type: 'interface' },
          { regex: /(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(?<name>\w+)\s*\(/g, type: 'function' }
        );
        break;
    }

    return patterns;
  }

  /**
   * Determine search intent from query text
   */
  private determineSearchIntent(query: string): SearchQuery['intent'] {
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('function') || lowerQuery.includes('method')) {
      return 'find_function';
    }
    if (lowerQuery.includes('class') || lowerQuery.includes('component')) {
      return 'find_class';
    }
    if (lowerQuery.includes('usage') || lowerQuery.includes('used') || lowerQuery.includes('called')) {
      return 'find_usage';
    }
    if (lowerQuery.includes('pattern') || lowerQuery.includes('similar')) {
      return 'find_pattern';
    }
    
    return 'general';
  }

  /**
   * Perform search based on query intent
   */
  private async performSearch(query: SearchQuery): Promise<CodeSearchResult[]> {
    const results: CodeSearchResult[] = [];

    switch (query.intent) {
      case 'find_function':
        results.push(...await this.searchFunctions(query.text));
        break;
      
      case 'find_class':
        results.push(...await this.searchClasses(query.text));
        break;
      
      case 'find_usage':
        results.push(...await this.searchUsages(query.text));
        break;
      
      case 'find_pattern':
        results.push(...await this.searchPatterns(query.text));
        break;
      
      default:
        results.push(...await this.searchGeneral(query.text));
        break;
    }

    return results;
  }

  /**
   * Search for functions
   */
  private async searchFunctions(query: string): Promise<CodeSearchResult[]> {
    const results: CodeSearchResult[] = [];
    const queryTerms = query.toLowerCase().split(/\s+/);

    for (const [filePath, symbols] of this.symbolIndex) {
      const functions = symbols.filter(s => s.type === 'function');
      
      for (const func of functions) {
        const score = this.calculateRelevanceScore(func.name, queryTerms);
        
        if (score > 0.3) {
          const content = this.fileIndex.get(filePath) || '';
          const context = this.extractSymbolContext(content, func);
          
          results.push({
            filePath,
            content: context,
            relevanceScore: score,
            matchedLines: [func.line],
            context: `Function: ${func.name}`,
            symbols: [func],
            explanation: `Found function "${func.name}" matching your query`
          });
        }
      }
    }

    return results;
  }

  /**
   * Search for classes
   */
  private async searchClasses(query: string): Promise<CodeSearchResult[]> {
    const results: CodeSearchResult[] = [];
    const queryTerms = query.toLowerCase().split(/\s+/);

    for (const [filePath, symbols] of this.symbolIndex) {
      const classes = symbols.filter(s => s.type === 'class' || s.type === 'interface');
      
      for (const cls of classes) {
        const score = this.calculateRelevanceScore(cls.name, queryTerms);
        
        if (score > 0.3) {
          const content = this.fileIndex.get(filePath) || '';
          const context = this.extractSymbolContext(content, cls);
          
          results.push({
            filePath,
            content: context,
            relevanceScore: score,
            matchedLines: [cls.line],
            context: `${cls.type}: ${cls.name}`,
            symbols: [cls],
            explanation: `Found ${cls.type} "${cls.name}" matching your query`
          });
        }
      }
    }

    return results;
  }

  /**
   * Search for general patterns
   */
  private async searchGeneral(query: string): Promise<CodeSearchResult[]> {
    const results: CodeSearchResult[] = [];
    const queryTerms = query.toLowerCase().split(/\s+/);

    for (const [filePath, content] of this.fileIndex) {
      const lines = content.split('\n');
      const matchedLines: number[] = [];
      let totalScore = 0;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();
        const lineScore = this.calculateLineRelevanceScore(line, queryTerms);
        
        if (lineScore > 0.2) {
          matchedLines.push(i + 1);
          totalScore += lineScore;
        }
      }

      if (matchedLines.length > 0) {
        const avgScore = totalScore / matchedLines.length;
        const context = this.extractLinesContext(lines, matchedLines);
        const symbols = this.symbolIndex.get(filePath) || [];

        results.push({
          filePath,
          content: context,
          relevanceScore: avgScore,
          matchedLines,
          context: `${matchedLines.length} matches found`,
          symbols: symbols.filter(s => matchedLines.includes(s.line)),
          explanation: `Found ${matchedLines.length} relevant lines in ${path.basename(filePath)}`
        });
      }
    }

    return results;
  }

  /**
   * Calculate relevance score for symbol name
   */
  private calculateRelevanceScore(symbolName: string, queryTerms: string[]): number {
    const name = symbolName.toLowerCase();
    let score = 0;

    for (const term of queryTerms) {
      if (name === term) {
        score += 1.0;
      } else if (name.includes(term)) {
        score += 0.7;
      } else if (this.fuzzyMatch(name, term)) {
        score += 0.4;
      }
    }

    return Math.min(score / queryTerms.length, 1.0);
  }

  /**
   * Calculate relevance score for a line of code
   */
  private calculateLineRelevanceScore(line: string, queryTerms: string[]): number {
    let score = 0;

    for (const term of queryTerms) {
      if (line.includes(term)) {
        score += 0.5;
      }
    }

    return Math.min(score / queryTerms.length, 1.0);
  }

  /**
   * Simple fuzzy matching
   */
  private fuzzyMatch(text: string, pattern: string): boolean {
    let patternIndex = 0;
    
    for (let i = 0; i < text.length && patternIndex < pattern.length; i++) {
      if (text[i] === pattern[patternIndex]) {
        patternIndex++;
      }
    }
    
    return patternIndex === pattern.length;
  }

  /**
   * Utility methods
   */
  private async getCodeFiles(): Promise<string[]> {
    const files: string[] = [];
    const extensions = ['.ts', '.js', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs'];
    
    await this.walkDirectory(this.workspaceRoot, files, extensions);
    return files;
  }

  private async walkDirectory(dir: string, files: string[], extensions: string[]): Promise<void> {
    try {
      const entries = await readdir(dir);
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry);
        const stats = await stat(fullPath);
        
        if (stats.isDirectory() && !this.shouldSkipDirectory(entry)) {
          await this.walkDirectory(fullPath, files, extensions);
        } else if (stats.isFile() && extensions.includes(path.extname(entry))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Failed to read directory ${dir}:`, error);
    }
  }

  private shouldSkipDirectory(dirName: string): boolean {
    const skipDirs = ['node_modules', '.git', 'dist', 'build', '.vscode', 'coverage'];
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }

  private async getFileContent(filePath: string): Promise<string> {
    try {
      return await readFile(filePath, 'utf8');
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private generateSimpleEmbedding(content: string): number[] {
    // Simplified embedding generation - in real implementation would use ML model
    const words = content.toLowerCase().match(/\w+/g) || [];
    const embedding = new Array(100).fill(0);
    
    for (let i = 0; i < words.length && i < 100; i++) {
      embedding[i] = words[i].charCodeAt(0) / 255;
    }
    
    return embedding;
  }

  private isIndexed(): boolean {
    return this.embeddings.size > 0;
  }

  private initializeWatchers(): void {
    // Watch for file changes and update index
    const watcher = vscode.workspace.createFileSystemWatcher('**/*.{ts,js,py,java}');
    
    watcher.onDidChange(uri => {
      this.indexFile(uri.fsPath);
    });
    
    watcher.onDidDelete(uri => {
      this.embeddings.delete(uri.fsPath);
      this.symbolIndex.delete(uri.fsPath);
      this.fileIndex.delete(uri.fsPath);
    });
  }

  private extractContext(content: string, position: vscode.Position, contextLines: number): string {
    const lines = content.split('\n');
    const start = Math.max(0, position.line - contextLines);
    const end = Math.min(lines.length, position.line + contextLines + 1);
    
    return lines.slice(start, end).join('\n');
  }

  private extractSymbolContext(content: string, symbol: CodeSymbol): string {
    const lines = content.split('\n');
    const start = Math.max(0, symbol.line - 3);
    const end = Math.min(lines.length, symbol.line + 3);
    
    return lines.slice(start, end).join('\n');
  }

  private extractLinesContext(lines: string[], matchedLines: number[]): string {
    const contextLines: string[] = [];
    
    for (const lineNum of matchedLines.slice(0, 5)) { // Limit to first 5 matches
      const start = Math.max(0, lineNum - 2);
      const end = Math.min(lines.length, lineNum + 2);
      
      for (let i = start; i < end; i++) {
        if (!contextLines.includes(lines[i])) {
          contextLines.push(lines[i]);
        }
      }
    }
    
    return contextLines.join('\n');
  }

  private determineScope(lines: string[], lineIndex: number): string {
    // Simple scope determination - can be enhanced
    for (let i = lineIndex - 1; i >= 0; i--) {
      const line = lines[i].trim();
      if (line.includes('class ') || line.includes('function ') || line.includes('namespace ')) {
        return line;
      }
    }
    return 'global';
  }

  private extractDocumentation(lines: string[], lineIndex: number): string | undefined {
    // Look for JSDoc or similar documentation above the symbol
    let docLines: string[] = [];
    
    for (let i = lineIndex - 1; i >= 0; i--) {
      const line = lines[i].trim();
      
      if (line.startsWith('/**') || line.startsWith('/*') || line.startsWith('*') || line.startsWith('//')) {
        docLines.unshift(line);
      } else if (line === '' || line.startsWith('*')) {
        continue;
      } else {
        break;
      }
    }
    
    return docLines.length > 0 ? docLines.join('\n') : undefined;
  }

  private rankResults(results: CodeSearchResult[], query: SearchQuery): CodeSearchResult[] {
    return results.sort((a, b) => {
      // Primary sort by relevance score
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore;
      }
      
      // Secondary sort by number of symbols
      return b.symbols.length - a.symbols.length;
    });
  }

  private reRankWithContext(
    results: CodeSearchResult[],
    currentFilePath: string,
    context: string
  ): CodeSearchResult[] {
    // Boost results from same directory or related files
    return results.map(result => {
      let boost = 0;
      
      if (path.dirname(result.filePath) === path.dirname(currentFilePath)) {
        boost += 0.2;
      }
      
      if (result.content.toLowerCase().includes(context.toLowerCase())) {
        boost += 0.1;
      }
      
      return {
        ...result,
        relevanceScore: Math.min(result.relevanceScore + boost, 1.0)
      };
    }).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  private async searchUsages(query: string): Promise<CodeSearchResult[]> {
    const results: CodeSearchResult[] = [];
    const symbolName = this.extractSymbolName(query);

    if (!symbolName) {
      return results;
    }

    // Find all usages of the symbol
    for (const [filePath, content] of this.fileIndex) {
      const lines = content.split('\n');
      const matchedLines: number[] = [];

      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes(symbolName)) {
          matchedLines.push(i + 1);
        }
      }

      if (matchedLines.length > 0) {
        const context = this.extractLinesContext(lines, matchedLines);
        const symbols = this.symbolIndex.get(filePath) || [];

        results.push({
          filePath,
          content: context,
          relevanceScore: 0.8,
          matchedLines,
          context: `${matchedLines.length} usages of ${symbolName}`,
          symbols: symbols.filter(s => s.name === symbolName),
          explanation: `Found ${matchedLines.length} usages of "${symbolName}" in ${path.basename(filePath)}`
        });
      }
    }

    return results;
  }

  private async searchPatterns(query: string): Promise<CodeSearchResult[]> {
    const results: CodeSearchResult[] = [];
    const patterns = this.extractPatterns(query);

    for (const pattern of patterns) {
      const patternResults = await this.findSimilarPatterns(pattern);
      results.push(...patternResults);
    }

    return results;
  }

  /**
   * Enhanced context-aware search with machine learning-based ranking
   */
  async searchWithContext(
    query: SearchQuery,
    currentFile?: string,
    recentFiles?: string[],
    userActivity?: { files: string[]; symbols: string[] }
  ): Promise<CodeSearchResult[]> {
    // Get base search results
    const baseResults = await this.searchCodebase(query);

    // Apply context-aware ranking
    const contextRankedResults = this.applyContextualRanking(
      baseResults,
      currentFile,
      recentFiles,
      userActivity
    );

    // Apply intent-based filtering
    const intentFilteredResults = this.applyIntentFiltering(contextRankedResults, query);

    // Apply relevance boosting
    const boostedResults = this.applyRelevanceBoosting(intentFilteredResults, query);

    return boostedResults;
  }

  /**
   * Apply contextual ranking based on current development context
   */
  private applyContextualRanking(
    results: CodeSearchResult[],
    currentFile?: string,
    recentFiles?: string[],
    userActivity?: { files: string[]; symbols: string[] }
  ): CodeSearchResult[] {
    return results.map(result => {
      let contextBoost = 0;

      // Boost results from current file
      if (currentFile && result.filePath === currentFile) {
        contextBoost += 0.3;
      }

      // Boost results from recently accessed files
      if (recentFiles && recentFiles.includes(result.filePath)) {
        contextBoost += 0.2;
      }

      // Boost results from files in same directory as current file
      if (currentFile && path.dirname(result.filePath) === path.dirname(currentFile)) {
        contextBoost += 0.15;
      }

      // Boost results containing recently used symbols
      if (userActivity?.symbols) {
        for (const symbol of result.symbols) {
          if (userActivity.symbols.includes(symbol.name)) {
            contextBoost += 0.1;
          }
        }
      }

      // Boost results from frequently accessed files
      if (userActivity?.files) {
        const fileFrequency = userActivity.files.filter(f => f === result.filePath).length;
        contextBoost += Math.min(fileFrequency * 0.05, 0.2);
      }

      return {
        ...result,
        relevanceScore: Math.min(result.relevanceScore + contextBoost, 1.0)
      };
    }).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Apply intent-based filtering to prioritize relevant results
   */
  private applyIntentFiltering(results: CodeSearchResult[], query: SearchQuery): CodeSearchResult[] {
    switch (query.intent) {
      case 'find_function':
        return results.filter(r => r.symbols.some(s => s.type === 'function'));

      case 'find_class':
        return results.filter(r => r.symbols.some(s => s.type === 'class' || s.type === 'interface'));

      case 'find_usage':
        return results.filter(r => r.matchedLines.length > 1); // Multiple usages

      default:
        return results;
    }
  }

  /**
   * Apply relevance boosting based on query analysis
   */
  private applyRelevanceBoosting(results: CodeSearchResult[], query: SearchQuery): CodeSearchResult[] {
    const queryTerms = query.text.toLowerCase().split(/\s+/);

    return results.map(result => {
      let boost = 0;

      // Boost exact matches in symbol names
      for (const symbol of result.symbols) {
        if (queryTerms.some(term => symbol.name.toLowerCase() === term)) {
          boost += 0.2;
        }
      }

      // Boost results with documentation matching query
      for (const symbol of result.symbols) {
        if (symbol.documentation) {
          const docLower = symbol.documentation.toLowerCase();
          const matchingTerms = queryTerms.filter(term => docLower.includes(term));
          boost += matchingTerms.length * 0.05;
        }
      }

      // Boost results from test files if query suggests testing
      if (query.text.toLowerCase().includes('test') && result.filePath.includes('test')) {
        boost += 0.1;
      }

      return {
        ...result,
        relevanceScore: Math.min(result.relevanceScore + boost, 1.0)
      };
    });
  }

  /**
   * Provide explanations for why results were selected
   */
  private generateResultExplanation(result: CodeSearchResult, query: SearchQuery): string {
    const reasons: string[] = [];

    // Symbol matches
    const matchingSymbols = result.symbols.filter(s =>
      query.text.toLowerCase().includes(s.name.toLowerCase())
    );

    if (matchingSymbols.length > 0) {
      reasons.push(`Contains symbols: ${matchingSymbols.map(s => s.name).join(', ')}`);
    }

    // Content matches
    if (result.matchedLines.length > 0) {
      reasons.push(`${result.matchedLines.length} relevant lines found`);
    }

    // High relevance score
    if (result.relevanceScore > 0.8) {
      reasons.push('High relevance match');
    }

    // File type relevance
    const fileExt = path.extname(result.filePath);
    if (query.fileTypes && query.fileTypes.includes(fileExt)) {
      reasons.push(`Matches requested file type (${fileExt})`);
    }

    return reasons.length > 0
      ? `Selected because: ${reasons.join('; ')}`
      : 'General content match';
  }

  /**
   * Learn from user interactions to improve future searches
   */
  async recordUserInteraction(
    query: SearchQuery,
    selectedResult: CodeSearchResult,
    interactionType: 'clicked' | 'opened' | 'copied' | 'dismissed'
  ): Promise<void> {
    // Store interaction data for machine learning
    const interaction = {
      query: query.text,
      intent: query.intent,
      selectedFile: selectedResult.filePath,
      selectedSymbols: selectedResult.symbols.map(s => s.name),
      interactionType,
      timestamp: new Date(),
      relevanceScore: selectedResult.relevanceScore
    };

    // In a real implementation, this would be stored in a database
    // and used to train a ranking model
    console.log('User interaction recorded:', interaction);
  }

  /**
   * Get search suggestions based on current context
   */
  async getSearchSuggestions(
    partialQuery: string,
    currentFile?: string,
    maxSuggestions: number = 5
  ): Promise<string[]> {
    const suggestions: string[] = [];

    // Symbol name suggestions
    const symbolSuggestions = await this.getSymbolSuggestions(partialQuery);
    suggestions.push(...symbolSuggestions.slice(0, 3));

    // Pattern suggestions based on current file
    if (currentFile) {
      const patternSuggestions = await this.getPatternSuggestions(partialQuery, currentFile);
      suggestions.push(...patternSuggestions.slice(0, 2));
    }

    return suggestions.slice(0, maxSuggestions);
  }

  /**
   * Get symbol name suggestions
   */
  private async getSymbolSuggestions(partialQuery: string): Promise<string[]> {
    const suggestions: string[] = [];
    const lowerQuery = partialQuery.toLowerCase();

    for (const symbols of this.symbolIndex.values()) {
      for (const symbol of symbols) {
        if (symbol.name.toLowerCase().startsWith(lowerQuery)) {
          suggestions.push(symbol.name);
        }
      }
    }

    return [...new Set(suggestions)].sort();
  }

  /**
   * Get pattern suggestions based on current file
   */
  private async getPatternSuggestions(partialQuery: string, currentFile: string): Promise<string[]> {
    const suggestions: string[] = [];

    // Analyze current file for common patterns
    const content = this.fileIndex.get(currentFile);
    if (content) {
      const patterns = this.extractCommonPatterns(content);

      for (const pattern of patterns) {
        if (pattern.toLowerCase().includes(partialQuery.toLowerCase())) {
          suggestions.push(pattern);
        }
      }
    }

    return suggestions;
  }

  /**
   * Extract common patterns from code
   */
  private extractCommonPatterns(content: string): string[] {
    const patterns: string[] = [];
    const lines = content.split('\n');

    // Extract import patterns
    const imports = lines.filter(line => line.trim().startsWith('import'));
    patterns.push(...imports.map(imp => imp.trim()));

    // Extract function call patterns
    const functionCalls = content.match(/\w+\([^)]*\)/g) || [];
    patterns.push(...functionCalls.slice(0, 10));

    return [...new Set(patterns)];
  }

  /**
   * Utility methods for pattern extraction
   */
  private extractSymbolName(query: string): string | null {
    // Extract symbol name from usage query
    const match = query.match(/(?:usage of|uses of|find)\s+(\w+)/i);
    return match ? match[1] : null;
  }

  private extractPatterns(query: string): string[] {
    // Extract patterns from pattern query
    const patterns: string[] = [];

    // Look for quoted patterns
    const quotedPatterns = query.match(/"([^"]+)"/g);
    if (quotedPatterns) {
      patterns.push(...quotedPatterns.map(p => p.slice(1, -1)));
    }

    // Look for code-like patterns
    const codePatterns = query.match(/\w+\([^)]*\)/g);
    if (codePatterns) {
      patterns.push(...codePatterns);
    }

    return patterns;
  }

  private async findSimilarPatterns(pattern: string): Promise<CodeSearchResult[]> {
    const results: CodeSearchResult[] = [];

    for (const [filePath, content] of this.fileIndex) {
      if (content.includes(pattern)) {
        const lines = content.split('\n');
        const matchedLines: number[] = [];

        for (let i = 0; i < lines.length; i++) {
          if (lines[i].includes(pattern)) {
            matchedLines.push(i + 1);
          }
        }

        if (matchedLines.length > 0) {
          const context = this.extractLinesContext(lines, matchedLines);

          results.push({
            filePath,
            content: context,
            relevanceScore: 0.7,
            matchedLines,
            context: `Pattern: ${pattern}`,
            symbols: [],
            explanation: `Found pattern "${pattern}" in ${path.basename(filePath)}`
          });
        }
      }
    }

    return results;
  }
}
