(function() {
  const vscode = acquireVsCodeApi();
  
  // State management
  let currentSearchTerm = '';
  let searchResults = [];
  let currentSearchIndex = 0;
  
  // Initialize the panel
  document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeCollapsibleSections();
    initializeSearch();
    highlightWordLevelChanges();
  });
  
  function initializeEventListeners() {
    // Accept/Reject buttons
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('accept-edit')) {
        const editIndex = parseInt(e.target.dataset.editIndex);
        vscode.postMessage({
          command: 'acceptEdit',
          editIndex: editIndex
        });
      }
      
      if (e.target.classList.contains('reject-edit')) {
        const editIndex = parseInt(e.target.dataset.editIndex);
        vscode.postMessage({
          command: 'rejectEdit',
          editIndex: editIndex
        });
      }
      
      if (e.target.classList.contains('show-file')) {
        const filePath = e.target.dataset.filePath;
        vscode.postMessage({
          command: 'showFile',
          filePath: filePath
        });
      }
    });
    
    // Accept/Reject all buttons
    const acceptAllBtn = document.getElementById('acceptAll');
    const rejectAllBtn = document.getElementById('rejectAll');
    
    if (acceptAllBtn) {
      acceptAllBtn.addEventListener('click', function() {
        vscode.postMessage({ command: 'acceptAll' });
      });
    }
    
    if (rejectAllBtn) {
      rejectAllBtn.addEventListener('click', function() {
        vscode.postMessage({ command: 'rejectAll' });
      });
    }
    
    // Line number clicks for navigation
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('line-number')) {
        const lineNumber = parseInt(e.target.textContent.trim());
        const editPreview = e.target.closest('.edit-preview');
        const filePath = editPreview?.dataset.filePath;
        
        if (filePath && lineNumber > 0) {
          vscode.postMessage({
            command: 'showFile',
            filePath: filePath,
            lineNumber: lineNumber
          });
        }
      }
    });
  }
  
  function initializeCollapsibleSections() {
    // Create collapsible sections for large diffs
    const diffContainers = document.querySelectorAll('.diff-container');
    
    diffContainers.forEach(container => {
      const diffLines = container.querySelectorAll('.diff-line');
      
      if (diffLines.length > 50) {
        createCollapsibleSections(container, diffLines);
      }
    });
  }
  
  function createCollapsibleSections(container, diffLines) {
    const sections = [];
    let currentSection = [];
    let unchangedCount = 0;
    
    diffLines.forEach((line, index) => {
      if (line.classList.contains('diff-unchanged')) {
        unchangedCount++;
        currentSection.push(line);
        
        // If we have many unchanged lines, create a collapsible section
        if (unchangedCount > 10 && currentSection.length > 15) {
          sections.push({
            type: 'collapsible',
            lines: [...currentSection],
            startIndex: index - currentSection.length + 1,
            endIndex: index
          });
          currentSection = [];
          unchangedCount = 0;
        }
      } else {
        if (currentSection.length > 0) {
          sections.push({
            type: 'normal',
            lines: [...currentSection]
          });
          currentSection = [];
          unchangedCount = 0;
        }
        sections.push({
          type: 'normal',
          lines: [line]
        });
      }
    });
    
    // Handle remaining lines
    if (currentSection.length > 0) {
      sections.push({
        type: 'normal',
        lines: currentSection
      });
    }
    
    // Rebuild the container with collapsible sections
    rebuildWithCollapsibleSections(container, sections);
  }
  
  function rebuildWithCollapsibleSections(container, sections) {
    container.innerHTML = '';
    
    sections.forEach((section, sectionIndex) => {
      if (section.type === 'collapsible') {
        const collapsibleDiv = document.createElement('div');
        collapsibleDiv.className = 'collapsible-section collapsed';
        
        const header = document.createElement('div');
        header.className = 'collapsible-header';
        header.innerHTML = `
          <span>Show ${section.lines.length} unchanged lines</span>
          <span class="collapse-indicator">▼</span>
        `;
        
        const content = document.createElement('div');
        content.className = 'collapsible-content';
        
        section.lines.forEach(line => {
          content.appendChild(line.cloneNode(true));
        });
        
        header.addEventListener('click', function() {
          const isCollapsed = collapsibleDiv.classList.contains('collapsed');
          
          if (isCollapsed) {
            collapsibleDiv.classList.remove('collapsed');
            content.classList.add('expanded');
            header.querySelector('span').textContent = `Hide ${section.lines.length} unchanged lines`;
          } else {
            collapsibleDiv.classList.add('collapsed');
            content.classList.remove('expanded');
            header.querySelector('span').textContent = `Show ${section.lines.length} unchanged lines`;
          }
        });
        
        collapsibleDiv.appendChild(header);
        collapsibleDiv.appendChild(content);
        container.appendChild(collapsibleDiv);
      } else {
        section.lines.forEach(line => {
          container.appendChild(line.cloneNode(true));
        });
      }
    });
  }
  
  function initializeSearch() {
    // Add search functionality
    const diffContainers = document.querySelectorAll('.diff-container');
    
    diffContainers.forEach(container => {
      const searchContainer = document.createElement('div');
      searchContainer.className = 'search-container';
      searchContainer.innerHTML = `
        <input type="text" class="search-input" placeholder="Search in diff...">
        <div class="search-results"></div>
      `;
      
      container.parentNode.insertBefore(searchContainer, container);
      
      const searchInput = searchContainer.querySelector('.search-input');
      const searchResults = searchContainer.querySelector('.search-results');
      
      searchInput.addEventListener('input', function() {
        performSearch(this.value, container, searchResults);
      });
      
      searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
          if (e.shiftKey) {
            navigateSearchResults(-1, container);
          } else {
            navigateSearchResults(1, container);
          }
        }
      });
    });
  }
  
  function performSearch(term, container, resultsElement) {
    // Clear previous highlights
    clearSearchHighlights(container);
    
    if (!term.trim()) {
      resultsElement.textContent = '';
      return;
    }
    
    currentSearchTerm = term.toLowerCase();
    searchResults = [];
    currentSearchIndex = 0;
    
    const diffLines = container.querySelectorAll('.diff-line');
    
    diffLines.forEach((line, lineIndex) => {
      const content = line.querySelector('.line-content');
      if (content) {
        const text = content.textContent.toLowerCase();
        let startIndex = 0;
        
        while (true) {
          const index = text.indexOf(currentSearchTerm, startIndex);
          if (index === -1) break;
          
          searchResults.push({
            lineIndex: lineIndex,
            element: line,
            startIndex: index,
            endIndex: index + currentSearchTerm.length
          });
          
          startIndex = index + 1;
        }
      }
    });
    
    // Highlight all results
    highlightSearchResults(container);
    
    // Update results display
    if (searchResults.length > 0) {
      resultsElement.textContent = `${searchResults.length} results`;
      highlightCurrentResult();
    } else {
      resultsElement.textContent = 'No results';
    }
  }
  
  function highlightSearchResults(container) {
    searchResults.forEach((result, index) => {
      const content = result.element.querySelector('.line-content');
      if (content) {
        const text = content.textContent;
        const beforeText = text.substring(0, result.startIndex);
        const matchText = text.substring(result.startIndex, result.endIndex);
        const afterText = text.substring(result.endIndex);
        
        const highlightSpan = document.createElement('span');
        highlightSpan.className = 'search-highlight';
        highlightSpan.textContent = matchText;
        highlightSpan.dataset.searchIndex = index;
        
        content.innerHTML = '';
        content.appendChild(document.createTextNode(beforeText));
        content.appendChild(highlightSpan);
        content.appendChild(document.createTextNode(afterText));
      }
    });
  }
  
  function clearSearchHighlights(container) {
    const highlights = container.querySelectorAll('.search-highlight');
    highlights.forEach(highlight => {
      const parent = highlight.parentNode;
      parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
      parent.normalize();
    });
  }
  
  function navigateSearchResults(direction, container) {
    if (searchResults.length === 0) return;
    
    currentSearchIndex += direction;
    
    if (currentSearchIndex >= searchResults.length) {
      currentSearchIndex = 0;
    } else if (currentSearchIndex < 0) {
      currentSearchIndex = searchResults.length - 1;
    }
    
    highlightCurrentResult();
    scrollToCurrentResult(container);
  }
  
  function highlightCurrentResult() {
    // Remove current highlight
    document.querySelectorAll('.search-highlight.current').forEach(el => {
      el.classList.remove('current');
    });
    
    // Add current highlight
    if (searchResults.length > 0) {
      const currentResult = searchResults[currentSearchIndex];
      const highlight = currentResult.element.querySelector(`[data-search-index="${currentSearchIndex}"]`);
      if (highlight) {
        highlight.classList.add('current');
      }
    }
  }
  
  function scrollToCurrentResult(container) {
    if (searchResults.length > 0) {
      const currentResult = searchResults[currentSearchIndex];
      currentResult.element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }
  
  function highlightWordLevelChanges() {
    // Enhance diff lines with word-level change highlighting
    const diffLines = document.querySelectorAll('.diff-line');
    
    for (let i = 0; i < diffLines.length - 1; i++) {
      const currentLine = diffLines[i];
      const nextLine = diffLines[i + 1];
      
      if (currentLine.classList.contains('diff-removed') && 
          nextLine.classList.contains('diff-added')) {
        
        const removedContent = currentLine.querySelector('.line-content');
        const addedContent = nextLine.querySelector('.line-content');
        
        if (removedContent && addedContent) {
          const removedText = removedContent.textContent;
          const addedText = addedContent.textContent;
          
          const wordDiff = computeWordDiff(removedText, addedText);
          
          if (wordDiff.hasChanges) {
            removedContent.innerHTML = wordDiff.removedHtml;
            addedContent.innerHTML = wordDiff.addedHtml;
          }
        }
      }
    }
  }
  
  function computeWordDiff(oldText, newText) {
    const oldWords = oldText.split(/(\s+)/);
    const newWords = newText.split(/(\s+)/);
    
    // Simple word-level diff algorithm
    const removedHtml = [];
    const addedHtml = [];
    let hasChanges = false;
    
    let oldIndex = 0;
    let newIndex = 0;
    
    while (oldIndex < oldWords.length || newIndex < newWords.length) {
      const oldWord = oldWords[oldIndex];
      const newWord = newWords[newIndex];
      
      if (oldWord === newWord) {
        removedHtml.push(escapeHtml(oldWord || ''));
        addedHtml.push(escapeHtml(newWord || ''));
        oldIndex++;
        newIndex++;
      } else {
        // Find next matching word
        let foundMatch = false;
        
        for (let i = newIndex + 1; i < newWords.length; i++) {
          if (oldWord === newWords[i]) {
            // Words were added
            for (let j = newIndex; j < i; j++) {
              addedHtml.push(`<span class="word-added">${escapeHtml(newWords[j])}</span>`);
            }
            addedHtml.push(escapeHtml(oldWord));
            removedHtml.push(escapeHtml(oldWord));
            newIndex = i + 1;
            oldIndex++;
            foundMatch = true;
            hasChanges = true;
            break;
          }
        }
        
        if (!foundMatch) {
          for (let i = oldIndex + 1; i < oldWords.length; i++) {
            if (newWord === oldWords[i]) {
              // Words were removed
              for (let j = oldIndex; j < i; j++) {
                removedHtml.push(`<span class="word-removed">${escapeHtml(oldWords[j])}</span>`);
              }
              removedHtml.push(escapeHtml(newWord));
              addedHtml.push(escapeHtml(newWord));
              oldIndex = i + 1;
              newIndex++;
              foundMatch = true;
              hasChanges = true;
              break;
            }
          }
        }
        
        if (!foundMatch) {
          // Words are different
          if (oldWord) {
            removedHtml.push(`<span class="word-removed">${escapeHtml(oldWord)}</span>`);
            oldIndex++;
          }
          if (newWord) {
            addedHtml.push(`<span class="word-added">${escapeHtml(newWord)}</span>`);
            newIndex++;
          }
          hasChanges = true;
        }
      }
    }
    
    return {
      hasChanges,
      removedHtml: removedHtml.join(''),
      addedHtml: addedHtml.join('')
    };
  }
  
  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
  
  // Handle messages from the extension
  window.addEventListener('message', event => {
    const message = event.data;
    
    switch (message.command) {
      case 'updatePreview':
        // Handle preview updates
        location.reload();
        break;
    }
  });
})();
