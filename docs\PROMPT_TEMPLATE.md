# Codebase Context Prompt

Before responding to the user's request, please perform the following steps to ensure you have full context of the codebase:

1. **Set Project Path** (if not already set):
   ```
   <mcp1_set_project_path>
   {"path": "c:\\xampp\\htdocs\\vidyadhara"}
   </mcp1_set_project_path>
   ```

2. **Refresh Index** (if needed):
   ```
   <mcp1_refresh_index>{}</mcp1_refresh_index>
   ```

3. **Search for Relevant Files** (based on the user's request):
   ```
   <mcp1_search_code_advanced>
   {
     "pattern": "[search terms from user request]",
     "case_sensitive": false,
     "context_lines": 2,
     "file_pattern": "*.*",
     "fuzzy": true
   }
   </mcp1_search_code_advanced>
   ```

4. **Examine Key Files** (as needed):
   - Check package.json for dependencies and scripts
   - Review main entry points (e.g., extension.ts, main.js)
   - Check configuration files (webpack.config.js, tsconfig.json, etc.)
   - Review any files mentioned in the user's request

5. **Analyze Code Structure** (if needed):
   - Understand the project's architecture
   - Identify key components and their relationships
   - Note any relevant patterns or conventions

6. **Check for Recent Changes** (if applicable):
   ```
   <mcp1_find_files>
   {"pattern": "*.*", "type": "file", "max_depth": 3}
   </mcp1_find_files>
   ```

7. **Verify Dependencies** (if needed):
   - Check package.json for required dependencies
   - Verify any external service configurations

After gathering this context, proceed to address the user's specific request with full awareness of the codebase structure and content.
