import * as vscode from 'vscode';
import { Task, TaskState, TaskPriority } from './taskManager';

export interface ScheduledTask extends Task {
  scheduledStart?: Date;
  scheduledEnd?: Date;
  criticalPath: boolean;
  slack: number; // Available delay without affecting project completion
}

export interface ProjectSchedule {
  tasks: ScheduledTask[];
  criticalPath: string[];
  projectStart: Date;
  projectEnd: Date;
  totalDuration: number;
  milestones: Milestone[];
}

export interface Milestone {
  id: string;
  name: string;
  date: Date;
  taskIds: string[];
  description?: string;
}

export interface ResourceAllocation {
  assignee: string;
  tasks: Array<{
    taskId: string;
    startDate: Date;
    endDate: Date;
    hoursPerDay: number;
  }>;
  totalHours: number;
  utilization: number; // Percentage of available time
}

export interface SchedulingConflict {
  type: 'resource_overallocation' | 'dependency_violation' | 'deadline_miss';
  description: string;
  affectedTasks: string[];
  severity: 'low' | 'medium' | 'high';
  suggestions: string[];
}

export class TaskScheduler {
  private workingHoursPerDay = 8;
  private workingDaysPerWeek = 5;

  /**
   * Generate project schedule using Critical Path Method (CPM)
   */
  generateSchedule(
    tasks: Task[],
    projectStart: Date = new Date(),
    constraints?: {
      deadlines?: Map<string, Date>;
      resourceLimits?: Map<string, number>;
      workingHours?: number;
    }
  ): ProjectSchedule {
    // Build dependency graph
    const taskMap = new Map(tasks.map(task => [task.id, task]));
    const dependencyGraph = this.buildDependencyGraph(tasks);
    
    // Calculate early start/finish times (forward pass)
    const earlyTimes = this.calculateEarlyTimes(tasks, dependencyGraph, projectStart);
    
    // Calculate late start/finish times (backward pass)
    const lateTimes = this.calculateLateTimes(tasks, dependencyGraph, earlyTimes);
    
    // Identify critical path
    const criticalPath = this.findCriticalPath(tasks, earlyTimes, lateTimes);
    
    // Create scheduled tasks
    const scheduledTasks: ScheduledTask[] = tasks.map(task => ({
      ...task,
      scheduledStart: earlyTimes.get(task.id)?.start,
      scheduledEnd: earlyTimes.get(task.id)?.finish,
      criticalPath: criticalPath.includes(task.id),
      slack: this.calculateSlack(task.id, earlyTimes, lateTimes)
    }));

    // Apply resource constraints
    const resourceOptimizedTasks = this.optimizeResourceAllocation(scheduledTasks, constraints);
    
    // Calculate project end date
    const projectEnd = this.calculateProjectEnd(resourceOptimizedTasks);
    
    // Generate milestones
    const milestones = this.generateMilestones(resourceOptimizedTasks);

    return {
      tasks: resourceOptimizedTasks,
      criticalPath,
      projectStart,
      projectEnd,
      totalDuration: this.calculateDuration(projectStart, projectEnd),
      milestones
    };
  }

  /**
   * Detect scheduling conflicts
   */
  detectConflicts(schedule: ProjectSchedule, constraints?: {
    deadlines?: Map<string, Date>;
    resourceLimits?: Map<string, number>;
  }): SchedulingConflict[] {
    const conflicts: SchedulingConflict[] = [];

    // Check deadline violations
    if (constraints?.deadlines) {
      for (const [taskId, deadline] of constraints.deadlines) {
        const task = schedule.tasks.find(t => t.id === taskId);
        if (task?.scheduledEnd && task.scheduledEnd > deadline) {
          conflicts.push({
            type: 'deadline_miss',
            description: `Task "${task.name}" scheduled to finish after deadline`,
            affectedTasks: [taskId],
            severity: task.criticalPath ? 'high' : 'medium',
            suggestions: [
              'Reduce task duration',
              'Allocate more resources',
              'Negotiate deadline extension'
            ]
          });
        }
      }
    }

    // Check resource overallocation
    const resourceAllocations = this.calculateResourceAllocations(schedule);
    for (const allocation of resourceAllocations) {
      if (allocation.utilization > 100) {
        const overallocatedTasks = allocation.tasks.map(t => t.taskId);
        conflicts.push({
          type: 'resource_overallocation',
          description: `${allocation.assignee} is overallocated (${allocation.utilization.toFixed(1)}%)`,
          affectedTasks: overallocatedTasks,
          severity: allocation.utilization > 150 ? 'high' : 'medium',
          suggestions: [
            'Redistribute tasks to other team members',
            'Extend task durations',
            'Hire additional resources'
          ]
        });
      }
    }

    // Check dependency violations
    for (const task of schedule.tasks) {
      for (const depId of task.dependencies) {
        const dependency = schedule.tasks.find(t => t.id === depId);
        if (dependency?.scheduledEnd && task.scheduledStart && 
            dependency.scheduledEnd > task.scheduledStart) {
          conflicts.push({
            type: 'dependency_violation',
            description: `Task "${task.name}" starts before dependency "${dependency.name}" finishes`,
            affectedTasks: [task.id, depId],
            severity: 'high',
            suggestions: [
              'Adjust task start dates',
              'Remove or modify dependency',
              'Allow parallel execution where possible'
            ]
          });
        }
      }
    }

    return conflicts.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  /**
   * Optimize schedule to resolve conflicts
   */
  optimizeSchedule(
    schedule: ProjectSchedule,
    conflicts: SchedulingConflict[]
  ): ProjectSchedule {
    let optimizedSchedule = { ...schedule };

    // Sort conflicts by severity
    const sortedConflicts = conflicts.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });

    for (const conflict of sortedConflicts) {
      switch (conflict.type) {
        case 'resource_overallocation':
          optimizedSchedule = this.resolveResourceConflict(optimizedSchedule, conflict);
          break;
        case 'dependency_violation':
          optimizedSchedule = this.resolveDependencyConflict(optimizedSchedule, conflict);
          break;
        case 'deadline_miss':
          optimizedSchedule = this.resolveDeadlineConflict(optimizedSchedule, conflict);
          break;
      }
    }

    return optimizedSchedule;
  }

  /**
   * Calculate resource allocations
   */
  calculateResourceAllocations(schedule: ProjectSchedule): ResourceAllocation[] {
    const allocations = new Map<string, ResourceAllocation>();

    for (const task of schedule.tasks) {
      if (!task.assignee || !task.scheduledStart || !task.scheduledEnd) continue;

      if (!allocations.has(task.assignee)) {
        allocations.set(task.assignee, {
          assignee: task.assignee,
          tasks: [],
          totalHours: 0,
          utilization: 0
        });
      }

      const allocation = allocations.get(task.assignee)!;
      const duration = this.calculateDuration(task.scheduledStart, task.scheduledEnd);
      const hoursPerDay = (task.estimatedHours || duration * this.workingHoursPerDay) / duration;

      allocation.tasks.push({
        taskId: task.id,
        startDate: task.scheduledStart,
        endDate: task.scheduledEnd,
        hoursPerDay
      });

      allocation.totalHours += task.estimatedHours || duration * this.workingHoursPerDay;
    }

    // Calculate utilization
    for (const allocation of allocations.values()) {
      const totalAvailableHours = this.calculateAvailableHours(allocation.tasks);
      allocation.utilization = (allocation.totalHours / totalAvailableHours) * 100;
    }

    return Array.from(allocations.values());
  }

  /**
   * Generate Gantt chart data
   */
  generateGanttData(schedule: ProjectSchedule): Array<{
    taskId: string;
    taskName: string;
    startDate: Date;
    endDate: Date;
    progress: number;
    dependencies: string[];
    assignee?: string;
    criticalPath: boolean;
  }> {
    return schedule.tasks
      .filter(task => task.scheduledStart && task.scheduledEnd)
      .map(task => ({
        taskId: task.id,
        taskName: task.name,
        startDate: task.scheduledStart!,
        endDate: task.scheduledEnd!,
        progress: this.getTaskProgress(task),
        dependencies: task.dependencies,
        assignee: task.assignee,
        criticalPath: task.criticalPath
      }));
  }

  /**
   * Private helper methods
   */
  private buildDependencyGraph(tasks: Task[]): Map<string, string[]> {
    const graph = new Map<string, string[]>();
    
    for (const task of tasks) {
      graph.set(task.id, task.dependencies);
    }
    
    return graph;
  }

  private calculateEarlyTimes(
    tasks: Task[],
    dependencyGraph: Map<string, string[]>,
    projectStart: Date
  ): Map<string, { start: Date; finish: Date }> {
    const earlyTimes = new Map<string, { start: Date; finish: Date }>();
    const visited = new Set<string>();
    
    const calculateEarly = (taskId: string): { start: Date; finish: Date } => {
      if (visited.has(taskId)) {
        return earlyTimes.get(taskId)!;
      }
      
      visited.add(taskId);
      const task = tasks.find(t => t.id === taskId)!;
      const dependencies = dependencyGraph.get(taskId) || [];
      
      let earliestStart = projectStart;
      
      // Find latest finish time of all dependencies
      for (const depId of dependencies) {
        const depTimes = calculateEarly(depId);
        if (depTimes.finish > earliestStart) {
          earliestStart = depTimes.finish;
        }
      }
      
      const duration = this.getTaskDuration(task);
      const finish = this.addWorkingDays(earliestStart, duration);
      
      const times = { start: earliestStart, finish };
      earlyTimes.set(taskId, times);
      
      return times;
    };
    
    for (const task of tasks) {
      calculateEarly(task.id);
    }
    
    return earlyTimes;
  }

  private calculateLateTimes(
    tasks: Task[],
    dependencyGraph: Map<string, string[]>,
    earlyTimes: Map<string, { start: Date; finish: Date }>
  ): Map<string, { start: Date; finish: Date }> {
    const lateTimes = new Map<string, { start: Date; finish: Date }>();
    const visited = new Set<string>();
    
    // Find project end date
    const projectEnd = new Date(Math.max(...Array.from(earlyTimes.values()).map(t => t.finish.getTime())));
    
    // Build reverse dependency graph
    const reverseDeps = new Map<string, string[]>();
    for (const [taskId, deps] of dependencyGraph) {
      for (const depId of deps) {
        if (!reverseDeps.has(depId)) {
          reverseDeps.set(depId, []);
        }
        reverseDeps.get(depId)!.push(taskId);
      }
    }
    
    const calculateLate = (taskId: string): { start: Date; finish: Date } => {
      if (visited.has(taskId)) {
        return lateTimes.get(taskId)!;
      }
      
      visited.add(taskId);
      const task = tasks.find(t => t.id === taskId)!;
      const dependents = reverseDeps.get(taskId) || [];
      
      let latestFinish = projectEnd;
      
      // Find earliest start time of all dependents
      for (const depId of dependents) {
        const depTimes = calculateLate(depId);
        if (depTimes.start < latestFinish) {
          latestFinish = depTimes.start;
        }
      }
      
      const duration = this.getTaskDuration(task);
      const start = this.subtractWorkingDays(latestFinish, duration);
      
      const times = { start, finish: latestFinish };
      lateTimes.set(taskId, times);
      
      return times;
    };
    
    for (const task of tasks) {
      calculateLate(task.id);
    }
    
    return lateTimes;
  }

  private findCriticalPath(
    tasks: Task[],
    earlyTimes: Map<string, { start: Date; finish: Date }>,
    lateTimes: Map<string, { start: Date; finish: Date }>
  ): string[] {
    const criticalTasks: string[] = [];
    
    for (const task of tasks) {
      const early = earlyTimes.get(task.id)!;
      const late = lateTimes.get(task.id)!;
      
      // Task is critical if early start equals late start (no slack)
      if (early.start.getTime() === late.start.getTime()) {
        criticalTasks.push(task.id);
      }
    }
    
    return criticalTasks;
  }

  private calculateSlack(
    taskId: string,
    earlyTimes: Map<string, { start: Date; finish: Date }>,
    lateTimes: Map<string, { start: Date; finish: Date }>
  ): number {
    const early = earlyTimes.get(taskId)!;
    const late = lateTimes.get(taskId)!;
    
    return this.calculateDuration(early.start, late.start);
  }

  private optimizeResourceAllocation(
    tasks: ScheduledTask[],
    constraints?: { resourceLimits?: Map<string, number> }
  ): ScheduledTask[] {
    // Resource leveling algorithm would go here
    // For now, return tasks as-is
    return tasks;
  }

  private calculateProjectEnd(tasks: ScheduledTask[]): Date {
    const endDates = tasks
      .map(task => task.scheduledEnd)
      .filter(Boolean) as Date[];
    
    return new Date(Math.max(...endDates.map(d => d.getTime())));
  }

  private generateMilestones(tasks: ScheduledTask[]): Milestone[] {
    const milestones: Milestone[] = [];
    
    // Create milestone for project completion
    const projectEnd = this.calculateProjectEnd(tasks);
    milestones.push({
      id: 'project_completion',
      name: 'Project Completion',
      date: projectEnd,
      taskIds: tasks.filter(t => t.criticalPath).map(t => t.id),
      description: 'All critical path tasks completed'
    });
    
    return milestones;
  }

  private getTaskDuration(task: Task): number {
    if (task.estimatedHours) {
      return Math.ceil(task.estimatedHours / this.workingHoursPerDay);
    }
    return 1; // Default to 1 day
  }

  private getTaskProgress(task: Task): number {
    switch (task.state) {
      case TaskState.NOT_STARTED: return 0;
      case TaskState.IN_PROGRESS: return 50;
      case TaskState.REVIEW: return 90;
      case TaskState.COMPLETE: return 100;
      default: return 0;
    }
  }

  private calculateDuration(start: Date, end: Date): number {
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private addWorkingDays(date: Date, days: number): Date {
    const result = new Date(date);
    let addedDays = 0;
    
    while (addedDays < days) {
      result.setDate(result.getDate() + 1);
      
      // Skip weekends
      if (result.getDay() !== 0 && result.getDay() !== 6) {
        addedDays++;
      }
    }
    
    return result;
  }

  private subtractWorkingDays(date: Date, days: number): Date {
    const result = new Date(date);
    let subtractedDays = 0;
    
    while (subtractedDays < days) {
      result.setDate(result.getDate() - 1);
      
      // Skip weekends
      if (result.getDay() !== 0 && result.getDay() !== 6) {
        subtractedDays++;
      }
    }
    
    return result;
  }

  private calculateAvailableHours(taskAllocations: Array<{ startDate: Date; endDate: Date }>): number {
    if (taskAllocations.length === 0) return 0;
    
    const earliestStart = new Date(Math.min(...taskAllocations.map(t => t.startDate.getTime())));
    const latestEnd = new Date(Math.max(...taskAllocations.map(t => t.endDate.getTime())));
    
    const totalDays = this.calculateDuration(earliestStart, latestEnd);
    const workingDays = Math.floor(totalDays * (this.workingDaysPerWeek / 7));
    
    return workingDays * this.workingHoursPerDay;
  }

  private resolveResourceConflict(schedule: ProjectSchedule, conflict: SchedulingConflict): ProjectSchedule {
    // Resource leveling logic would go here
    return schedule;
  }

  private resolveDependencyConflict(schedule: ProjectSchedule, conflict: SchedulingConflict): ProjectSchedule {
    // Dependency resolution logic would go here
    return schedule;
  }

  private resolveDeadlineConflict(schedule: ProjectSchedule, conflict: SchedulingConflict): ProjectSchedule {
    // Deadline resolution logic would go here
    return schedule;
  }
}
