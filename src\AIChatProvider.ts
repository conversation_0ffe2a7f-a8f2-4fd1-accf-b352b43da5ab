import * as vscode from 'vscode';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { MemoryManager } from './managers/memoryManager';
import { ProcessManager } from './managers/processManager';
import { GithubManager } from './managers/githubManager';
import { DiagnosticsManager } from './managers/diagnosticsManager';
import { WebManager } from './managers/webManager';
import { CodeIndexer } from './codeIndexer';
import { CodeStructureAnalyzer } from './codeStructureAnalyzer';
import { Message } from './models/message';
import { Conversation } from './models/conversation';
import { ModeManager, ChatMode } from './managers/modeManager';

export class AIChatProvider {
  private apiKey: string;
  private defaultModel: string;
  private maxContextLength: number;

  private currentMode: ChatMode;

  /**
   * Sets the current chat mode
   * @param mode The mode to set
   */
  public setMode(mode: ChatMode): void {
    console.log(`[AIChatProvider] Setting mode to: ${mode}`);
    this.currentMode = mode;
  }

  constructor(
    private context: vscode.ExtensionContext,
    private memoryManager: MemoryManager,
    private processManager: ProcessManager,
    private githubManager: GithubManager,
    private diagnosticsManager: DiagnosticsManager,
    private webManager: WebManager,
    private codeIndexer: CodeIndexer,
    private codeStructureAnalyzer: CodeStructureAnalyzer,
    private modeManager: ModeManager
  ) {
    this.currentMode = this.modeManager.getCurrentMode();
    
    // Listen for mode changes
    this.context.subscriptions.push(
      vscode.workspace.onDidChangeConfiguration(e => {
        if (e.affectsConfiguration('vidyadhara.chatMode')) {
          this.currentMode = this.modeManager.getCurrentMode();
        }
      })
    );
    const config = vscode.workspace.getConfiguration('vidyadhara');
    this.apiKey = config.get<string>('openRouterApiKey') || '';
    this.defaultModel = config.get<string>('defaultModel') || 'anthropic/claude-3-opus-20240229';
    this.maxContextLength = config.get<number>('maxContextLength') || 100000;

    // Listen for configuration changes
    vscode.workspace.onDidChangeConfiguration(e => {
      if (e.affectsConfiguration('vidyadhara')) {
        const newConfig = vscode.workspace.getConfiguration('vidyadhara');
        this.apiKey = newConfig.get<string>('openRouterApiKey') || '';
        this.defaultModel = newConfig.get<string>('defaultModel') || 'anthropic/claude-3-opus-20240229';
        this.maxContextLength = newConfig.get<number>('maxContextLength') || 100000;
      }
    });
  }

  public async sendMessage(
    message: string,
    conversationId: string,
    onUpdate: (content: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: string) => void,
    modeOverride?: ChatMode
  ): Promise<void> {
    // Use the provided mode override or fall back to the current mode
    const mode = modeOverride || this.currentMode;
    if (!this.apiKey) {
      vscode.window.showErrorMessage('OpenRouter API key not set. Please set it in the extension settings.');
      onError('OpenRouter API key not set');
      return;
    }

    try {
      // Get conversation history
      const conversation = await this.memoryManager.getConversation(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      // Add user message to conversation
      const userMessage: Message = {
        id: uuidv4(),
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      };

      await this.memoryManager.addMessageToConversation(conversationId, userMessage);

      // Prepare context for the AI
      const context = await this.prepareContext(message);

      // Prepare messages for the API
      const messages = this.prepareMessages(conversation, context);

      // Call OpenRouter API with streaming
      await this.callOpenRouterAPI(
        messages,
        conversationId,
        onUpdate,
        onComplete,
        onError
      );
    } catch (error) {
      console.error('Error sending message:', error);
      onError(`Error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async prepareContext(message: string): Promise<string> {
    // Get relevant code from the codebase
    const codeContext = await this.codeIndexer.getRelevantCode(message);

    // Get code structure information if relevant
    let codeStructureContext = '';
    if (message.toLowerCase().includes('structure') ||
      message.toLowerCase().includes('class') ||
      message.toLowerCase().includes('interface') ||
      message.toLowerCase().includes('function') ||
      message.toLowerCase().includes('method') ||
      message.toLowerCase().includes('relationship') ||
      message.toLowerCase().includes('inheritance') ||
      message.toLowerCase().includes('extends') ||
      message.toLowerCase().includes('implements')) {
      try {
        // Find related symbols based on the message
        const relatedSymbols = await this.codeStructureAnalyzer.findRelatedSymbols(message, 5);

        if (relatedSymbols.length > 0) {
          codeStructureContext = '# Code Structure Information\n\n';

          for (const symbol of relatedSymbols) {
            codeStructureContext += `## ${symbol.kind.charAt(0).toUpperCase() + symbol.kind.slice(1)}: ${symbol.name}\n\n`;

            if (symbol.documentation) {
              codeStructureContext += `${symbol.documentation}\n\n`;
            }

            codeStructureContext += `File: ${vscode.workspace.asRelativePath(symbol.filePath)}\n\n`;

            if (symbol.signature) {
              codeStructureContext += `\`\`\`typescript\n${symbol.signature}\n\`\`\`\n\n`;
            }

            if (symbol.extends && symbol.extends.length > 0) {
              codeStructureContext += `Extends: ${symbol.extends.join(', ')}\n\n`;
            }

            if (symbol.implements && symbol.implements.length > 0) {
              codeStructureContext += `Implements: ${symbol.implements.join(', ')}\n\n`;
            }

            if (symbol.properties && symbol.properties.length > 0) {
              codeStructureContext += 'Properties:\n\n';
              for (const prop of symbol.properties) {
                codeStructureContext += `- ${prop}\n`;
              }
              codeStructureContext += '\n';
            }

            if (symbol.methods && symbol.methods.length > 0) {
              codeStructureContext += 'Methods:\n\n';
              for (const method of symbol.methods) {
                codeStructureContext += `- ${method}\n`;
              }
              codeStructureContext += '\n';
            }

            // Get relationships for this symbol
            const relationships = this.codeStructureAnalyzer.getSymbolRelationships(symbol.name);
            if (relationships.length > 0) {
              codeStructureContext += 'Relationships:\n\n';
              for (const rel of relationships) {
                codeStructureContext += `- ${rel.type} ${rel.targetSymbol}\n`;
              }
              codeStructureContext += '\n';
            }
          }
        }
      } catch (error) {
        console.error('Error getting code structure information:', error);
      }
    }

    // Get diagnostics if relevant
    const diagnostics = await this.diagnosticsManager.getRelevantDiagnostics(message);

    // Get GitHub context if relevant
    const githubContext = await this.githubManager.getRelevantGithubInfo(message);

    // Get web search results if the message contains a question that might benefit from web search
    let webSearchResults = '';
    if (message.toLowerCase().includes('search') ||
      message.toLowerCase().includes('find information') ||
      message.toLowerCase().includes('look up') ||
      message.includes('?')) {
      try {
        // Extract a search query from the message
        const searchQuery = this.extractSearchQuery(message);
        if (searchQuery) {
          webSearchResults = await this.webManager.searchAndFormatResults(searchQuery, 3);
        }
      } catch (error) {
        console.error('Error performing web search:', error);
      }
    }

    // Combine all context
    let context = '';

    if (codeContext) {
      context += `# Relevant Code\n${codeContext}\n\n`;
    }

    if (codeStructureContext) {
      context += `${codeStructureContext}\n\n`;
    }

    if (diagnostics) {
      context += `# Diagnostics\n${diagnostics}\n\n`;
    }

    if (githubContext) {
      context += `# GitHub Context\n${githubContext}\n\n`;
    }

    if (webSearchResults) {
      context += `# Web Search Results\n${webSearchResults}\n\n`;
    }

    return context;
  }

  private prepareMessages(conversation: Conversation, context: string): any[] {
    const messages: any[] = [];

    // System message with context
    messages.push({
      role: 'system',
      content: `You are Vidyadhara, an advanced AI coding assistant based on the Claude model. You have access to the user's codebase and can help with coding tasks, debugging, and providing explanations.

Current date: ${new Date().toISOString()}

${context}

You have access to various tools to help the user:
1. Code editing tools
2. Process execution tools
3. GitHub integration tools
4. Diagnostics tools
5. Web search tools
6. Code structure analysis tools

Always provide helpful, accurate responses and suggest solutions when appropriate.`
    });

    // Add conversation history (limited by maxContextLength)
    let currentLength = messages[0].content.length;
    const historyMessages = [];

    // Add messages in reverse order until we hit the context limit
    for (let i = conversation.messages.length - 1; i >= 0; i--) {
      const msg = conversation.messages[i];
      const msgLength = msg.content.length;

      if (currentLength + msgLength < this.maxContextLength) {
        historyMessages.unshift({
          role: msg.role,
          content: msg.content
        });
        currentLength += msgLength;
      } else {
        break;
      }
    }

    messages.push(...historyMessages);

    return messages;
  }

  private async callOpenRouterAPI(
    messages: any[],
    conversationId: string,
    onUpdate: (content: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const controller = new AbortController();
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': 'https://vidyadhara.dev',
          'X-Title': 'Vidyadhara'
        },
        body: JSON.stringify({
          model: this.defaultModel,
          messages: messages,
          stream: true,
          max_tokens: 4000
        }),
        signal: controller.signal
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let fullResponse = '';
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const text = decoder.decode(value, { stream: true });
          buffer += text;

          // Process complete lines
          let lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') continue;
            if (line.trim() === 'data: [DONE]') continue;

            try {
              const data = JSON.parse(line.replace(/^data: /, ''));
              if (data.choices && data.choices[0]?.delta?.content) {
                const content = data.choices[0].delta.content;
                fullResponse += content;
                onUpdate(fullResponse);
              }
            } catch (e) {
              console.error('Error parsing streaming response:', e, line);
            }
          }
        }

        // Save assistant message to conversation
        const assistantMessage: Message = {
          id: uuidv4(),
          role: 'assistant',
          content: fullResponse,
          timestamp: new Date().toISOString()
        };

        await this.memoryManager.addMessageToConversation(conversationId, assistantMessage);

        onComplete(fullResponse);
      } catch (error) {
        if (error instanceof Error) {
          console.error('Stream reading error:', error);
          onError(`Stream error: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('Error calling OpenRouter API:', error);
      onError(`Error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async createNewConversation(title: string = 'New Conversation'): Promise<string> {
    return this.memoryManager.createConversation(title);
  }

  /**
   * Extracts a search query from a user message.
   * This is a simple implementation and could be improved with more sophisticated NLP.
   * @param message User message
   * @returns Extracted search query or null if no query could be extracted
   */
  private extractSearchQuery(message: string): string | null {
    // Look for common search patterns
    const searchPatterns = [
      /search for (.+?)(?:\.|$)/i,
      /find information (?:about|on) (.+?)(?:\.|$)/i,
      /look up (.+?)(?:\.|$)/i,
      /what is (.+?)(?:\?|$)/i,
      /how (?:do|does|to) (.+?)(?:\?|$)/i,
      /who (?:is|are) (.+?)(?:\?|$)/i,
      /when (?:is|was|will) (.+?)(?:\?|$)/i,
      /where (?:is|are) (.+?)(?:\?|$)/i,
      /why (?:is|are|does) (.+?)(?:\?|$)/i
    ];

    for (const pattern of searchPatterns) {
      const match = message.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    // If no pattern matches but the message contains a question mark,
    // use the whole message as the query (limited to 100 chars)
    if (message.includes('?')) {
      return message.substring(0, 100);
    }

    return null;
  }
}
